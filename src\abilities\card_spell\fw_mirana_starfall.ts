
import { modifier_fw_mirana_starfall } from "../../modifiers/abilities/card_spell/modifier_fw_mirana_starfall";
import { BaseAbility, registerAbility } from "../../utils/dota_ts_adapter";

export class fw_mirana_starfall extends BaseAbility {
    
    spell:SpellInfoKV;
    GetAOERadius(): number {
        return this.spell.AOERadius
    }

    OnSpellStart(): void {
        let duration = this.GetSpecialValueFor("duration")
        let damage_interval = this.GetSpecialValueFor("damage_interval")

        let hero = this.GetCaster()
        let ab = this
        let team = hero.GetTeam()
        let tarPos = this.GetCursorPosition()
        let radius = this.GetAOERadius()
        let tarTeam = this.GetAbilityTargetTeam()
        let tarFlag = this.GetAbilityTargetFlags()
        let fwTargetType = this.spell.FWTargetType
        let e = GameRules.SoundUtils.getSoundEntity(tarPos)

        let parPre = ParticleManager.CreateParticle("particles/spell/mirana_starfall/pre/mirana_starstorm.vpcf", ParticleAttachment_t.PATTACH_WORLDORIGIN, undefined)
        ParticleManager.SetParticleControl(parPre, 0, tarPos)
        ParticleManager.SetParticleControl(parPre, 1, Vector(radius,0,8))
        e.EmitSound("Fw.Cards.Spell.starfall.cast")
        GameRules.FastWarSpell.startIntervalSpell(0,duration,damage_interval,
        ()=>{

        },()=>{
            let par = ParticleManager.CreateParticle("particles/spell/mirana_starfall/pre_using_3.vpcf", ParticleAttachment_t.PATTACH_WORLDORIGIN, undefined)
            ParticleManager.SetParticleControl(par, 0, tarPos)
            ParticleManager.SetParticleControl(par, 1, Vector(radius,0,0))
            let tars = FindUnitsInRadius(
                team,
                tarPos,
                undefined,
                radius,
                tarTeam,
                1 + 2 + 4,
                tarFlag,
                0,
                false,
            )
            for (const unit of tars) {
                if (unit.fastWarUnitType != undefined && (fwTargetType == GoFastWarAIUnitTypeEnum.ALL || unit.fastWarUnitType == fwTargetType)) {
                    unit.AddNewModifier(hero, ab, modifier_fw_mirana_starfall.name, {duration:0.5}) 
                }
            }
        },()=>{
            ParticleManager.DestroyParticle(parPre, false)
            e.StopSound("Fw.Cards.Spell.starfall.cast")
            GameRules.SoundUtils.backSoundEntity(e)
        })
    }

    Spawn(): void {
        this.spell = GameRules.KVUtils.getSpellInfo("fw_mirana_starfall")
    }
    
    Precache(context: CScriptPrecacheContext): void {
        PrecacheResource("particle","particles/spell/mirana_starfall/pre_using_3.vpcf",context)
        PrecacheResource("particle","particles/units/heroes/hero_mirana/mirana_starfall_attack.vpcf",context)
        PrecacheResource("particle","particles/spell/mirana_starfall/pre/mirana_starstorm.vpcf",context)
    }
}

