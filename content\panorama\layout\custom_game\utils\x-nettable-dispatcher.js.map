{"version": 3, "file": "x-nettable-dispatcher.js", "sourceRoot": "", "sources": ["../../../../../src/panorama/utils/x-nettable-dispatcher.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAC7C,OAAO,iCAAiC,CAAC;AAsBzC;;;;;;;GAOG;AACH,MAAM,UAAU,OAAO,CAAC,IAAS,EAAE,IAAS;IAC1C,iBAAiB;IACjB,IAAI,OAAO,IAAI,KAAK,OAAO,IAAI,EAAE;QAC/B,OAAO,KAAK,CAAC;KACd;IAED,SAAS;IACT,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QAC5B,YAAY;QACZ,IAAI,IAAI,KAAK,IAAI,EAAE;YACjB,OAAO,IAAI,KAAK,IAAI,CAAC;SACtB;QAED,OAAO;QACP,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACvB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,EAAE;gBACvD,OAAO,KAAK,CAAC;aACd;YAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACpC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;oBAC9B,OAAO,KAAK,CAAC;iBACd;aACF;YACD,OAAO,IAAI,CAAC;SACb;QAED,SAAS;QACT,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACvB,OAAO,KAAK,CAAC;SACd;QAED,YAAY;QACZ,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnC,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEnC,IAAI,QAAQ,CAAC,MAAM,KAAK,QAAQ,CAAC,MAAM,EAAE;YACvC,OAAO,KAAK,CAAC;SACd;QAED,KAAK,MAAM,GAAG,IAAI,QAAQ,EAAE;YAC1B,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;YAC3B,MAAM,SAAS,GAAI,IAA4B,CAAC,MAAM,CAAC,CAAC;YACxD,MAAM,SAAS,GAAI,IAA4B,CAAC,MAAM,CAAC,CAAC;YACxD,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE;gBAClE,OAAO,KAAK,CAAC;aACd;SACF;QAED,OAAO,IAAI,CAAC;KACb;IAED,SAAS;IACT,OAAO,IAAI,KAAK,IAAI,CAAC;AACvB,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,QAAQ,CAAC,UAAkB,EAAE,GAAW,EAAE,OAAY;IACpE,IAAI;QACF,WAAW;QACX,MAAM,YAAY,GAAG,MAAM,CAAC,cAAc,EAAwB,CAAC;QAEnE,IAAI,CAAC,YAAY,CAAC,oBAAoB,EAAE;YACtC,YAAY,CAAC,oBAAoB,GAAG,EAAE,CAAC;SACxC;QAED,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,UAAU,CAAC,EAAE;YAClD,YAAY,CAAC,oBAAoB,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;SACpD;QAED,SAAS;QACT,MAAM,IAAI,GAAG,YAAY,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC;QAEhE,iBAAiB;QACjB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE;YAC3B,OAAO;YACP,YAAY,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC;YAE7D,SAAS;YACT,MAAM,SAAS,GAAiB;gBAC9B,UAAU;gBACV,GAAG;gBACH,OAAO;aACR,CAAC;YAEF,SAAS;YACT,iEAAiE;YACjE,cAAc,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;SAC1C;KACF;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,GAAG,CAAC,+BAA+B,UAAU,OAAO,GAAG,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC;KACzF;AACH,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,cAAc,CAAC,UAAkB,EAAE,GAAW;IAC5D,MAAM,YAAY,GAAG,MAAM,CAAC,cAAc,EAAwB,CAAC;IAEnE,IAAI,CAAC,YAAY,CAAC,oBAAoB;QAClC,CAAC,YAAY,CAAC,oBAAoB,CAAC,UAAU,CAAC,EAAE;QAClD,OAAO,SAAS,CAAC;KAClB;IAED,OAAO,YAAY,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC;AAC5D,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,cAAc,CAAC,UAAkB;IAC/C,MAAM,YAAY,GAAG,MAAM,CAAC,cAAc,EAAwB,CAAC;IAEnE,IAAI,CAAC,YAAY,CAAC,oBAAoB,EAAE;QACtC,OAAO,SAAS,CAAC;KAClB;IAED,OAAO,YAAY,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;AACvD,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,eAAe,CAAC,UAAkB;IAChD,MAAM,YAAY,GAAG,MAAM,CAAC,cAAc,EAAwB,CAAC;IAEnE,IAAI,YAAY,CAAC,oBAAoB;QACjC,YAAY,CAAC,oBAAoB,CAAC,UAAU,CAAC,EAAE;QACjD,OAAO,YAAY,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;KACtD;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,aAAa;IAC3B,MAAM,YAAY,GAAG,MAAM,CAAC,cAAc,EAAwB,CAAC;IACnE,YAAY,CAAC,oBAAoB,GAAG,EAAE,CAAC;AACzC,CAAC;AAED,sBAAsB;AACtB,IAAI,OAAO,eAAe,KAAK,WAAW,EAAE;IAC1C,mBAAmB;IAClB,eAAuB,CAAC,yBAAyB,CAAC,EAAE,EAAE,CAAC,SAAiB,EAAE,GAAW,EAAE,KAAU,EAAE,EAAE;QACpG,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;IAClC,CAAC,CAAC,CAAC;CACJ;AAED,eAAe;IACb,OAAO;IACP,QAAQ;IACR,cAAc;IACd,cAAc;IACd,eAAe;IACf,aAAa;CACd,CAAC"}