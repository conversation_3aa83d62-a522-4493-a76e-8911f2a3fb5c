// 游戏数据类型定义
// 基于现有JSON数据结构生成

// 卡牌类型相关
export type CardType = 'unit' | 'spell';
export type CardUIType = 'creep' | 'hero' | 'building' | 'spell';
export type LayType = 'lay_row';

// 单位卡牌信息
export interface UnitCardInfo {
  Delay: number;
  LayType: LayType;
  CoverTemplate: number;
  LayGroupNum: string | number;
  Template1: string;
  Num1: number;
  Template2?: string;
  Num2?: number;
}

// 法术卡牌信息
export interface SpellCardInfo {
  UICardBack?: number;
  SpellTemplate?: string;
}

// 额外数据
export interface ExtraData {
  HandCardCooldown?: number[];
  HandCardCost?: number[];
}

// 机器人AI数据
export interface BotCardAI {
  CardTag: string;
}

// 卡牌定义
export interface Card {
  Name: string;
  ChName: string;
  LayCost: number;
  CardType: CardType;
  CardUIType: CardUIType;
  Enable: number;
  EnableForDeckCards: number;
  UsingAreaLimit: number;
  UnitCardInfo: UnitCardInfo;
  SpellCardInfo: SpellCardInfo;
  ExtraData: ExtraData;
  BotCardAI: BotCardAI;
}

// 卡牌集合
export interface CardCollection {
  [cardId: string]: Card;
}

// 英雄/单位模板相关类型
export type UnitType = 'building' | 'ground';
export type AttackCapabilities = 
  | 'DOTA_UNIT_CAP_RANGED_ATTACK' 
  | 'DOTA_UNIT_CAP_MELEE_ATTACK' 
  | 'DOTA_UNIT_CAP_NO_ATTACK';
export type MovementCapabilities = 
  | 'DOTA_UNIT_CAP_MOVE_NONE' 
  | 'DOTA_UNIT_CAP_MOVE_GROUND';
export type BoundsHullName = 
  | 'DOTA_HULL_SIZE_TOWER' 
  | 'DOTA_HULL_SIZE_BUILDING' 
  | 'DOTA_HULL_SIZE_REGULAR';

// 可穿戴物品
export interface AttachWearable {
  ItemDef: number;
}

export interface AttachWearables {
  [slotId: string]: AttachWearable;
}

// 生物配置
export interface Creature {
  AttachWearables: AttachWearables;
}

// 单位AI配置
export interface UnitAI {
  AutoNum: string;
  SpellAI: {
    [abilitySlot: string]: string;
  };
}

// 英雄/单位模板
export interface HeroTemplate {
  Level: number;
  UnitType: UnitType;
  AttackType: number;
  AttackDamageMax: number;
  AttackDamageMin: number;
  AttackCapabilities: AttackCapabilities;
  ProjectileModel?: string;
  ProjectileSpeed?: number;
  AttackAnimationPoint: number;
  AttackRange: number;
  AttackRate: number;
  AttackAcquisitionRange: number;
  BaseAttackSpeed: number;
  BaseClass: string;
  ArmorPhysical: number;
  MagicalResistance: number;
  ConsideredHero: number;
  BoundsHullName: BoundsHullName;
  HealthBarOffset: number;
  HealthBarPips: number;
  AttackHealthPips: number;
  Model: string;
  ModelScale: number;
  SoundSet: string;
  MinimapIcon?: string;
  MovementCapabilities: MovementCapabilities;
  MovementSpeed: number;
  MovementTurnRate: number;
  RingRadius: number;
  StatusHealth: number;
  StatusHealthRegen: number;
  StatusMana: number;
  StatusManaRegen: number;
  VisionDaytimeRange: number;
  VisionNighttimeRange: number;
  Ability1: string | number;
  Ability2: string | number;
  Ability3: string | number;
  Ability4: string | number;
  Ability5: string | number;
  Ability6: string | number;
  Ability7: string | number;
  Ability8: string | number;
  Ability9: string | number;
  Ability10: string | number;
  Ability11: string | number;
  Ability12: string | number;
  Ability13: string | number;
  Ability14: string | number;
  Ability15: string | number;
  Ability16: string | number;
  Ability17: string | number;
  Ability18: string | number;
  Creature: Creature;
  UnitAI: UnitAI;
  AbilityNum: number;
  UnitDuration: number;
  ParEntUnit: string | number;
  PreUsingSound: string | number;
  EntranceSound: string | number;
  DeathSound: string | number;
  SpawnActivityModifiers?: string;
}

// 英雄模板集合
export interface HeroTemplateCollection {
  [templateId: string]: HeroTemplate;
}

// 玩家法术
export interface PlayerSpell {
  Index: number;
  HeroImage: number;
  SpellName: string;
  CardIndex?: number | string;
  UsingPar?: string;
}

// 玩家法术集合
export interface PlayerSpellCollection {
  [spellId: string]: PlayerSpell;
}

// 游戏状态相关
export interface GameState {
  currentTurn: number;
  playerMana: number;
  playerHealth: number;
  handCards: Card[];
  deckCards: Card[];
  boardUnits: any[]; // 待定义具体类型
}

// UI 相关类型
export interface UIState {
  selectedCard?: Card;
  hoveredCard?: Card;
  draggedCard?: Card;
  isCardSelectionMode: boolean;
  isGamePaused: boolean;
}

// 网络通信相关
export interface NetTableData {
  [tableName: string]: {
    [key: string]: any;
  };
}

// 事件类型
export type GameEventType = 
  | 'card_played'
  | 'unit_spawned'
  | 'unit_died'
  | 'spell_cast'
  | 'turn_changed'
  | 'game_ended';

export interface GameEvent {
  type: GameEventType;
  data: any;
  timestamp: number;
} 