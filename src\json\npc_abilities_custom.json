{"fw_unit_str_hero_health": {"BaseClass": "ability_lua", "ScriptFile": "abilities/unit_innate/fw_unit_str_hero_health.lua", "AbilityValues": {"health": 40}, "AbilityTextureName": "innate", "AbilityBehavior": "DOTA_ABILITY_BEHAVIOR_PASSIVE", "Innate": 1, "IsBreakable": 0, "SpellDispellableType": "SPELL_DISPELLABLE_NO"}, "fw_unit_str_hero": {"BaseClass": "ability_lua", "ScriptFile": "abilities/unit_innate/fw_unit_str_hero.lua", "AbilityValues": {"damage": 10}, "AbilityTextureName": "innate", "AbilityBehavior": "DOTA_ABILITY_BEHAVIOR_PASSIVE", "Innate": 1, "IsBreakable": 0, "SpellDispellableType": "SPELL_DISPELLABLE_NO"}, "fw_unit_int_hero": {"BaseClass": "ability_lua", "ScriptFile": "abilities/unit_innate/fw_unit_int_hero.lua", "AbilityValues": {"attack_speed": 8}, "AbilityTextureName": "innate", "AbilityBehavior": "DOTA_ABILITY_BEHAVIOR_PASSIVE", "Innate": 1, "IsBreakable": 0, "SpellDispellableType": "SPELL_DISPELLABLE_NO"}, "fw_unit_agi_hero": {"BaseClass": "ability_lua", "ScriptFile": "abilities/unit_innate/fw_unit_agi_hero.lua", "AbilityValues": {"cooldown": 8, "max_cd": 48, "cost": 8, "max_cost": 48}, "AbilityTextureName": "innate", "AbilityBehavior": "DOTA_ABILITY_BEHAVIOR_PASSIVE", "Innate": 1, "IsBreakable": 0, "SpellDispellableType": "SPELL_DISPELLABLE_NO"}, "fw_unit_luna_hero": {"BaseClass": "ability_lua", "ScriptFile": "abilities/unit_innate/fw_unit_luna_hero.lua", "AbilityValues": {"extra_attack": 4}, "AbilityTextureName": "innate", "AbilityBehavior": "DOTA_ABILITY_BEHAVIOR_PASSIVE", "Innate": 1, "IsBreakable": 0, "SpellDispellableType": "SPELL_DISPELLABLE_NO"}, "fw_unit_tiny_hero": {"BaseClass": "ability_lua", "ScriptFile": "abilities/unit_innate/fw_unit_tiny_hero.lua", "AbilityValues": {"extra_num": 2}, "AbilityTextureName": "innate", "AbilityBehavior": "DOTA_ABILITY_BEHAVIOR_PASSIVE", "Innate": 1, "IsBreakable": 0, "SpellDispellableType": "SPELL_DISPELLABLE_NO"}, "fw_unit_shaman_hero": {"BaseClass": "ability_lua", "ScriptFile": "abilities/unit_innate/fw_unit_shaman_hero.lua", "AbilityValues": {"extra_range": 15, "extra_dsub": 10, "max_dsub": 50}, "AbilityTextureName": "innate", "AbilityBehavior": "DOTA_ABILITY_BEHAVIOR_PASSIVE", "Innate": 1, "IsBreakable": 0, "SpellDispellableType": "SPELL_DISPELLABLE_NO"}, "fw_unit_delay_hero": {"BaseClass": "ability_lua", "ScriptFile": "abilities/unit_innate/fw_unit_delay_hero.lua", "AbilityValues": {"sub_delay": 0.1, "max_sub": 0.5}, "AbilityTextureName": "innate", "AbilityBehavior": "DOTA_ABILITY_BEHAVIOR_PASSIVE", "Innate": 1, "IsBreakable": 0, "SpellDispellableType": "SPELL_DISPELLABLE_NO"}, "creep_siege": {"AbilityValues": {"bonus_building_damage": 150, "incoming_hero_damage_penalty": 0, "incoming_basic_damage_penalty": 0, "incoming_controlled_unit_penalty": 0}, "MaxLevel": 1}, "axe_counter_helix": {"AbilityValues": {"damage": 75, "radius": 275, "AbilityCooldown": 0.3, "trigger_attacks": 3}, "MaxLevel": 1, "AbilityUnitDamageType": "DAMAGE_TYPE_PHYSICAL"}, "axe_berserkers_call": {"AbilityValues": {"radius": 350, "bonus_armor": 12, "duration": 2.2, "bonus_attack_speed": 0}, "MaxLevel": 1, "AbilityCooldown": 15, "AbilityManaCost": 0}, "juggernaut_blade_dance": {"AbilityValues": {"blade_dance_crit_chance": 70, "blade_dance_crit_mult": 150}, "MaxLevel": 1}, "abaddon_aphotic_shield": {"AbilityValues": {"AbilityCooldown": 8, "duration": 8, "radius": 450, "damage_absorb": 120}, "MaxLevel": 1, "AbilityCooldown": 8, "AbilityManaCost": 30, "AbilityCastRange": 550}, "ursa_fury_swipes": {"AbilityValues": {"bonus_reset_time": 99, "damage_per_stack": 20, "attack_speed": 250, "bonus_reset_time_roshan": 0}, "MaxLevel": 1}, "weaver_geminate_attack": {"AbilityValues": {"delay": 0.25, "extra_attack": 1, "bonus_damage": 20}, "MaxLevel": 1, "AbilityCooldown": 2}, "huskar_burning_spear": {"AbilityValues": {"health_cost": 4, "max_health_cost": 0, "burn_damage": 15, "burn_damage_max_pct": 0, "duration": 9}, "MaxLevel": 1}, "huskar_berserkers_blood": {"AbilityValues": {"maximum_attack_speed": 270, "maximum_health_regen": 25, "hp_threshold_max": 25, "maximum_magic_resist": 15}, "MaxLevel": 1}, "skywrath_mage_concussive_shot": {"AbilityValues": {"launch_radius": 1000, "slow_radius": 200, "speed": 800, "damage": 90, "slow_duration": 4, "movement_speed_pct": 40, "shot_vision": 0, "vision_duration": 0}, "MaxLevel": 1, "AbilityCooldown": 6, "AbilityManaCost": 30, "AbilityCastRange": 1000}, "dark_seer_ion_shell": {"AbilityValues": {"radius": 275, "damage_per_second": 50, "duration": 9, "tick_interval": 0.2, "AbilityCharges": 0, "bonus_health": 100}, "MaxLevel": 1, "AbilityCooldown": 6, "AbilityManaCost": 30, "AbilityCastRange": 500}, "primal_beast_pulverize": {"AbilityValues": {"splash_radius": 600, "interval": 0.75, "ministun": 0.2, "cast_range": 200, "animation_rate": 1.5, "damage": 100, "bonus_damage_per_hit": 0, "channel_time": 2.3}, "MaxLevel": 1, "AbilityCooldown": 10, "AbilityManaCost": 50}, "tiny_tree_grab": {"AbilityValues": {"attack_count": 4, "bonus_damage": 150, "bonus_damage_buildings": 0, "attack_range": 150, "splash_width": 200, "splash_range": 400, "splash_pct": 80, "bat_increase": 0, "speed_reduction": 0}, "MaxLevel": 1, "AbilityCooldown": 20, "AbilityManaCost": 50, "AbilityCastRange": 0, "LinkedAbility": "tiny_tree_grab"}, "sniper_headshot": {"AbilityValues": {"damage": 50, "proc_chance": 100, "knockback_distance": 50, "slow": -100, "slow_duration": 0.2}}, "sniper_keen_scope": {"AbilityValues": {"bonus_range": 350}, "AbilityTextureName": "sniper_take_aim", "DependentOnAbility": 0, "Innate": 0}, "spirit_breaker_charge_of_darkness": {"AbilityValues": {"movement_speed": 325, "stun_duration": 1.2, "bash_radius": 300, "vision_radius": 400, "vision_duration": 0.94, "AbilityCooldown": 12}, "AbilityCooldown": 12, "AbilityManaCost": 30}, "spirit_breaker_greater_bash": {"AbilityValues": {"AbilityCooldown": 1.2, "chance_pct": 17, "damage": 20, "duration": 1, "knockback_duration": 0.5, "knockback_distance": 143, "knockback_height": 50, "movespeed_duration": 3, "creep_multiplier": 1}}, "jakiro_liquid_fire": {"AbilityValues": {"slow_attack_speed_pct": -40, "radius": 300, "damage": 10, "pct_health_damage": 0, "tick_rate": 0.5, "building_dmg_pct": 75, "shares_cooldown": 0}}, "jakiro_liquid_ice": {"AbilityValues": {"movement_slow": 20, "damage": 10, "bonus_instance_damage_from_other_abilities": 0, "pct_health_damage": 0, "duration": 4, "radius": 0, "tick_rate": 0.5, "shares_cooldown": 0}}, "dark_seer_vacuum": {"AbilityValues": {"radius": 500, "duration": 0.3, "damage": 50, "radius_tree": 150}, "MaxLevel": 1, "AbilityCooldown": 0, "AbilityManaCost": 0, "AbilityUnitDamageType": "DAMAGE_TYPE_MAGICAL", "AbilityBehavior": ["DOTA_ABILITY_BEHAVIOR_POINT", "DOTA_ABILITY_BEHAVIOR_AOE", "DOTA_ABILITY_B<PERSON><PERSON><PERSON>OR_IGNORE_BACKSWING"], "AbilityCastRange": 0, "AbilityCastPoint": 0, "AOERadius": 500, "EffectPar": "particles/gui/choose_card/ab_10/pre_using.vpcf"}, "alpha_wolf_command_aura": {"AbilityValues": {"bonus_damage_pct": 30, "radius": 600}}, "forest_troll_high_priest_heal": {"AbilityValues": {"health": 100}, "MaxLevel": 1, "AbilityCooldown": 6, "AbilityManaCost": 10, "AbilityBehavior": ["DOTA_ABILITY_BEHAVIOR_UNIT_TARGET", "DOTA_ABILITY_B<PERSON><PERSON><PERSON>OR_IGNORE_BACKSWING"], "AbilityCastRange": 450, "AbilityCastPoint": 0.5}, "berserker_troll_break": {"AbilityValues": {}}, "centaur_khan_endurance_aura": {"AbilityValues": {"bonus_attack_speed": 30, "radius": 600}}, "furbolg_enrage_attack_speed": {"AbilityValues": {"bonus_aspd": 60, "duration": 5, "radius": 600}}, "polar_furbolg_ursa_warrior_thunder_clap": {"AbilityValues": {"radius": 300, "movespeed_slow": -25, "attackspeed_slow": -25, "duration": 3}, "AbilityCooldown": 0, "AbilityManaCost": 100, "AbilityDamage": 200, "AbilityUnitDamageType": "DAMAGE_TYPE_MAGICAL", "AbilityBehavior": "DOTA_ABILITY_BEHAVIOR_NO_TARGET"}, "furbolg_enrage_damage": {"AbilityValues": {"bonus_dmg_pct": 30, "duration": 5, "radius": 600}}, "ogre_magi_frost_armor": {"AbilityValues": {"armor_bonus": 5, "duration": 12, "movespeed_slow": -20, "attackspeed_slow": -30, "slow_duration": 5}, "AbilityCooldown": 8, "AbilityManaCost": 50, "AbilityCastRange": 400}, "ogre_bruiser_ogre_smash": {"AbilityValues": {"radius": 200, "damage": 100, "damage_pct": 8, "hero_stun_duration": 2}, "AbilityCooldown": 0, "AbilityManaCost": 100, "AbilityCastPoint": 1.4}, "spawnlord_master_stomp": {"AbilityValues": {"radius": 300, "damage": 150, "duration": 6, "armor_reduction_pct": 50}, "AbilityCooldown": 12, "AbilityManaCost": 100, "AbilityUnitDamageType": "DAMAGE_TYPE_PHYSICAL", "AbilityCastPoint": 0.5}, "spawnlord_master_freeze": {"AbilityValues": {"duration": 1.5, "damage": 100, "tick_interval": 0.1}, "AbilityCooldown": 10, "AbilityUnitDamageType": "DAMAGE_TYPE_PHYSICAL", "AbilityBehavior": ["DOTA_ABILITY_BEHAVIOR_UNIT_TARGET", "DOTA_ABILITY_BEHAVIOR_AUTOCAST", "DOTA_ABILITY_BEHAVIOR_ATTACK"], "AbilityCastRange": 0, "AbilityCastPoint": 0}, "big_thunder_lizard_slam": {"AbilityValues": {"radius": 300, "movespeed_slow": -60, "attack_slow_tooltip": -30, "non_hero_duration": 2, "hero_duration": 2}, "AbilityCooldown": 10, "AbilityManaCost": 30, "AbilityDamage": 150, "AbilityUnitDamageType": "DAMAGE_TYPE_MAGICAL", "AbilityCastPoint": 0.5}, "big_thunder_lizard_frenzy": {"AbilityValues": {"attackspeed_bonus": 60, "duration": 5}, "AbilityCooldown": 8, "AbilityManaCost": 30, "AbilityCastRange": 900, "AbilityCastPoint": 0.5}, "big_thunder_lizard_wardrums_aura": {"AbilityValues": {"speed_bonus": 25, "accuracy": 50, "radius": 800}}, "black_dragon_fireball": {"AbilityValues": {"radius": 300, "damage": 85, "duration": 8, "burn_interval": 0.5}, "AbilityCooldown": 0, "AbilityManaCost": 100, "AbilityUnitDamageType": "DAMAGE_TYPE_MAGICAL", "AbilityCastRange": 500, "AbilityCastPoint": 0.3}, "black_dragon_splash_attack": {"AbilityValues": {"range": 250, "damage_percent": 50}}, "black_dragon_dragonhide_aura": {"AbilityValues": {"bonus_armor": 2, "radius": 600}}, "ice_shaman_incendiary_bomb": {"AbilityValues": {"duration": 4, "burn_damage": 50, "building_damage_pct": 100}, "MaxLevel": 1, "AbilityCooldown": 10, "AbilityManaCost": 30, "AbilityCastRange": 700}, "fw_crystal_maiden_freezing_field": {"BaseClass": "ability_lua", "ScriptFile": "abilities/card_spell/fw_crystal_maiden_freezing_field.lua", "AbilityValues": {"duration": 4, "damage_interval": 0.3, "movespeed_slow": 40, "attackspeed_slow": 80, "damage": 15}, "MaxLevel": 1, "AbilityCooldown": 0, "AbilityManaCost": 0, "AbilityTextureName": "crystal_maiden_freezing_field_alt1", "AbilityUnitDamageType": "DAMAGE_TYPE_MAGICAL", "AbilityBehavior": ["DOTA_ABILITY_BEHAVIOR_POINT", "DOTA_ABILITY_BEHAVIOR_AOE", "DOTA_ABILITY_B<PERSON><PERSON><PERSON>OR_IGNORE_BACKSWING"], "AbilityUnitTargetTeam": "DOTA_UNIT_TARGET_TEAM_ENEMY", "FWTargetType": "all", "AbilityCastRange": 0, "AbilityCastPoint": 0, "SpellDispellableType": "SPELL_DISPELLABLE_YES", "AOERadius": 600, "EffectPar": "particles/gui/choose_card/ab_5/cm_persona_freezing_field_snow_frostglow.vpcf"}, "fw_dragon_knight_fireball": {"BaseClass": "ability_lua", "ScriptFile": "abilities/card_spell/fw_dragon_knight_fireball.lua", "AbilityValues": {"duration": 3, "damage_interval": 0.5, "damage": 30, "first_damage": 250, "project_speed": 1300}, "MaxLevel": 1, "AbilityCooldown": 0, "AbilityManaCost": 0, "AbilityTextureName": "dragon_knight_fireball", "AbilityUnitDamageType": "DAMAGE_TYPE_MAGICAL", "AbilityBehavior": ["DOTA_ABILITY_BEHAVIOR_POINT", "DOTA_ABILITY_BEHAVIOR_AOE", "DOTA_ABILITY_B<PERSON><PERSON><PERSON>OR_IGNORE_BACKSWING"], "AbilityUnitTargetTeam": "DOTA_UNIT_TARGET_TEAM_ENEMY", "FWTargetType": "all", "AbilityCastRange": 0, "AbilityCastPoint": 0, "SpellDispellableType": "SPELL_DISPELLABLE_YES", "AOERadius": 350, "EffectPar": "particles/gui/choose_card/ab_6/pre_using5.vpcf"}, "fw_mirana_starfall": {"BaseClass": "ability_lua", "ScriptFile": "abilities/card_spell/fw_mirana_starfall.lua", "AbilityValues": {"duration": 3, "damage_interval": 1, "damage": 85}, "MaxLevel": 1, "AbilityCooldown": 0, "AbilityManaCost": 0, "AbilityTextureName": "mirana_starfall", "AbilityUnitDamageType": "DAMAGE_TYPE_MAGICAL", "AbilityBehavior": ["DOTA_ABILITY_BEHAVIOR_POINT", "DOTA_ABILITY_BEHAVIOR_AOE", "DOTA_ABILITY_B<PERSON><PERSON><PERSON>OR_IGNORE_BACKSWING"], "AbilityUnitTargetTeam": "DOTA_UNIT_TARGET_TEAM_ENEMY", "FWTargetType": "ground", "AbilityUnitTargetFlags": "DOTA_UNIT_TARGET_FLAG_CAN_BE_SEEN", "AbilityCastRange": 0, "AbilityCastPoint": 0, "AOERadius": 400, "EffectPar": "particles/gui/choose_card/ab_7/pre_using.vpcf"}, "fw_razor_eye_of_the_storm": {"BaseClass": "ability_lua", "ScriptFile": "abilities/card_spell/fw_razor_eye_of_the_storm.lua", "AbilityValues": {"duration": 8, "damage_interval": 0.2, "damage": 30, "move_speed": 150}, "MaxLevel": 1, "AbilityCooldown": 0, "AbilityManaCost": 0, "AbilityTextureName": "razor_eye_of_the_storm", "AbilityUnitDamageType": "DAMAGE_TYPE_MAGICAL", "AbilityBehavior": ["DOTA_ABILITY_BEHAVIOR_POINT", "DOTA_ABILITY_BEHAVIOR_AOE", "DOTA_ABILITY_B<PERSON><PERSON><PERSON>OR_IGNORE_BACKSWING"], "AbilityUnitTargetTeam": "DOTA_UNIT_TARGET_TEAM_ENEMY", "FWTargetType": "all", "AbilityUnitTargetFlags": "DOTA_UNIT_TARGET_FLAG_CAN_BE_SEEN", "AbilityCastRange": 0, "AbilityCastPoint": 0, "AOERadius": 400, "EffectPar": "particles/gui/choose_card/ab_9/pre_using.vpcf"}, "fw_luna_eclipse": {"BaseClass": "ability_lua", "ScriptFile": "abilities/card_spell/fw_luna_eclipse.lua", "AbilityValues": {"duration": 5.2, "damage_interval": 0.4, "damage": 90, "extra_attack": 40}, "MaxLevel": 1, "AbilityCooldown": 0, "AbilityManaCost": 0, "AbilityTextureName": "luna_eclipse", "AbilityUnitDamageType": "DAMAGE_TYPE_MAGICAL", "AbilityBehavior": ["DOTA_ABILITY_BEHAVIOR_POINT", "DOTA_ABILITY_BEHAVIOR_AOE", "DOTA_ABILITY_B<PERSON><PERSON><PERSON>OR_IGNORE_BACKSWING"], "AbilityUnitTargetTeam": "DOTA_UNIT_TARGET_TEAM_BOTH", "FWTargetType": "ground", "AbilityUnitTargetFlags": "DOTA_UNIT_TARGET_FLAG_CAN_BE_SEEN", "AbilityCastRange": 0, "AbilityCastPoint": 0, "AOERadius": 800, "EffectPar": "particles/gui/choose_card/ab_7/pre_using.vpcf"}, "fw_berserk": {"BaseClass": "ability_lua", "ScriptFile": "abilities/card_spell/fw_berserk.lua", "AbilityValues": {"duration": 4, "sub_damage": 30, "attack_speed": 50, "move_speed": 50}, "MaxLevel": 1, "AbilityCooldown": 0, "AbilityManaCost": 0, "AbilityTextureName": "warlock_hellborn_upheaval", "AbilityUnitDamageType": "DAMAGE_TYPE_NONE", "AbilityBehavior": ["DOTA_ABILITY_BEHAVIOR_POINT", "DOTA_ABILITY_BEHAVIOR_AOE", "DOTA_ABILITY_B<PERSON><PERSON><PERSON>OR_IGNORE_BACKSWING"], "AbilityUnitTargetTeam": "DOTA_UNIT_TARGET_TEAM_FRIENDLY", "AbilityUnitTargetType": "DOTA_UNIT_TARGET_HEROES_AND_CREEPS", "FWTargetType": "ground", "AbilityCastRange": 0, "AbilityCastPoint": 0, "AOERadius": 600, "EffectPar": "particles/gui/choose_card/ab_13/pre_using.vpcf"}, "fw_time_travel": {"BaseClass": "ability_lua", "ScriptFile": "abilities/card_spell/fw_time_travel.lua", "AbilityValues": {"delay": 3, "time": 10}, "MaxLevel": 1, "AbilityCooldown": 0, "AbilityManaCost": 0, "AbilityTextureName": "abyssal_underlord_dark_portal", "AbilityUnitDamageType": "DAMAGE_TYPE_NONE", "AbilityBehavior": ["DOTA_ABILITY_BEHAVIOR_POINT", "DOTA_ABILITY_BEHAVIOR_AOE", "DOTA_ABILITY_B<PERSON><PERSON><PERSON>OR_IGNORE_BACKSWING"], "AbilityUnitTargetTeam": "DOTA_UNIT_TARGET_TEAM_BOTH", "AbilityUnitTargetType": "DOTA_UNIT_TARGET_HEROES_AND_CREEPS", "FWTargetType": "ground", "AbilityCastRange": 0, "AbilityCastPoint": 0, "AOERadius": 500, "EffectPar": "particles/gui/choose_card/ab_10/pre_using.vpcf"}, "fw_omniknight_martyr": {"BaseClass": "ability_lua", "ScriptFile": "abilities/card_spell/fw_omniknight_martyr.lua", "AbilityValues": {"duration": 5, "magic_resistance": 50}, "MaxLevel": 1, "AbilityCooldown": 0, "AbilityManaCost": 0, "AbilityTextureName": "omniknight_martyr", "AbilityUnitDamageType": "DAMAGE_TYPE_NONE", "AbilityBehavior": ["DOTA_ABILITY_BEHAVIOR_POINT", "DOTA_ABILITY_BEHAVIOR_AOE", "DOTA_ABILITY_B<PERSON><PERSON><PERSON>OR_IGNORE_BACKSWING"], "AbilityUnitTargetTeam": "DOTA_UNIT_TARGET_TEAM_FRIENDLY", "AbilityUnitTargetType": "DOTA_UNIT_TARGET_HEROES_AND_CREEPS", "FWTargetType": "ground", "AbilityCastRange": 0, "AbilityCastPoint": 0, "AOERadius": 400, "EffectPar": "particles/gui/choose_card/ab_8/pre_using.vpcf"}, "fw_invis": {"BaseClass": "ability_lua", "ScriptFile": "abilities/card_spell/fw_invis.lua", "AbilityValues": {"delay": 1, "duration": 3, "extra_movespeed": 40}, "MaxLevel": 1, "AbilityCooldown": 0, "AbilityManaCost": 0, "AbilityTextureName": "bounty_hunter_wind_walk", "AbilityUnitDamageType": "DAMAGE_TYPE_NONE", "AbilityBehavior": ["DOTA_ABILITY_BEHAVIOR_POINT", "DOTA_ABILITY_BEHAVIOR_AOE", "DOTA_ABILITY_B<PERSON><PERSON><PERSON>OR_IGNORE_BACKSWING"], "AbilityUnitTargetTeam": "DOTA_UNIT_TARGET_TEAM_FRIENDLY", "AbilityUnitTargetType": "DOTA_UNIT_TARGET_HEROES_AND_CREEPS", "FWTargetType": "ground", "AbilityCastRange": 0, "AbilityCastPoint": 0, "AOERadius": 450, "EffectPar": "particles/gui/choose_card/ab_14/pre_using.vpcf"}, "fw_earthshaker_echo_slam": {"BaseClass": "ability_lua", "ScriptFile": "abilities/card_spell/fw_earthshaker_echo_slam.lua", "AbilityValues": {"duration": 5.2, "damage_interval": 0.4, "damage": 70, "extra_attack": 30}, "MaxLevel": 1, "AbilityCooldown": 0, "AbilityManaCost": 0, "AbilityTextureName": "luna_eclipse", "AbilityUnitDamageType": "DAMAGE_TYPE_MAGICAL", "AbilityBehavior": ["DOTA_ABILITY_BEHAVIOR_POINT", "DOTA_ABILITY_BEHAVIOR_AOE", "DOTA_ABILITY_B<PERSON><PERSON><PERSON>OR_IGNORE_BACKSWING"], "AbilityUnitTargetTeam": "DOTA_UNIT_TARGET_TEAM_BOTH", "FWTargetType": "ground", "AbilityUnitTargetFlags": "DOTA_UNIT_TARGET_FLAG_CAN_BE_SEEN", "AbilityCastRange": 0, "AbilityCastPoint": 0, "AOERadius": 800, "EffectPar": "particles/gui/choose_card/ab_7/pre_using.vpcf"}, "fw_summon_zombie": {"BaseClass": "ability_lua", "ScriptFile": "abilities/unit_spell/fw_summon_zombie.lua", "AbilityValues": {"num": 2, "summon_interval": 4, "last_num": 4}, "MaxLevel": 1, "AbilityCooldown": 0, "AbilityManaCost": 0, "AbilityTextureName": "undying_tombstone_zombie_deathstrike", "AbilityBehavior": ["DOTA_ABILITY_BEHAVIOR_NO_TARGET", "DOTA_ABILITY_B<PERSON><PERSON><PERSON>OR_IGNORE_BACKSWING"], "AbilityCastRange": 0, "AbilityCastPoint": 0, "AOERadius": 200, "UnitTemplate": "creep_default_undying_zombie"}, "fw_zombie_extra": {"BaseClass": "ability_lua", "ScriptFile": "abilities/unit_spell/fw_zombie_extra.lua", "AbilityValues": {"duration": 3}, "MaxLevel": 1, "AbilityCooldown": 0, "AbilityTextureName": "undying_soul_rip", "AbilityBehavior": "DOTA_ABILITY_BEHAVIOR_PASSIVE", "AbilityCastPoint": 0}, "fw_summon_zombie_b": {"BaseClass": "ability_lua", "ScriptFile": "abilities/unit_spell/fw_summon_zombie_b.lua", "AbilityValues": {"num": 2}, "MaxLevel": 1, "AbilityCooldown": 10, "AbilityManaCost": 50, "AbilityTextureName": "undying_tombstone_zombie_deathstrike", "AbilityBehavior": ["DOTA_ABILITY_BEHAVIOR_NO_TARGET", "DOTA_ABILITY_B<PERSON><PERSON><PERSON>OR_IGNORE_BACKSWING"], "AbilityCastPoint": 0.5, "AbilityCastAnimation": "ACT_DOTA_CAST_ABILITY_2", "UnitTemplate": "creep_default_undying_zombie"}, "fw_techies_summon_bomb": {"BaseClass": "ability_lua", "ScriptFile": "abilities/unit_spell/fw_techies_summon_bomb.lua", "AbilityValues": {"damage": 150, "range": 300}, "MaxLevel": 1, "AbilityCooldown": 6, "AbilityManaCost": 30, "AbilityTextureName": "techies_sticky_bomb", "AbilityUnitDamageType": "DAMAGE_TYPE_MAGICAL", "AbilityBehavior": ["DOTA_ABILITY_BEHAVIOR_NO_TARGET", "DOTA_ABILITY_B<PERSON><PERSON><PERSON>OR_IGNORE_BACKSWING"], "AbilityCastPoint": 0.5, "AbilityCastAnimation": "ACT_DOTA_CAST_ABILITY_6", "UnitTemplate": "creep_default_techies_bomb"}, "fw_techies_bomb_adsorb": {"BaseClass": "ability_lua", "ScriptFile": "abilities/unit_spell/fw_techies_bomb_adsorb.lua", "AbilityValues": {"damage": 150, "range": 300}, "MaxLevel": 1, "AbilityCooldown": 0, "AbilityTextureName": "techies_sticky_bomb", "AbilityUnitDamageType": "DAMAGE_TYPE_MAGICAL", "AbilityBehavior": "DOTA_ABILITY_BEHAVIOR_PASSIVE", "AbilityUnitTargetTeam": "DOTA_UNIT_TARGET_TEAM_ENEMY", "AbilityUnitTargetType": "DOTA_UNIT_TARGET_HEROES_AND_CREEPS", "FWTargetType": "all", "AbilityUnitTargetFlags": "DOTA_UNIT_TARGET_FLAG_CAN_BE_SEEN"}, "fw_huskar_berserkers_blood": {"BaseClass": "ability_lua", "ScriptFile": "abilities/unit_spell/fw_huskar_berserkers_blood.lua", "AbilityValues": {"maximum_attack_speed": 150, "maximum_health_regen": 20, "hp_threshold_max": 25, "maximum_magic_resist": 25}, "MaxLevel": 1, "AbilityCooldown": 0, "AbilityManaCost": 0, "AbilityTextureName": "huskar_berserkers_blood", "AbilityBehavior": "DOTA_ABILITY_BEHAVIOR_PASSIVE", "IsBreakable": 1, "SpellDispellableType": "SPELL_DISPELLABLE_NO"}, "fw_tiny_tree_grab": {"BaseClass": "ability_lua", "ScriptFile": "abilities/unit_spell/fw_tiny_tree_grab.lua", "AbilityValues": {"attack_count": 4, "bonus_damage": 150, "bonus_damage_buildings": 100, "attack_range": 150, "splash_width": 200, "splash_range": 400, "splash_pct": 80, "extra_num": 2, "status_resistance": 60}, "MaxLevel": 1, "AbilityCooldown": 5, "AbilityManaCost": 0, "AbilityTextureName": "tiny_tree_grab", "AbilityUnitDamageType": "DAMAGE_TYPE_PHYSICAL", "AbilityBehavior": ["DOTA_ABILITY_BEHAVIOR_NO_TARGET", "DOTA_ABILITY_B<PERSON><PERSON><PERSON>OR_IGNORE_BACKSWING"], "AbilityCastRange": 150, "SpellDispellableType": "SPELL_DISPELLABLE_NO"}, "fw_luna_lunar_grace": {"BaseClass": "ability_lua", "ScriptFile": "abilities/unit_spell/fw_luna_lunar_grace.lua", "AbilityValues": {"max_radius": 400, "min_radius": 100, "min_attack": 10, "max_attack": 30, "time": 0.5, "extra_attack": 4}, "MaxLevel": 1, "AbilityCooldown": 0, "AbilityManaCost": 0, "AbilityTextureName": "luna_lunar_blessing", "AbilityBehavior": "DOTA_ABILITY_BEHAVIOR_PASSIVE", "IsBreakable": 1}, "fw_weaver_geminate_attack": {"BaseClass": "ability_lua", "ScriptFile": "abilities/unit_spell/fw_weaver_geminate_attack.lua", "AbilityValues": {}, "MaxLevel": 1, "AbilityCooldown": 6, "AbilityTextureName": "weaver_geminate_attack", "AbilityBehavior": "DOTA_ABILITY_BEHAVIOR_PASSIVE", "AbilityCastPoint": 0}, "fw_forest_troll_high_priest_heal": {"BaseClass": "ability_lua", "ScriptFile": "abilities/unit_spell/fw_forest_troll_high_priest_heal.lua", "AbilityValues": {"health": 80}, "MaxLevel": 1, "AbilityCooldown": 4, "AbilityManaCost": 20, "AbilityTextureName": "forest_troll_high_priest_heal", "AbilityBehavior": ["DOTA_ABILITY_BEHAVIOR_UNIT_TARGET", "DOTA_ABILITY_B<PERSON><PERSON><PERSON>OR_IGNORE_BACKSWING"], "AbilityUnitTargetTeam": "DOTA_UNIT_TARGET_TEAM_FRIENDLY", "AbilityUnitTargetType": "DOTA_UNIT_TARGET_HEROES_AND_CREEPS", "FWTargetType": "ground", "AbilityCastRange": 450, "AbilityCastPoint": 0.5}, "fw_shadow_shaman_shackles": {"BaseClass": "ability_lua", "ScriptFile": "abilities/unit_spell/fw_shadow_shaman_shackles.lua", "AbilityValues": {"duration": 3, "damage": 10, "self_damage": 200, "extra_dsub": 10, "max_dsub": 50}, "MaxLevel": 1, "AbilityCooldown": 8, "AbilityManaCost": 20, "AbilityTextureName": "shadow_shaman_shackles", "AbilityUnitDamageType": "DAMAGE_TYPE_MAGICAL", "AbilityBehavior": ["DOTA_ABILITY_BEHAVIOR_UNIT_TARGET", "DOTA_ABILITY_BEHAVIOR_CHANNELLED"], "AbilityUnitTargetTeam": "DOTA_UNIT_TARGET_TEAM_ENEMY", "AbilityUnitTargetType": "DOTA_UNIT_TARGET_HEROES_AND_CREEPS", "FWTargetType": "ground", "AbilityCastRange": 400, "AbilityCastPoint": 0.3, "SpellDispellableType": "SPELL_DISPELLABLE_YES"}, "fw_crystal_maiden_frostbite": {"BaseClass": "ability_lua", "ScriptFile": "abilities/unit_spell/fw_crystal_maiden_frostbite.lua", "AbilityValues": {"delay": 1, "duration": 2.5, "attack_speed_debuff": -40, "range": 300}, "MaxLevel": 1, "AbilityCooldown": 0, "AbilityManaCost": 0, "AbilityTextureName": "crystal_maiden_frostbite", "AbilityBehavior": "DOTA_ABILITY_BEHAVIOR_PASSIVE", "AbilityUnitTargetTeam": "DOTA_UNIT_TARGET_TEAM_ENEMY", "AbilityUnitTargetType": "DOTA_UNIT_TARGET_HEROES_AND_CREEPS", "FWTargetType": "ground", "IsBreakable": 1, "SpellDispellableType": "SPELL_DISPELLABLE_YES"}, "fw_hero_spell_hide": {"BaseClass": "ability_lua", "ScriptFile": "abilities/hero_spell/fw_hero_spell_hide.lua", "AbilityValues": {}, "MaxLevel": 1, "AbilityCooldown": 0, "AbilityManaCost": 0, "AbilityTextureName": "action_stop", "AbilityCastRange": 0, "AbilityCastPoint": 0}, "fw_hero_spell_sven": {"BaseClass": "ability_lua", "ScriptFile": "abilities/hero_spell/fw_hero_spell_sven.lua", "AbilityValues": {"shield": 100, "duration": 5, "cost": 1, "range": 400}, "MaxLevel": 1, "AbilityCooldown": 10, "AbilityManaCost": 0, "AbilityTextureName": "sven_warcry", "AbilityBehavior": ["DOTA_ABILITY_BEHAVIOR_POINT", "DOTA_ABILITY_BEHAVIOR_AOE", "DOTA_ABILITY_B<PERSON><PERSON><PERSON>OR_IGNORE_BACKSWING"], "AbilityUnitTargetTeam": "DOTA_UNIT_TARGET_TEAM_FRIENDLY", "AbilityUnitTargetType": "DOTA_UNIT_TARGET_HEROES_AND_CREEPS", "FWTargetType": "ground", "AbilityCastRange": 0, "AbilityCastPoint": 0, "SpellDispellableType": "SPELL_DISPELLABLE_YES", "AOERadius": 400, "EffectPar": "particles/gui/choose_card/ab_8/pre_using.vpcf"}, "fw_hero_spell_assassin": {"BaseClass": "ability_lua", "ScriptFile": "abilities/hero_spell/fw_hero_spell_assassin.lua", "AbilityValues": {"cooldown": 10, "cost_sub": 1}, "MaxLevel": 1, "AbilityCooldown": 0, "AbilityManaCost": 0, "AbilityTextureName": "necronomicon_archer_purge", "AbilityBehavior": "DOTA_ABILITY_BEHAVIOR_PASSIVE", "AbilityCastRange": 0, "AbilityCastPoint": 0}, "fw_hero_spell_priest": {"BaseClass": "ability_lua", "ScriptFile": "abilities/hero_spell/fw_hero_spell_priest.lua", "AbilityValues": {"extra_health": 100, "attackspeed": 40, "attack": 30, "movespeed": 40, "cost": 0}, "MaxLevel": 1, "AbilityCooldown": 12, "AbilityManaCost": 0, "AbilityTextureName": "spell_priest", "AbilityBehavior": "DOTA_ABILITY_BEHAVIOR_PASSIVE", "AbilityCastRange": 0, "AbilityCastPoint": 0, "SpellDispellableType": "SPELL_DISPELLABLE_YES"}, "fw_hero_spell_saint": {"BaseClass": "ability_lua", "ScriptFile": "abilities/hero_spell/fw_hero_spell_saint.lua", "AbilityValues": {"duration": 2, "cost": 1, "range": 300}, "MaxLevel": 1, "AbilityCooldown": 4, "AbilityManaCost": 0, "AbilityTextureName": "drow_ranger_wave_of_silence", "AbilityBehavior": ["DOTA_ABILITY_BEHAVIOR_POINT", "DOTA_ABILITY_BEHAVIOR_AOE", "DOTA_ABILITY_B<PERSON><PERSON><PERSON>OR_IGNORE_BACKSWING"], "AbilityUnitTargetTeam": "DOTA_UNIT_TARGET_TEAM_BOTH", "AbilityUnitTargetType": "DOTA_UNIT_TARGET_HEROES_AND_CREEPS", "FWTargetType": "ground", "AbilityCastRange": 0, "AbilityCastPoint": 0, "SpellDispellableType": "SPELL_DISPELLABLE_YES", "AOERadius": 300, "EffectPar": "particles/gui/choose_card/ab_4/pre_using_3.vpcf"}, "fw_hero_spell_saint_lv2": {"BaseClass": "ability_lua", "ScriptFile": "abilities/hero_spell/fw_hero_spell_saint.lua", "AbilityValues": {"duration": 3, "movespeed_slow": 20, "cost": 2, "range": 400}, "MaxLevel": 1, "AbilityCooldown": 4, "AbilityManaCost": 0, "AbilityTextureName": "drow_ranger_silence_arcana_alt1", "AbilityBehavior": ["DOTA_ABILITY_BEHAVIOR_POINT", "DOTA_ABILITY_BEHAVIOR_AOE", "DOTA_ABILITY_B<PERSON><PERSON><PERSON>OR_IGNORE_BACKSWING"], "AbilityUnitTargetTeam": "DOTA_UNIT_TARGET_TEAM_ENEMY", "AbilityUnitTargetType": "DOTA_UNIT_TARGET_HEROES_AND_CREEPS", "FWTargetType": "ground", "AbilityCastRange": 0, "AbilityCastPoint": 0, "SpellDispellableType": "SPELL_DISPELLABLE_YES", "AOERadius": 400, "EffectPar": "particles/gui/choose_card/ab_4/pre_using_3.vpcf"}, "fw_hero_spell_saint_lv3": {"BaseClass": "ability_lua", "ScriptFile": "abilities/hero_spell/fw_hero_spell_saint.lua", "AbilityValues": {"passive_duration": 5, "duration": 4, "damage": 60, "movespeed_slow": 40, "cost": 2, "range": 500}, "MaxLevel": 1, "AbilityCooldown": 4, "AbilityManaCost": 0, "AbilityTextureName": "drow_ranger_silence_arcana", "AbilityUnitDamageType": "DAMAGE_TYPE_MAGICAL", "AbilityBehavior": ["DOTA_ABILITY_BEHAVIOR_POINT", "DOTA_ABILITY_BEHAVIOR_AOE", "DOTA_ABILITY_B<PERSON><PERSON><PERSON>OR_IGNORE_BACKSWING"], "AbilityUnitTargetTeam": "DOTA_UNIT_TARGET_TEAM_BOTH", "AbilityUnitTargetType": "DOTA_UNIT_TARGET_HEROES_AND_CREEPS", "FWTargetType": "ground", "AbilityCastRange": 0, "AbilityCastPoint": 0, "SpellDispellableType": "SPELL_DISPELLABLE_YES", "AOERadius": 500, "EffectPar": "particles/gui/choose_card/ab_4/pre_using_3.vpcf"}, "fw_hero_spell_treant": {"BaseClass": "ability_lua", "ScriptFile": "abilities/hero_spell/fw_hero_spell_treant.lua", "AbilityValues": {"health": 150, "armor": 5, "duration": 3, "cost": 3, "range": 300}, "MaxLevel": 1, "AbilityCooldown": 25, "AbilityManaCost": 0, "AbilityTextureName": "treant_living_armor", "AbilityBehavior": ["DOTA_ABILITY_BEHAVIOR_POINT", "DOTA_ABILITY_BEHAVIOR_AOE", "DOTA_ABILITY_B<PERSON><PERSON><PERSON>OR_IGNORE_BACKSWING"], "AbilityUnitTargetTeam": "DOTA_UNIT_TARGET_TEAM_FRIENDLY", "AbilityUnitTargetType": "DOTA_UNIT_TARGET_HEROES_AND_CREEPS", "FWTargetType": "all", "AbilityCastRange": 0, "AbilityCastPoint": 0, "SpellDispellableType": "SPELL_DISPELLABLE_YES", "AOERadius": 300, "EffectPar": "particles/gui/choose_card/ab_11/pre_using.vpcf"}, "fw_hero_spell_lycan": {"BaseClass": "ability_lua", "ScriptFile": "abilities/hero_spell/fw_hero_spell_lycan.lua", "AbilityValues": {"cost": 0, "attack": 20}, "MaxLevel": 1, "AbilityCooldown": 6, "AbilityManaCost": 0, "AbilityTextureName": "lycan_summon_wolves", "AbilityCastRange": 0, "AbilityCastPoint": 0}, "fw_hero_spell_beastmaster": {"BaseClass": "ability_lua", "ScriptFile": "abilities/hero_spell/fw_hero_spell_beastmaster.lua", "AbilityValues": {"cost": 1, "attackspeed": 30, "ues_attackspeed": 30, "duration": 5}, "MaxLevel": 1, "AbilityCooldown": 10, "AbilityManaCost": 0, "AbilityTextureName": "beastmaster_drums_of_slom", "AbilityBehavior": ["DOTA_ABILITY_BEHAVIOR_POINT", "DOTA_ABILITY_BEHAVIOR_AOE", "DOTA_ABILITY_B<PERSON><PERSON><PERSON>OR_IGNORE_BACKSWING"], "AbilityUnitTargetTeam": "DOTA_UNIT_TARGET_TEAM_FRIENDLY", "AbilityUnitTargetType": "DOTA_UNIT_TARGET_HEROES_AND_CREEPS", "FWTargetType": "all", "AbilityCastRange": 0, "AbilityCastPoint": 0, "SpellDispellableType": "SPELL_DISPELLABLE_YES", "AOERadius": 500, "EffectPar": "particles/gui/choose_card/ab_12/pre_using.vpcf"}, "fw_hero_spell_blacksmith": {"BaseClass": "ability_lua", "ScriptFile": "abilities/hero_spell/fw_hero_spell_blacksmith.lua", "AbilityValues": {"attack_speed": 70, "attack_speed_debuff": -40, "attack_damage": 50, "num": 15, "time": 0.3, "re_time": 2}, "MaxLevel": 1, "AbilityCooldown": 0, "AbilityManaCost": 0, "AbilityTextureName": "hill_troll_rally", "AbilityBehavior": "DOTA_ABILITY_BEHAVIOR_PASSIVE", "AbilityCastRange": 0, "AbilityCastPoint": 0, "SpellDispellableType": "SPELL_DISPELLABLE_NO"}}