import { MathUtils } from "./math_utils";


export class HostTimeUtils {


    public static defaultBulletTime (player:CDOTAPlayer<PERSON>ontroller, pos:Vector) {
        HostTimeUtils.changeTimeScale(0.1, 0.1, 0.3, 0.1)
    }

    public static bossInBulletTime (bossName:string) {
        HostTimeUtils.changeTimeScale(0.3, 1, 4, 0.05)
    }

    public static changeTimeScale (fadeIn:number, fadeOut:number, mainTime:number, maxScale:number) {
        if (GameRules.timeScaleChange) {
            return false
        }
        GameRules.timeScaleChange = true
        mainTime = Math.max(fadeIn+fadeOut,mainTime)
        fadeOut = mainTime - fadeOut
        let n = 0
        let timeScale = 1
        Timers.CreateTimer(()=>{
            if (n < fadeIn) {
                timeScale = MathUtils.numInRange(n,0,fadeIn,1,maxScale)
            } else if (n > fadeOut) {
                timeScale = MathUtils.numInRange(n,fadeOut,mainTime,maxScale,1)
            } else {
                timeScale = maxScale
            }
            SendToServerConsole("host_timescale "+timeScale)
            if (n < mainTime) {
                n += 0.03/(timeScale/1)
                return 0.03
            } else {
                SendToServerConsole("host_timescale "+ 1)
                GameRules.timeScaleChange = false
            }
        })
        return true
    }


}