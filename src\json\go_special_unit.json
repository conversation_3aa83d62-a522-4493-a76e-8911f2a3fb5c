{"radiant_tower_default": {"Template": "tower_default", "SpecialName": "radiant", "Model": "models/props_structures/tower_good.vmdl", "MinimapIcon": "minimap_tower90", "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": "particles/base_attacks/ranged_tower_good.vpcf", "TowerDestroyPar": "particles/radiant_fx/radiant_tower002_destruction.vpcf", "TowerAmbientPar": "particles/radiant_fx/tower_good3_lamp.vpcf", "precache": {"model": "models/props_structures/tower_good.vmdl", "particle": "particles/radiant_fx/tower_good3_lamp.vpcf"}, "OriginName": "npc_dota_hero_tower_radiant"}, "dire_tower_default": {"Template": "tower_default", "SpecialName": "dire", "Model": "models/props_structures/tower_bad.vmdl", "MinimapIcon": "minimap_tower90", "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": "particles/base_attacks/ranged_tower_bad.vpcf", "TowerDestroyPar": "particles/dire_fx/dire_tower002_destruction.vpcf", "TowerAmbientPar": "particles/dire_fx/tower_bad_lamp.vpcf", "precache": {"model": "models/props_structures/tower_bad.vmdl", "particle": "particles/dire_fx/tower_bad_lamp.vpcf"}, "OriginName": "npc_dota_hero_tower_dire"}, "radiant_tower_default_aciant": {"Template": "tower_default_aciant", "SpecialName": "radiant", "Model": "models/props_structures/radiant_ancient001.vmdl", "MinimapIcon": "minimap_ancient", "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": "particles/base_attacks/ranged_tower_good.vpcf", "TowerDestroyPar": "particles/radiant_fx2/radiant_ancient001_destruction2.vpcf", "TowerAmbientPar": "particles/radiant_fx2/good_ancient001_ambient.vpcf", "precache": {"model": "models/props_structures/radiant_ancient001.vmdl", "particle": "particles/radiant_fx2/good_ancient001_ambient.vpcf"}, "OriginName": "npc_dota_hero_default"}, "dire_tower_default_aciant": {"Template": "tower_default_aciant", "SpecialName": "dire", "Model": "models/props_structures/bad_ancient002.vmdl", "MinimapIcon": "minimap_ancient", "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": "particles/base_attacks/ranged_tower_bad.vpcf", "TowerDestroyPar": "particles/radiant_fx2/dire_ancient_base001_destruction2.vpcf", "TowerAmbientPar": "particles/dire_fx/bad_ancient_ambient.vpcf", "precache": {"model": "models/props_structures/bad_ancient002.vmdl", "particle": "particles/dire_fx/bad_ancient_ambient.vpcf"}, "OriginName": "npc_dota_hero_default"}, "radiant_creep_default_melee": {"Template": "creep_default_melee", "SpecialName": "radiant", "Model": "models/creeps/lane_creeps/creep_radiant_melee/radiant_melee.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "precache": {"model": "models/creeps/lane_creeps/creep_radiant_melee/radiant_melee.vmdl"}, "OriginName": "npc_dota_hero_creep_radiant", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.small", "DeathSound": "Fw.Cards.Creep.Default.Death.small"}, "dire_creep_default_melee": {"Template": "creep_default_melee", "SpecialName": "dire", "Model": "models/creeps/lane_creeps/creep_bad_melee/creep_bad_melee.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "precache": {"model": "models/creeps/lane_creeps/creep_bad_melee/creep_bad_melee.vmdl"}, "OriginName": "npc_dota_hero_creep_dire", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.small", "DeathSound": "Fw.Cards.Creep.Default.Death.small"}, "radiant_creep_default_ranged": {"Template": "creep_default_ranged", "SpecialName": "radiant", "Model": "models/creeps/lane_creeps/creep_radiant_ranged/radiant_ranged.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": "particles/base_attacks/ranged_goodguy.vpcf", "precache": {"model": "models/creeps/lane_creeps/creep_radiant_ranged/radiant_ranged.vmdl", "particle": "particles/base_attacks/ranged_goodguy.vpcf"}, "OriginName": "npc_dota_hero_creep_radiant", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.small", "DeathSound": "Fw.Cards.Creep.Default.Death.small"}, "dire_creep_default_ranged": {"Template": "creep_default_ranged", "SpecialName": "dire", "Model": "models/creeps/lane_creeps/creep_bad_ranged/lane_dire_ranged.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": "particles/base_attacks/ranged_badguy.vpcf", "precache": {"model": "models/creeps/lane_creeps/creep_bad_ranged/lane_dire_ranged.vmdl", "particle": "particles/base_attacks/ranged_badguy.vpcf"}, "OriginName": "npc_dota_hero_creep_dire", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.small", "DeathSound": "Fw.Cards.Creep.Default.Death.small"}, "radiant_creep_default_siege": {"Template": "creep_default_siege", "SpecialName": "radiant", "Model": "models/creeps/lane_creeps/creep_good_siege/creep_good_siege.vmdl", "MinimapIcon": "minimap_siege", "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": "particles/base_attacks/ranged_siege_good.vpcf", "precache": {"model": "models/creeps/lane_creeps/creep_good_siege/creep_good_siege.vmdl", "particle": "particles/base_attacks/ranged_siege_good.vpcf"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.Engine", "DeathSound": "Fw.Cards.Creep.Default.Death.siege"}, "dire_creep_default_siege": {"Template": "creep_default_siege", "SpecialName": "dire", "Model": "models/creeps/lane_creeps/creep_bad_siege/creep_bad_siege.vmdl", "MinimapIcon": "minimap_siege", "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": "particles/base_attacks/ranged_siege_bad.vpcf", "precache": {"model": "models/creeps/lane_creeps/creep_bad_siege/creep_bad_siege.vmdl", "particle": "particles/base_attacks/ranged_siege_bad.vpcf"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.Engine", "DeathSound": "Fw.Cards.Creep.Default.Death.siege"}, "base_creep_default_axe": {"Template": "creep_default_axe", "SpecialName": "base", "Model": "models/heroes/axe/axe.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 2}, "2": {"ItemDef": 3}, "3": {"ItemDef": 4}, "4": {"ItemDef": 5}, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "precache": {"model": "models/heroes/axe/axe.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.Axe.Entrance.Default", "DeathSound": "Fw.Cards.Hero.Axe.Death.Default"}, "default_creep_default_axe": {"Template": "creep_default_axe", "SpecialName": "default", "Model": "models/heroes/axe/axe.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 18545}, "2": {"ItemDef": 18546}, "3": {"ItemDef": 18547}, "4": {"ItemDef": 18548}, "5": {"ItemDef": 18549}, "6": {"ItemDef": 18550}, "7": 0, "8": 0, "9": 0, "10": 0}}, "precache": {"model": "models/heroes/axe/axe.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.Axe.Entrance.Default", "DeathSound": "Fw.Cards.Hero.Axe.Death.Default"}, "default_creep_default_juggernaut": {"Template": "creep_default_juggernaut", "SpecialName": "default", "Model": "models/heroes/juggernaut/juggernaut.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 6}, "2": {"ItemDef": 7}, "3": {"ItemDef": 8}, "4": {"ItemDef": 9}, "5": {"ItemDef": 62}, "6": {"ItemDef": 811}, "7": 0, "8": 0, "9": 0, "10": 0}}, "precache": {"model": "models/heroes/juggernaut/juggernaut.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.Jugg.Entrance.Default", "DeathSound": "Fw.Cards.Hero.Jugg.Death.Default"}, "default_creep_granite_golem": {"Template": "creep_granite_golem", "SpecialName": "default", "Model": "models/creeps/neutral_creeps/n_creep_golem_a/neutral_creep_golem_a.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "precache": {"model": "models/creeps/neutral_creeps/n_creep_golem_a/neutral_creep_golem_a.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.granite", "DeathSound": 0}, "default_creep_undyingt_tombstone": {"Template": "creep_undyingt_tombstone", "SpecialName": "default", "Model": "models/heroes/undying/undying_tower.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "precache": {"model": "models/heroes/undying/undying_tower.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Tombstone.Entrance", "DeathSound": "Fw.Cards.Creep.Tombstone.Death"}, "default_creep_default_undying_zombie": {"Template": "creep_default_undying_zombie", "SpecialName": "default", "Model": "models/heroes/undying/undying_minion.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "precache": {"model": "models/heroes/undying/undying_minion.vmdl"}, "OriginName": "npc_dota_unit_undying_zombie", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.undying", "DeathSound": 0}, "default_creep_default_flesh_golem": {"Template": "creep_default_flesh_golem", "SpecialName": "default", "Model": "models/heroes/undying/undying_flesh_golem.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "precache": {"model": "models/heroes/undying/undying_flesh_golem.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.flesh_golem.Entrance", "DeathSound": 0}, "default_creep_default_abaddon": {"Template": "creep_default_abaddon", "SpecialName": "default", "Model": "models/heroes/abaddon/abaddon.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 454}, "2": {"ItemDef": 455}, "3": {"ItemDef": 456}, "4": {"ItemDef": 457}, "5": {"ItemDef": 458}, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "precache": {"model": "models/heroes/abaddon/abaddon.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.A<PERSON>don.Entrance.Default", "DeathSound": "Fw.Cards.Hero<PERSON>.Death.Default"}, "default_creep_default_ursa": {"Template": "creep_default_ursa", "SpecialName": "default", "Model": "models/heroes/ursa/ursa.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 85}, "2": {"ItemDef": 86}, "3": {"ItemDef": 87}, "4": {"ItemDef": 88}, "5": {"ItemDef": 584}, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "precache": {"model": "models/heroes/ursa/ursa.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.Ursa.Entrance.Default", "DeathSound": "Fw.Cards.Hero.Ursa.Death.Default"}, "default_creep_default_weaver": {"Template": "creep_default_weaver", "SpecialName": "default", "Model": "models/heroes/weaver/weaver.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 277}, "2": {"ItemDef": 278}, "3": {"ItemDef": 279}, "4": {"ItemDef": 280}, "5": {"ItemDef": 585}, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": "particles/units/heroes/hero_weaver/weaver_base_attack.vpcf", "precache": {"model": "models/heroes/weaver/weaver.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero<PERSON>.Entrance.Default", "DeathSound": "Fw.Cards.<PERSON><PERSON>.Death.Default"}, "default_creep_default_spirit_breaker": {"Template": "creep_default_spirit_breaker", "SpecialName": "default", "Model": "models/heroes/spirit_breaker/spirit_breaker.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 106}, "2": {"ItemDef": 111}, "3": {"ItemDef": 112}, "4": {"ItemDef": 113}, "5": {"ItemDef": 114}, "6": {"ItemDef": 115}, "7": {"ItemDef": 116}, "8": 0, "9": 0, "10": 0}}, "precache": {"model": "models/heroes/spirit_breaker/spirit_breaker.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.spirit_breaker.Entrance.Default", "DeathSound": "Fw.Cards.Hero.spirit_breaker.Death.Default"}, "default_creep_default_huskar": {"Template": "creep_default_huskar", "SpecialName": "default", "Model": "models/heroes/huskar/huskar.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 268}, "2": {"ItemDef": 269}, "3": {"ItemDef": 270}, "4": {"ItemDef": 271}, "5": {"ItemDef": 272}, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": "particles/units/heroes/hero_huskar/huskar_base_attack.vpcf", "precache": {"model": "models/heroes/huskar/huskar.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.huskar.Entrance.Default", "DeathSound": "Fw.Cards.Hero.huskar.<PERSON>.Default"}, "default_creep_default_earthshaker": {"Template": "creep_default_earthshaker", "SpecialName": "default", "Model": "models/heroes/earthshaker/earthshaker.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 459}, "2": {"ItemDef": 460}, "3": {"ItemDef": 461}, "4": {"ItemDef": 462}, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "precache": {"model": "models/heroes/earthshaker/earthshaker.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "Entrance": "fall_down", "EntranceSound": "Fw.Cards.Hero.earthshaker.Entrance.Default", "DeathSound": "Fw.Cards.Hero.earthshaker.Death.Default"}, "default_creep_default_skywrath_mage": {"Template": "creep_default_skywrath_mage", "SpecialName": "default", "Model": "models/heroes/skywrath_mage/skywrath_mage.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 420}, "2": {"ItemDef": 421}, "3": {"ItemDef": 422}, "4": {"ItemDef": 423}, "5": {"ItemDef": 424}, "6": {"ItemDef": 425}, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": "particles/units/heroes/hero_skywrath_mage/skywrath_mage_base_attack.vpcf", "precache": {"model": "models/heroes/skywrath_mage/skywrath_mage.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.skywrath_mage.Entrance.Default", "DeathSound": "Fw.Cards.Hero.skywrath_mage.Death.Default"}, "default_creep_default_dark_seer": {"Template": "creep_default_dark_seer", "SpecialName": "default", "Model": "models/heroes/dark_seer/dark_seer.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 348}, "2": {"ItemDef": 349}, "3": {"ItemDef": 350}, "4": {"ItemDef": 351}, "5": {"ItemDef": 352}, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "precache": {"model": "models/heroes/dark_seer/dark_seer.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.dark_seer.Entrance.Default", "DeathSound": "Fw.Cards.Hero.dark_seer.<PERSON>.Default"}, "default_creep_default_primal_beast": {"Template": "creep_default_primal_beast", "SpecialName": "default", "Model": "models/heroes/primal_beast/primal_beast_base.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 769}, "2": {"ItemDef": 770}, "3": {"ItemDef": 771}, "4": {"ItemDef": 772}, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "precache": {"model": "models/heroes/primal_beast/primal_beast_base.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.primal_beast.Entrance.Default", "DeathSound": "Fw.Cards.Hero.primal_beast.Death.Default"}, "default_creep_default_tiny": {"Template": "creep_default_tiny", "SpecialName": "default", "Model": "models/heroes/tiny/tiny_04/tiny_04.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "precache": {"model": "models/heroes/tiny/tiny_04/tiny_04.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.tiny.Entrance.Default", "DeathSound": "Fw.Cards.Hero.tiny.<PERSON>.Default", "ExtraModel": "models/heroes/tiny/tiny_tree/tiny_tree.vmdl"}, "base_creep_default_luna": {"Template": "creep_default_luna", "SpecialName": "base", "Model": "models/heroes/luna/luna.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 448}, "2": {"ItemDef": 449}, "3": {"ItemDef": 450}, "4": {"ItemDef": 451}, "5": {"ItemDef": 452}, "6": {"ItemDef": 453}, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": "particles/units/heroes/hero_luna/luna_base_attack.vpcf", "precache": {"model": "models/heroes/luna/luna.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.luna.Entrance.Default", "DeathSound": "Fw.Cards.Hero.luna.Death.Default"}, "default_creep_default_sniper": {"Template": "creep_default_sniper", "SpecialName": "default", "Model": "models/heroes/sniper/sniper.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 281}, "2": {"ItemDef": 282}, "3": {"ItemDef": 283}, "4": {"ItemDef": 284}, "5": {"ItemDef": 285}, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": "particles/units/heroes/hero_sniper/sniper_base_attack.vpcf", "precache": {"model": "models/heroes/sniper/sniper.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.<PERSON>s.Hero.sniper.Entrance.Default", "DeathSound": "Fw.Cards.Hero.sniper.<PERSON>.Default"}, "default_creep_default_techies": {"Template": "creep_default_techies", "SpecialName": "default", "Model": "models/heroes/techies/techies.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 487}, "2": {"ItemDef": 489}, "3": {"ItemDef": 490}, "4": {"ItemDef": 491}, "5": {"ItemDef": 492}, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": "particles/units/heroes/hero_techies/techies_base_attack.vpcf", "precache": {"model": "models/heroes/techies/techies.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.techies.Entrance.Default", "DeathSound": "Fw.Cards.Hero.techies.Death.Default"}, "default_creep_default_jakiro": {"Template": "creep_default_jakiro", "SpecialName": "default", "Model": "models/heroes/jakiro/jakiro.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 631}, "2": {"ItemDef": 633}, "3": {"ItemDef": 634}, "4": {"ItemDef": 635}, "5": {"ItemDef": 636}, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": "particles/units/heroes/hero_jakiro/jakiro_base_attack.vpcf", "precache": {"model": "models/heroes/jakiro/jakiro.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.jakiro.Entrance.Default", "DeathSound": "Fw.Cards.Hero.jakiro.Death.Default"}, "default_creep_default_visage": {"Template": "creep_default_visage", "SpecialName": "default", "Model": "models/heroes/visage/visage.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": "particles/units/heroes/hero_jakiro/jakiro_base_attack.vpcf", "precache": {"model": "models/heroes/visage/visage.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": 0, "DeathSound": 0}, "default_creep_default_luna": {"Template": "creep_default_luna", "SpecialName": "default", "Model": "models/heroes/luna/luna.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 448}, "2": {"ItemDef": 449}, "3": {"ItemDef": 450}, "4": {"ItemDef": 451}, "5": {"ItemDef": 452}, "6": {"ItemDef": 453}, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": "particles/units/heroes/hero_luna/luna_base_attack.vpcf", "precache": {"model": "models/heroes/luna/luna.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.luna.Entrance.Default", "DeathSound": "Fw.Cards.Hero.luna.Death.Default"}, "default_creep_default_alpha_wolf": {"Template": "creep_default_alpha_wolf", "SpecialName": "default", "Model": "models/creeps/neutral_creeps/n_creep_worg_large/n_creep_worg_large.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "precache": {"model": "models/creeps/neutral_creeps/n_creep_worg_large/n_creep_worg_large.vmdl"}, "OriginName": "npc_dota_neutral_alpha_wolf", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.Worg", "DeathSound": "Fw.Cards.Creep.Default.Death.small2"}, "default_creep_default_giant_wolf": {"Template": "creep_default_giant_wolf", "SpecialName": "default", "Model": "models/creeps/neutral_creeps/n_creep_worg_small/n_creep_worg_small.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "precache": {"model": "models/creeps/neutral_creeps/n_creep_worg_small/n_creep_worg_small.vmdl"}, "OriginName": "npc_dota_neutral_giant_wolf", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.Worg", "DeathSound": "Fw.Cards.Creep.Default.Death.small2"}, "default_creep_default_polar_furbolg_ursa_warrior": {"Template": "creep_default_polar_furbolg_ursa_warrior", "SpecialName": "default", "Model": "models/creeps/neutral_creeps/n_creep_furbolg/n_creep_furbolg_disrupter.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "precache": {"model": "models/creeps/neutral_creeps/n_creep_furbolg/n_creep_furbolg_disrupter.vmdl"}, "OriginName": "npc_dota_neutral_polar_furbolg_ursa_warrior", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.furbolg_ursa", "DeathSound": 0}, "default_creep_default_polar_furbolg_champion": {"Template": "creep_default_polar_furbolg_champion", "SpecialName": "default", "Model": "models/creeps/neutral_creeps/n_creep_beast/n_creep_beast.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "precache": {"model": "models/creeps/neutral_creeps/n_creep_beast/n_creep_beast.vmdl"}, "OriginName": "npc_dota_neutral_polar_furbolg_champion", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.furbolg_champion", "DeathSound": 0}, "default_creep_default_ogre_magi": {"Template": "creep_default_ogre_magi", "SpecialName": "default", "Model": "models/creeps/neutral_creeps/n_creep_ogre_lrg/n_creep_ogre_lrg.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "precache": {"model": "models/creeps/neutral_creeps/n_creep_ogre_lrg/n_creep_ogre_lrg.vmdl"}, "OriginName": "npc_dota_neutral_ogre_magi", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.ogre", "DeathSound": 0}, "default_creep_default_ogre_mauler": {"Template": "creep_default_ogre_mauler", "SpecialName": "default", "Model": "models/creeps/neutral_creeps/n_creep_ogre_med/n_creep_ogre_med.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "precache": {"model": "models/creeps/neutral_creeps/n_creep_ogre_med/n_creep_ogre_med.vmdl"}, "OriginName": "npc_dota_neutral_ogre_mauler", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.ogre", "DeathSound": 0}, "default_creep_default_elder_jungle_stalker": {"Template": "creep_default_elder_jungle_stalker", "SpecialName": "default", "Model": "models/creeps/neutral_creeps/n_creep_gargoyle/n_creep_gargoyle.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "precache": {"model": "models/creeps/neutral_creeps/n_creep_gargoyle/n_creep_gargoyle.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.stalker", "DeathSound": 0}, "default_creep_default_jungle_stalker": {"Template": "creep_default_jungle_stalker", "SpecialName": "default", "Model": "models/creeps/neutral_creeps/n_creep_gargoyle/n_creep_gargoyle.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "precache": {"model": "models/creeps/neutral_creeps/n_creep_gargoyle/n_creep_gargoyle.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.stalker", "DeathSound": 0}, "default_creep_default_prowler_acolyte": {"Template": "creep_default_prowler_acolyte", "SpecialName": "default", "Model": "models/creeps/neutral_creeps/n_creep_satyr_spawn_a/n_creep_satyr_spawn_b.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "precache": {"model": "models/creeps/neutral_creeps/n_creep_satyr_spawn_a/n_creep_satyr_spawn_b.vmdl"}, "OriginName": "npc_dota_neutral_prowler_shaman", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.satyr", "DeathSound": 0}, "default_creep_default_prowler_shaman": {"Template": "creep_default_prowler_shaman", "SpecialName": "default", "Model": "models/creeps/neutral_creeps/n_creep_satyr_spawn_a/n_creep_satyr_spawn_a.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "precache": {"model": "models/creeps/neutral_creeps/n_creep_satyr_spawn_a/n_creep_satyr_spawn_a.vmdl"}, "OriginName": "npc_dota_neutral_prowler_shaman", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.satyr", "DeathSound": 0}, "default_creep_default_big_thunder_lizard": {"Template": "creep_default_big_thunder_lizard", "SpecialName": "default", "Model": "models/creeps/neutral_creeps/n_creep_thunder_lizard/n_creep_thunder_lizard_big.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": "particles/neutral_fx/thunderlizard_base_attack.vpcf", "precache": {"model": "models/creeps/neutral_creeps/n_creep_thunder_lizard/n_creep_thunder_lizard_big.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.lizard_big", "DeathSound": 0}, "default_creep_default_small_thunder_lizard": {"Template": "creep_default_small_thunder_lizard", "SpecialName": "default", "Model": "models/creeps/neutral_creeps/n_creep_thunder_lizard/n_creep_thunder_lizard_small.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": "particles/neutral_fx/thunderlizard_base_attack.vpcf", "precache": {"model": "models/creeps/neutral_creeps/n_creep_thunder_lizard/n_creep_thunder_lizard_small.vmdl"}, "OriginName": "npc_dota_neutral_small_thunder_lizard", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.lizard_small", "DeathSound": 0}, "default_creep_default_black_dragon": {"Template": "creep_default_black_dragon", "SpecialName": "default", "Model": "models/creeps/neutral_creeps/n_creep_black_dragon/n_creep_black_dragon.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": "particles/neutral_fx/black_dragon_attack.vpcf", "precache": {"model": "models/creeps/neutral_creeps/n_creep_black_dragon/n_creep_black_dragon.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.blackdragon", "DeathSound": 0}, "default_creep_default_ice_shaman": {"Template": "creep_default_ice_shaman", "SpecialName": "default", "Model": "models/creeps/ice_biome/giant/ice_giant01.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": "particles/units/heroes/hero_witchdoctor/witchdoctor_base_attack.vpcf", "precache": {"model": "models/creeps/ice_biome/giant/ice_giant01.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.ice_shaman", "DeathSound": 0}, "default_creep_default_forest_troll_high_priest": {"Template": "creep_default_forest_troll_high_priest", "SpecialName": "default", "Model": "models/creeps/neutral_creeps/n_creep_forest_trolls/n_creep_forest_troll_high_priest.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": "particles/units/heroes/hero_witchdoctor/witchdoctor_base_attack.vpcf", "precache": {"model": "models/creeps/neutral_creeps/n_creep_forest_trolls/n_creep_forest_troll_high_priest.vmdl"}, "OriginName": "npc_dota_neutral_forest_troll_high_priest", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.small2", "DeathSound": "Fw.Cards.Creep.Default.Death.small"}, "default_creep_default_forest_troll_berserker": {"Template": "creep_default_forest_troll_berserker", "SpecialName": "default", "Model": "models/creeps/neutral_creeps/n_creep_forest_trolls/n_creep_forest_troll_berserker.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": "particles/units/heroes/hero_witchdoctor/witchdoctor_base_attack.vpcf", "precache": {"model": "models/creeps/neutral_creeps/n_creep_forest_trolls/n_creep_forest_troll_berserker.vmdl"}, "OriginName": "npc_dota_neutral_forest_troll_berserker", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.small2", "DeathSound": "Fw.Cards.Creep.Default.Death.small"}, "default_creep_default_roshan": {"Template": "creep_default_roshan", "SpecialName": "default", "Model": "models/creeps/roshan/roshan.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": "particles/units/heroes/hero_witchdoctor/witchdoctor_base_attack.vpcf", "precache": {"model": "models/creeps/roshan/roshan.vmdl"}, "OriginName": "npc_dota_hero_roshan", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Roshan.Entrance", "DeathSound": "Fw.Cards.Creep.Roshan.Death"}, "default_creep_default_viper": {"Template": "creep_default_viper", "SpecialName": "default", "Model": "models/heroes/viper/viper.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 519}, "2": {"ItemDef": 611}, "3": {"ItemDef": 623}, "4": {"ItemDef": 654}, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": 0, "precache": {"model": "models/heroes/viper/viper.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.viper.Entrance.Default", "DeathSound": "Fw.Cards.Hero.viper.<PERSON>.Default"}, "default_creep_default_gyrocopter": {"Template": "creep_default_gyrocopter", "SpecialName": "default", "Model": "models/heroes/gyro/gyro.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 176}, "2": {"ItemDef": 177}, "3": {"ItemDef": 178}, "4": {"ItemDef": 179}, "5": {"ItemDef": 131}, "6": {"ItemDef": 527}, "7": {"ItemDef": 126}, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": 0, "precache": {"model": "models/heroes/gyro/gyro.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.gyrocopter.Entrance.Default", "DeathSound": "Fw.Cards.Hero.gyrocopter.Death.Default"}, "default_creep_default_ghost": {"Template": "creep_default_ghost", "SpecialName": "default", "Model": "models/creeps/neutral_creeps/n_creep_ghost_a/n_creep_ghost_a.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": "particles/neutral_fx/ghost_base_attack.vpcf", "precache": {"model": "models/creeps/neutral_creeps/n_creep_ghost_a/n_creep_ghost_a.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.ghost", "DeathSound": 0}, "default_creep_default_dark_troll": {"Template": "creep_default_dark_troll", "SpecialName": "default", "Model": "models/creeps/neutral_creeps/n_creep_troll_dark_a/n_creep_troll_dark_a.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": "particles/base_attacks/ranged_badguy.vpcf", "precache": {"model": "models/creeps/neutral_creeps/n_creep_troll_dark_a/n_creep_troll_dark_a.vmdl"}, "OriginName": "npc_dota_neutral_dark_troll", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.small3", "DeathSound": 0}, "default_creep_default_dark_troll_warlord": {"Template": "creep_default_dark_troll_warlord", "SpecialName": "default", "Model": "models/creeps/neutral_creeps/n_creep_troll_dark_b/n_creep_troll_dark_b.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": "particles/base_attacks/ranged_badguy.vpcf", "precache": {"model": "models/creeps/neutral_creeps/n_creep_troll_dark_b/n_creep_troll_dark_b.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.small3", "DeathSound": 0}, "default_creep_default_black_drake": {"Template": "creep_default_black_drake", "SpecialName": "default", "Model": "models/creeps/neutral_creeps/n_creep_black_drake/n_creep_black_drake.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": "particles/neutral_fx/black_drake_attack.vpcf", "precache": {"model": "models/creeps/neutral_creeps/n_creep_black_drake/n_creep_black_drake.vmdl"}, "OriginName": "npc_dota_neutral_black_drake", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.blackdrake", "DeathSound": 0}, "default_creep_default_techies_bomb": {"Template": "creep_default_techies_bomb", "SpecialName": "default", "Model": "models/heroes/techies/fx_techies_remote_cart.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": 0, "precache": {"model": "models/heroes/techies/fx_techies_remote_cart.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Entrance.Default.techies_bomb", "DeathSound": 0}, "default_creep_default_shaman": {"Template": "creep_default_shaman", "SpecialName": "default", "Model": "models/heroes/shadowshaman/shadowshaman.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 246}, "2": {"ItemDef": 247}, "3": {"ItemDef": 248}, "4": {"ItemDef": 249}, "5": {"ItemDef": 250}, "6": {"ItemDef": 251}, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": "particles/units/heroes/hero_shadowshaman/shadowshaman_base_attack.vpcf", "precache": {"model": "models/heroes/shadowshaman/shadowshaman.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.shaman.Entrance.Default", "DeathSound": "Fw.Cards.Hero.shaman.<PERSON>.Default"}, "default_creep_default_crystal_maiden": {"Template": "creep_default_crystal_maiden", "SpecialName": "default", "Model": "models/heroes/crystal_maiden/crystal_maiden.vmdl", "MinimapIcon": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 38}, "2": {"ItemDef": 39}, "3": {"ItemDef": 40}, "4": {"ItemDef": 41}, "5": {"ItemDef": 311}, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "ProjectileModel": "particles/units/heroes/hero_crystalmaiden/maiden_base_attack.vpcf", "precache": {"model": "models/heroes/crystal_maiden/crystal_maiden.vmdl"}, "OriginName": "npc_dota_hero_default", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.cm.Entrance.Default", "DeathSound": "Fw.Cards.Hero.cm.<PERSON>.Default"}}