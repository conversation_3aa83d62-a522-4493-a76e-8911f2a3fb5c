import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";
import { MathUtils } from "../../../utils/math_utils";
import { modifier_fw_unit_luna_hero } from "../unit_innate/modifier_fw_unit_luna_hero";


export class modifier_fw_luna_lunar_grace extends BaseModifier {
    

    GetAttributes(): ModifierAttribute {
        return 1 + 4
    }

    par:ParticleID;
    max_radius:number;
    min_radius:number;
    min_attack:number;
    max_attack:number;
    extra_attack:number;
    OnCreated(keys:any): void {
        let ab = this.GetAbility()
        if (IsServer()) {
            let unit = this.GetParent()
            this.par = ParticleManager.CreateParticle("particles/spell/luna_lunar_grace_aura/mirana_solar_blessing_buff_hands_glow.vpcf",ParticleAttachment_t.PATTACH_ABSORIGIN, unit)
            ParticleManager.SetParticleControlEnt(this.par, 0, unit, ParticleAttachment_t.PATTACH_POINT_FOLLOW, "attach_hitloc", Vector(0,0,0), true)
        }
        this.max_radius = ab.GetSpecialValueFor("max_radius")
        this.min_radius = ab.GetSpecialValueFor("min_radius")
        this.min_attack = ab.GetSpecialValueFor("min_attack")
        this.max_attack = ab.GetSpecialValueFor("max_attack")
        this.extra_attack = ab.GetSpecialValueFor("extra_attack")
    } 
    OnDestroy(): void {
        if (IsServer()) {
            ParticleManager.DestroyParticle(this.par,true)
        }
    }

    IsHidden() {
        return false;
    }

    RemoveOnDeath(): boolean {
        return true
    }

    GetModifierPreAttack_BonusDamage(): number {
        let u = this.GetCaster()
        let extra = 0
        if (u.HasModifier(modifier_fw_unit_luna_hero.name) && this.GetParent().entindex() != u.entindex()) {
            extra = u.GetModifierStackCount(modifier_fw_unit_luna_hero.name, u) * this.extra_attack
        }
        if (u.PassivesDisabled()) {
            return 0
        } else {
            return MathUtils.numInRange(this.GetParent().GetAbsOrigin().__sub(u.GetAbsOrigin()).Length2D(),this.min_radius,this.max_radius,this.max_attack,this.min_attack) + extra
        }
    }

    DeclareFunctions(): ModifierFunction[] {
        return [
            7,
        ];
    }
    
}
