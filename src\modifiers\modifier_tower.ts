import { BaseModifier, registerModifier } from "../utils/dota_ts_adapter";


export class modifier_tower extends BaseModifier {
    
    num:number;
    OnCreated(keys:any): void {
        if (IsServer()) {
            this.num = keys.bounsRange-100
        } 
    } 

    OnDestroy(){

    }

    IsHidden() {
        return true;
    }

    AllowIllusionDuplicate () {
        return true;
    }
    
    CheckState() {
        return {
            // [6]: true,
        }   
    }

    GetModifierAttackRangeBonus(): number {
        return this.num
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            12,
            // 2,
            ];
    }

   
    
}
