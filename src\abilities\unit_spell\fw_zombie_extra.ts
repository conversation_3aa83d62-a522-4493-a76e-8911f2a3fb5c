import { modifier_fw_summon_zombie } from "../../modifiers/abilities/unit_spell/modifier_fw_summon_zombie";
import { modifier_fw_zombie_extra } from "../../modifiers/abilities/unit_spell/modifier_fw_zombie_extra";
import { BaseAbility, registerAbility } from "../../utils/dota_ts_adapter";
import { MathUtils } from "../../utils/math_utils";

export class fw_zombie_extra extends BaseAbility {
    
    GetIntrinsicModifierName(): string {
        return modifier_fw_zombie_extra.name
    }
    
    Precache(context: CScriptPrecacheContext): void {
       
    }
}

