import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";
import { modifier_fw_luna_lunar_grace } from "./modifier_fw_luna_lunar_grace";


export class modifier_fw_luna_lunar_grace_aura extends BaseModifier {
    
    par:ParticleID;
    max_radius:number;
    time:number;
    OnCreated(keys:any): void {
        if (IsServer()) {
            let hero = this.GetParent()
            this.par = ParticleManager.CreateParticle("particles/spell/luna_lunar_grace_aura/aura_ground.vpcf", ParticleAttachment_t.PATTACH_ABSORIGIN_FOLLOW, hero)
            ParticleManager.SetParticleControl(this.par, 0, hero.GetAbsOrigin())
        }
        let ab = this.GetAbility()
        this.max_radius = ab.GetSpecialValueFor("max_radius")
        this.time = ab.GetSpecialValueFor("time")
    } 
    OnDestroy(): void {
        if (IsServer()) {
            ParticleManager.DestroyParticle(this.par, true)
        }
    }

    IsHidden() {
        return true;
    }

    IsAura(): boolean {
        return true
    }

    GetAuraRadius(): number {
        return this.max_radius
    }

    GetAuraDuration(): number {
        return this.time
    }

    GetAuraSearchFlags(): DOTA_UNIT_TARGET_FLAGS {
        return DOTA_UNIT_TARGET_FLAGS.DOTA_UNIT_TARGET_FLAG_NONE
    }

    GetAuraSearchTeam(): DOTA_UNIT_TARGET_TEAM {
        return DOTA_UNIT_TARGET_TEAM.DOTA_UNIT_TARGET_TEAM_FRIENDLY
    }

    GetAuraSearchType(): DOTA_UNIT_TARGET_TYPE {
        return DOTA_UNIT_TARGET_TYPE.DOTA_UNIT_TARGET_HERO + DOTA_UNIT_TARGET_TYPE.DOTA_UNIT_TARGET_CREEP
    }

    IsAuraActiveOnDeath(): boolean {
        return false
    }

    GetModifierAura(): string {
        return modifier_fw_luna_lunar_grace.name
    }

    RemoveOnDeath(): boolean {
        return true
    }
    
}


// import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";
// import { modifier_fw_luna_lunar_grace } from "./modifier_fw_luna_lunar_grace";


// // export class modifier_fw_luna_lunar_grace_aura extends BaseModifier {
    
//     par:ParticleID;
//     max_radius:number;
//     min_radius:number;
//     min_attack:number;
//     max_attack:number;
//     time:number;
//     OnCreated(keys:any): void {
//         if (IsServer()) {
//             let hero = this.GetParent()
//             this.par = ParticleManager.CreateParticle("particles/spell/luna_lunar_grace_aura/aura_ground.vpcf", ParticleAttachment_t.PATTACH_ABSORIGIN_FOLLOW, hero)
//             ParticleManager.SetParticleControl(this.par, 0, hero.GetAbsOrigin())
//             this.StartIntervalThink(0.1)
//         }
//         let ab = this.GetAbility()
//         this.max_radius = ab.GetSpecialValueFor("max_radius")
//         this.min_radius = ab.GetSpecialValueFor("min_radius")
//         this.min_attack = ab.GetSpecialValueFor("min_attack")
//         this.max_attack = ab.GetSpecialValueFor("max_attack")
//         this.time = ab.GetSpecialValueFor("time")


//     } 
//     OnDestroy(): void {
//         if (IsServer()) {
//             ParticleManager.DestroyParticle(this.par, false)
//         }
//     }

//     IsHidden() {
//         return false;
//     }

//     OnIntervalThink(): void {
//         let hero = this.GetParent()
//         let ab = this.GetAbility()
//         let tars = FindUnitsInRadius(
//             hero.GetTeam(),
//             hero.GetAbsOrigin(),
//             undefined,
//             this.max_radius ,
//             DOTA_UNIT_TARGET_TEAM.DOTA_UNIT_TARGET_TEAM_FRIENDLY,
//             1 + 2,
//             DOTA_UNIT_TARGET_FLAGS.DOTA_UNIT_TARGET_FLAG_NONE,
//             0,
//             false,
//         )
//         for (const tar of tars) {
//             if (tar.fastWarUnitType != GoFastWarAIUnitTypeEnum.BUILDING) {
//                 tar.AddNewModifier(hero,ab,modifier_fw_luna_lunar_grace.name,{duration:this.time})
//             }
//         }
//     }

//     RemoveOnDeath(): boolean {
//         return true
//     }
// }
