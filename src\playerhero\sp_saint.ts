import { PlayerHeroSpellHandle } from "./playerherospellhandle";


export class PlayerHeroSaint extends PlayerHeroSpellHandle {



    private lv1 = 3
    private lv2 = 6
    private coolTime = 5
    private ab:CDOTABaseAbility;
    AfterSpellUsing(data:{}): void {
        let cardUsesNum = this.nowSpellInfo.usesNum
        if (cardUsesNum == this.lv2) {
            this.cardInfo = GameRules.KVUtils.getCardsInfo(this.spellInfo.CardIndex[2])
            let sp = GameRules.KVUtils.getSpellInfo(this.cardInfo.SpellCardInfo!.SpellTemplate)
            this.nowSpellInfo = {
                HeroName: this.spellInfo.HeroName,
                HeroImage: this.spellInfo.HeroImage,
                SpellCardIndex: this.spellInfo.CardIndex[2],
                SpellName: sp.abName,
                cost: this.cardInfo.layCost,
                nextUsingTime: GameRules.GetGameTime() + sp.CoolDown,
                cooldown:sp.CoolDown,
                num:"",
                usesNum:0,
            }
        } else if (cardUsesNum == this.lv1) {
            this.cardInfo = GameRules.KVUtils.getCardsInfo(this.spellInfo.CardIndex[1])
            let sp = GameRules.KVUtils.getSpellInfo(this.cardInfo.SpellCardInfo!.SpellTemplate)
            this.nowSpellInfo = {
                HeroName: this.spellInfo.HeroName,
                HeroImage: this.spellInfo.HeroImage,
                SpellCardIndex: this.spellInfo.CardIndex[1],
                SpellName: sp.abName,
                cost: this.cardInfo.layCost,
                nextUsingTime: GameRules.GetGameTime() + sp.CoolDown,
                cooldown:sp.CoolDown,
                num:"0/"+(this.lv2-this.lv1),
                usesNum:0,
            }
        } else if (cardUsesNum < this.lv2 && cardUsesNum > this.lv1) {
            this.nowSpellInfo.num = (cardUsesNum-this.lv1) + "/" + (this.lv2-this.lv1)
        } else if (cardUsesNum < this.lv1) {
            this.nowSpellInfo.num = (cardUsesNum) + "/" + (this.lv1)
        }
    }

    AfterEnemySpellUsing (data:{playerId:PlayerID,info:PlayerHeroForClient, UsingCardType:string}) {
        return this.dealSilence(data)
    }

    AfterEnemyCardUsing (data:{playerId:PlayerID,info:PlayerHeroForClient, UsingCardType:string}) {
        return this.dealSilence(data)
    }

    private dealSilence (data:{playerId:PlayerID,info:PlayerHeroForClient, UsingCardType:string}) {
        if (this.ab == undefined) {
            this.ab = this.hero.FindAbilityByName("fw_hero_spell_saint_lv3")
        }
        if (this.nowSpellInfo.usesNum >= this.lv2 && this.nowSpellInfo.nextUsingTime <= GameRules.GetGameTime() && data.UsingCardType == "spell") {
            // let u = PlayerResource.GetPlayer(data.playerId).GetAssignedHero()
            // print("触发"+this.playerId+"的技能，沉默玩家"+data.playerId+","+u.GetUnitName())
            let player = PlayerResource.GetPlayer(data.playerId)
            Timers.CreateTimer(0.1,()=>{
                player.GetAssignedHero().AddNewModifier(this.hero, this.ab, "modifier_fw_hero_spell_saint", {duration:this.coolTime})
            })
            CustomGameEventManager.Send_ServerToPlayer(player, "go_ui_sound",{
                soundNames:"Fw.Hero.Spell.saint.passive",
                group:4,
                interval:2,
            }) 
            let particle = ParticleManager.CreateParticleForPlayer(
                "particles/hud/silence_notification/screen_damage_indicator.vpcf",
                ParticleAttachment_t.PATTACH_WORLDORIGIN,
                undefined,
                player
            );
            ParticleManager.SetParticleControl(particle, 0, Vector(1,0,0));
        }
        return data.info
    }

    private cardInfo:CardInfoKV;
    constructor (playerId:PlayerID,spellInfo:PlayerHeroForKV
    ) {
        super(playerId,spellInfo)

        this.cardInfo = GameRules.KVUtils.getCardsInfo(spellInfo.CardIndex[0])
        let sp = GameRules.KVUtils.getSpellInfo(spellInfo.SpellName)
        this.nowSpellInfo = {
            HeroName: spellInfo.HeroName,
            HeroImage: spellInfo.HeroImage,
            SpellCardIndex: spellInfo.CardIndex[0],
            SpellName: spellInfo.SpellName,
            cost: this.cardInfo.layCost,
            nextUsingTime: GameRules.GetGameTime() + sp.CoolDown,
            cooldown:sp.CoolDown,
            num:"",
            usesNum:0,
        }
    }

}