import React from 'react';
import 'panorama-polyfill-x/lib/console';
import 'panorama-polyfill-x/lib/timers';
import { render } from 'react-panorama-x';

// 类型定义
interface CardTooltipProps {
  heroId?: string;
  heroLevel?: number;
}

// 卡牌工具提示组件
function CardTooltip(props: CardTooltipProps): JSX.Element {
  const { heroId, heroLevel } = props;
  
  return (
    <Panel id="CardTooltipContainer">
      <Panel className="tooltip-content">
        <Panel className="tooltip-header">
          <Label text={`Hero: ${heroId || 'Unknown'}`} />
          <Label text={`Level: ${heroLevel || 1}`} />
        </Panel>
        
        <Panel className="tooltip-body">
          <Panel className="hero-info">
            <Panel className="hero-image">
              {/* 英雄头像 */}
              <DOTAHeroImage heroname={heroId || 'elder_titan'} />
            </Panel>
            
            <Panel className="hero-details">
              <Label className="hero-name" text={heroId || 'Elder Titan'} />
              <Label className="hero-level" text={`Level ${heroLevel || 1}`} />
            </Panel>
          </Panel>
          
          <Panel className="tooltip-description">
            <Label text="Card tooltip description goes here..." />
          </Panel>
        </Panel>
      </Panel>
    </Panel>
  );
}

// 工具提示加载事件处理
$.GetContextPanel().SetPanelEvent('ontooltiploaded', () => {
  $.Msg('ontooltiploaded');
  
  // 获取属性
  const heroId = $.GetContextPanel().GetAttributeString('heroId', 'elder_titan');
  const heroLevel = Number($.GetContextPanel().GetAttributeString('heroLevel', '1'));
  
  // 渲染 React 组件
  render(
    <CardTooltip heroId={heroId} heroLevel={heroLevel} />,
    $.GetContextPanel()
  );
});

export {};
