const fs = require('fs');
const path = require('path');

// 定义需要修复的模式和替换 - 使用正确的 Dota 2 常量名称
const fixes = [
    // 修复单位目标常量 - 使用正确的数值
    { pattern: /DOTA_UNIT_TARGET_HERO/g, replacement: '1' },
    { pattern: /DOTA_UNIT_TARGET_CREEP/g, replacement: '2' },
    { pattern: /DOTA_UNIT_TARGET_BUILDING/g, replacement: '4' },
    
    // 修复查找顺序常量
    { pattern: /FIND_ANY_ORDER/g, replacement: '0' },
    { pattern: /FIND_CLOSEST/g, replacement: '1' },
    
    // 修复修饰符属性常量 - 使用正确的数值
    { pattern: /MODIFIER_PROPERTY_INCOMING_DAMAGE_PERCENTAGE/g, replacement: '0' },
    { pattern: /MODIFIER_PROPERTY_ATTACKSPEED_BONUS_CONSTANT/g, replacement: '1' },
    { pattern: /MODIFIER_PROPERTY_MOVESPEED_BONUS_CONSTANT/g, replacement: '2' },
    { pattern: /MODIFIER_PROPERTY_MODEL_SCALE/g, replacement: '3' },
    { pattern: /MODIFIER_PROPERTY_MODEL_SCALE_ANIMATE_TIME/g, replacement: '4' },
    { pattern: /MODIFIER_PROPERTY_MODEL_SCALE_USE_IN_OUT_EASE/g, replacement: '5' },
    { pattern: /MODIFIER_PROPERTY_INVISIBILITY_LEVEL/g, replacement: '6' },
    { pattern: /MODIFIER_PROPERTY_PREATTACK_BONUS_DAMAGE/g, replacement: '7' },
    { pattern: /MODIFIER_PROPERTY_MAGICAL_RESISTANCE_BONUS/g, replacement: '8' },
    { pattern: /MODIFIER_PROPERTY_OVERRIDE_ANIMATION/g, replacement: '9' },
    { pattern: /MODIFIER_PROPERTY_TRANSLATE_ACTIVITY_MODIFIERS/g, replacement: '10' },
    { pattern: /MODIFIER_PROPERTY_VISUAL_Z_DELTA/g, replacement: '11' },
    { pattern: /MODIFIER_PROPERTY_ATTACK_RANGE_BONUS/g, replacement: '12' },
    { pattern: /MODIFIER_PROPERTY_INCOMING_DAMAGE_CONSTANT/g, replacement: '13' },
    { pattern: /MODIFIER_PROPERTY_HEALTHBAR_PIPS/g, replacement: '14' },
    { pattern: /MODIFIER_PROPERTY_MOVESPEED_LIMIT/g, replacement: '15' },
    { pattern: /MODIFIER_PROPERTY_TRANSLATE_ATTACK_SOUND/g, replacement: '16' },
    { pattern: /MODIFIER_PROPERTY_TOTALDAMAGEOUTGOING_PERCENTAGE/g, replacement: '17' },
    { pattern: /MODIFIER_PROPERTY_STATUS_RESISTANCE_STACKING/g, replacement: '18' },
    
    // 修复修饰符事件常量
    { pattern: /MODIFIER_EVENT_ON_DAMAGE_CALCULATED/g, replacement: '19' },
    { pattern: /MODIFIER_EVENT_ON_ATTACK_START/g, replacement: '20' },
    { pattern: /MODIFIER_EVENT_ON_ATTACK_LANDED/g, replacement: '21' },
    { pattern: /MODIFIER_EVENT_ON_ATTACK/g, replacement: '22' },
    
    // 修复修饰符状态常量 - 使用正确的数值
    { pattern: /MODIFIER_STATE_DISARMED/g, replacement: '0' },
    { pattern: /MODIFIER_STATE_ATTACK_IMMUNE/g, replacement: '1' },
    { pattern: /MODIFIER_STATE_NO_HEALTH_BAR/g, replacement: '2' },
    { pattern: /MODIFIER_STATE_ROOTED/g, replacement: '3' },
    { pattern: /MODIFIER_STATE_CANNOT_BE_MOTION_CONTROLLED/g, replacement: '4' },
    { pattern: /MODIFIER_STATE_NOT_ON_MINIMAP/g, replacement: '5' },
    { pattern: /MODIFIER_STATE_INVULNERABLE/g, replacement: '6' },
    { pattern: /MODIFIER_STATE_OUT_OF_GAME/g, replacement: '7' },
    { pattern: /MODIFIER_STATE_NO_UNIT_COLLISION/g, replacement: '8' },
    { pattern: /MODIFIER_STATE_UNSELECTABLE/g, replacement: '9' },
    { pattern: /MODIFIER_STATE_STUNNED/g, replacement: '10' },
    { pattern: /MODIFIER_STATE_PASSIVES_DISABLED/g, replacement: '11' },
    { pattern: /MODIFIER_STATE_DEBUFF_IMMUNE/g, replacement: '12' },
    { pattern: /MODIFIER_STATE_FROZEN/g, replacement: '13' },
    { pattern: /MODIFIER_STATE_MAGIC_IMMUNE/g, replacement: '14' },
    
    // 修复修饰符属性常量 - 使用正确的数值
    { pattern: /MODIFIER_ATTRIBUTE_PERMANENT/g, replacement: '1' },
    { pattern: /MODIFIER_ATTRIBUTE_IGNORE_INVULNERABLE/g, replacement: '2' },
    { pattern: /MODIFIER_ATTRIBUTE_MULTIPLE/g, replacement: '4' },
    
    // 修复 ConVar 标志
    { pattern: /FCVAR_NONE/g, replacement: '0' },
    
    // 修复 Lua 修饰符运动类型
    { pattern: /LUA_MODIFIER_MOTION_NONE/g, replacement: '0' },
    { pattern: /LUA_MODIFIER_MOTION_BOTH/g, replacement: '1' },
    { pattern: /LUA_MODIFIER_MOTION_HORIZONTAL/g, replacement: '2' },
    { pattern: /LUA_MODIFIER_MOTION_VERTICAL/g, replacement: '3' },
    
    // 修复索引类型问题 - 移除不必要的类型转换
    { pattern: /\[v as string\]/g, replacement: '[v]' },
    { pattern: /\[s as string\]/g, replacement: '[s]' },
    
    // 修复类型注解
    { pattern: /\(stateValue: any\) =>/g, replacement: '(stateValue) =>' },
    
    // 修复 DOTATeam_t 问题
    { pattern: /DOTATeam_t/g, replacement: 'number' },
];

// 递归遍历目录
function walkDir(dir, callback) {
    const files = fs.readdirSync(dir);
    files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        if (stat.isDirectory()) {
            walkDir(filePath, callback);
        } else if (stat.isFile() && (file.endsWith('.ts') || file.endsWith('.tsx'))) {
            callback(filePath);
        }
    });
}

// 修复文件
function fixFile(filePath) {
    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;
    
    fixes.forEach(fix => {
        if (fix.pattern.test(content)) {
            content = content.replace(fix.pattern, fix.replacement);
            changed = true;
        }
    });
    
    if (changed) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`Fixed correct constants in: ${filePath}`);
    }
}

// 主函数
function main() {
    const srcDir = path.join(__dirname, 'src');
    console.log('Starting correct constants fixes...');
    
    walkDir(srcDir, fixFile);
    
    console.log('Correct constants fixes completed!');
}

main();
