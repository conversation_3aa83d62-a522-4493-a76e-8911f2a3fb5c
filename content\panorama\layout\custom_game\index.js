// 主入口文件 - 整合所有 Panorama 模块
// 导入工具模块
import './utils/event-bus';
import './utils/x-nettable-dispatcher';
// 导入各个页面脚本
import './hud/script';
import './loading-screen/script';
import './tooltip/script';
// 导入 fast_war 游戏模块
import './fast_war';
// 全局初始化
console.log('Panorama TypeScript modules loaded successfully');
// 导出主要的工具函数供其他模块使用
export { emitLocalEvent, onLocalEvent, emitGameEvent, onGameEvent, cleanup } from './utils/event-bus';
export { isEqual, dispatch, getCachedValue, getCachedTable, clearTableCache, clearAllCache } from './utils/x-nettable-dispatcher';
export default {
    version: '1.0.0',
    description: 'Fast War Panorama UI - TypeScript Edition',
    modules: [
        'event-bus',
        'x-nettable-dispatcher',
        'hud-script',
        'loading-screen',
        'tooltip',
        'fast-war'
    ]
};
//# sourceMappingURL=index.js.map