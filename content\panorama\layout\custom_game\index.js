"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.clearAllCache = exports.clearTableCache = exports.getCachedTable = exports.getCachedValue = exports.dispatch = exports.isEqual = exports.cleanup = exports.onGameEvent = exports.emitGameEvent = exports.onLocalEvent = exports.emitLocalEvent = void 0;
require("./utils/event-bus");
require("./utils/x-nettable-dispatcher");
require("./hud/script");
require("./loading-screen/script");
require("./tooltip/script");
require("./fast_war");
console.log('Panorama TypeScript modules loaded successfully');
var event_bus_1 = require("./utils/event-bus");
Object.defineProperty(exports, "emitLocalEvent", { enumerable: true, get: function () { return event_bus_1.emitLocalEvent; } });
Object.defineProperty(exports, "onLocalEvent", { enumerable: true, get: function () { return event_bus_1.onLocalEvent; } });
Object.defineProperty(exports, "emitGameEvent", { enumerable: true, get: function () { return event_bus_1.emitGameEvent; } });
Object.defineProperty(exports, "onGameEvent", { enumerable: true, get: function () { return event_bus_1.onGameEvent; } });
Object.defineProperty(exports, "cleanup", { enumerable: true, get: function () { return event_bus_1.cleanup; } });
var x_nettable_dispatcher_1 = require("./utils/x-nettable-dispatcher");
Object.defineProperty(exports, "isEqual", { enumerable: true, get: function () { return x_nettable_dispatcher_1.isEqual; } });
Object.defineProperty(exports, "dispatch", { enumerable: true, get: function () { return x_nettable_dispatcher_1.dispatch; } });
Object.defineProperty(exports, "getCachedValue", { enumerable: true, get: function () { return x_nettable_dispatcher_1.getCachedValue; } });
Object.defineProperty(exports, "getCachedTable", { enumerable: true, get: function () { return x_nettable_dispatcher_1.getCachedTable; } });
Object.defineProperty(exports, "clearTableCache", { enumerable: true, get: function () { return x_nettable_dispatcher_1.clearTableCache; } });
Object.defineProperty(exports, "clearAllCache", { enumerable: true, get: function () { return x_nettable_dispatcher_1.clearAllCache; } });
exports.default = {
    version: '1.0.0',
    description: 'Fast War Panorama UI - TypeScript Edition',
    modules: [
        'event-bus',
        'x-nettable-dispatcher',
        'hud-script',
        'loading-screen',
        'tooltip',
        'fast-war'
    ]
};
