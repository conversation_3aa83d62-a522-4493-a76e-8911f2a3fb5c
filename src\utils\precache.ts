/** @noSelfInFile */
// 导出的预载入方法，用来给addon_game_mode.ts调用
export default function Precache(context: CScriptPrecacheContext) {
    // 需要预载的所有资源
    precacheResource(
        [
            // '***.vpcf',
            'soundevents/custom_sounds.vsndevts',
            // '***.vmdl',
           
            'models/props_structures/tower_good.vmdl',
            'particles/base_attacks/ranged_tower_good.vpcf',
            'particles/radiant_fx/radiant_tower002_destruction.vpcf',
            'particles/radiant_fx/tower_good3_lamp.vpcf',
            'models/props_structures/tower_bad.vmdl',
            'particles/base_attacks/ranged_tower_bad.vpcf',
            'particles/dire_fx/dire_tower002_destruction.vpcf',
            'particles/dire_fx/tower_bad_lamp.vpcf',
            'models/props_structures/radiant_ancient001.vmdl',
            'particles/radiant_fx2/radiant_ancient001_destruction.vpcf',
            'particles/radiant_fx2/good_ancient001_ambient.vpcf',
            'models/props_structures/bad_ancient002.vmdl',
            'particles/dire_fx/dire_ancient_base001_destruction.vpcf',
            'particles/dire_fx/bad_ancient_ambient.vpcf',
            
            "particles/status_fx/status_effect_faceless_chronosphere.vpcf",
            "models/heroes/pedestal/pedestal_2_ground_dire.vmdl",
            "particles/units/heroes/hero_leshrac/leshrac_split_earth.vpcf",
            "models/items/juggernaut/jugg_year_beast_slayer_weapon/jugg_year_beast_slayer_weapon.vmdl",
            "maps/backgrounds/models/fairy_showcase_hero_shield/fairy_showcase_hero_shield.vmdl",
          
            "particles/units/heroes/heroes_underlord/underlord_pitofmalice.vpcf",
            "particles/units/heroes/hero_undying/undying_tower_destruction.vpcf",
            "particles/units/heroes/hero_dark_seer/dark_seer_vacuum.vpcf",


            "particles/gui/card_range/card_range.vpcf",
            "particles/gui/choose_card/unit_one/model2.vpcf",
            "particles/map/in_game/cant_use_area/faceless_void_chronocube_edges.vpcf",
            "particles/gui/using_card/delay/overhead.vpcf",
            "particles/gui/card_scene/deny_spring_2021_bubble.vpcf",
            "particles/gui/battle_begin/ui_hero_level_5_icon_ambient.vpcf",
            "particles/hud/notification_screen/round_start/screen_killbanner_compendium14_triplekill.vpcf",
            "particles/hud/notification_screen/round_end/ct_win.vpcf",
            "particles/hud/player_spell/agh_aura_ti10_border.vpcf",
            "particles/gui/player_spell/emp/invoker_emp_charge.vpcf",
            "models/heroes/axe/axe_belt.vmdl",
            "models/heroes/axe/axe_ponytail.vmdl",
            "models/heroes/axe/axe_weapon.vmdl",
            "models/heroes/axe/axe_armor.vmdl",
            "models/heroes/juggernaut/juggernaut.vmdl",
            "models/heroes/juggernaut/jugg_bracers.vmdl",

            "particles/gui/choose_card/ab_1/ab_ring.vpcf",
            "particles/gui/choose_card/ab_2/pre_using_1.vpcf",
            "particles/gui/choose_card/ab_3/pre_using_2.vpcf",
            "particles/gui/choose_card/ab_4/pre_using_3.vpcf",
            "particles/gui/choose_card/ab_6/pre_using_5.vpcf",
            "particles/gui/choose_card/ab_7/pre_using.vpcf",
            "particles/gui/choose_card/ab_8/pre_using.vpcf",
            "particles/gui/choose_card/ab_9/pre_using.vpcf",
            "particles/gui/choose_card/ab_5/cm_persona_freezing_field_snow_frostglow.vpcf",
            "particles/gui/choose_card/ab_10/pre_using.vpcf",
            "particles/gui/choose_card/ab_11/pre_using.vpcf",

           "particles/gui/player_spell/yellow/hero_spell_preusing.vpcf",
           "particles/gui/player_spell/silence/silencer_last_word_status_2.vpcf",
           "particles/gui/player_spell/green/hero_spell_preusing.vpcf",
           "particles/gui/player_spell/white/hero_spell_preusing.vpcf",

           "particles/hud/damage_notification/screen_damage_indicator.vpcf",
           "particles/hud/silence_notification/screen_damage_indicator.vpcf",

        ],
        context
    );
    // 需要预载入的kv文件，会自动解析KV文件中的所有vpcf资源等等
    precacheEveryResourceInKV(
        [
            // kv文件路径
            // 'scripts/npc/go_units_custom.txt',
        ],
        context
    );
    // 需要预载入的单位
    precacheUnits(
        [
            // 单位名称
            // 'npc_dota_hero_***',
            // "default_creep_default_luna",
        ],
        context
    );
    // 需要预载入的物品
    precacheItems(
        [
            // 物品名称
            // 'item_***',
        ],
        context
    );
    print(`[Precache] Precache finished.`);
}

// 预载入KV文件中的所有资源
function precacheEveryResourceInKV(kvFileList: string[], context: CScriptPrecacheContext) {
    kvFileList.forEach(file => {
        const kvTable = LoadKeyValues(file);
        precacheEverythingFromTable(kvTable, context);
    });
}
// 预载入资源列表
function precacheResource(resourceList: string[], context: CScriptPrecacheContext) {
    resourceList.forEach(resource => {
        precacheResString(resource, context);
    });
}
function precacheResString(res: string, context: CScriptPrecacheContext) {
    // print("预载入："+res)
    if (res.endsWith('.vpcf')) {
        PrecacheResource('particle', res, context);
    } else if (res.endsWith('.vsndevts')) {
        PrecacheResource('soundfile', res, context);
    } else if (res.endsWith('.vmdl')) {
        PrecacheResource('model', res, context);
    }
}

// 预载入单位列表
function precacheUnits(unitNamesList: string[], context?: CScriptPrecacheContext) {
    if (context != null) {
        unitNamesList.forEach(unitName => {
            PrecacheUnitByNameSync(unitName, context);
        });
    } else {
        unitNamesList.forEach(unitName => {
            PrecacheUnitByNameAsync(unitName, () => {});
        });
    }
}
// 预载入物品列表
function precacheItems(itemList: string[], context: CScriptPrecacheContext) {
    itemList.forEach(itemName => {
        PrecacheItemByNameSync(itemName, context);
    });
}

// 一个辅助的，从KV表中解析出所有资源并预载入的方法
function precacheEverythingFromTable(kvTable: any, context: CScriptPrecacheContext) {
    for (const [k, v] of pairs(kvTable)) {
        if (type(v) === 'table') {
            precacheEverythingFromTable(v, context);
        } else if (type(v) === 'string') {
            precacheResString(v, context);
        }
    }
}
