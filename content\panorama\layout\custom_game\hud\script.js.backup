"use strict";
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
require("panorama-polyfill-x/lib/console");
require("panorama-polyfill-x/lib/timers");
var registFWEvents = {};
var FWEventKeys = {};
var index = 0;
GameUI.registFWEvent = function (eventName, callback) {
    if (registFWEvents[eventName] === undefined) {
        registFWEvents[eventName] = [];
    }
    registFWEvents[eventName].push(callback);
};
GameUI.dispatchFWEvent = function (eventName, key, data) {
    var i = key;
    if (i === -1) {
        i = index++;
    }
    if (FWEventKeys[i] === undefined || FWEventKeys[i].indexOf(eventName) === -1) {
        if (FWEventKeys[i] === undefined) {
            FWEventKeys[i] = [];
        }
        FWEventKeys[i].push(eventName);
        var callbacks = registFWEvents[eventName];
        if (callbacks !== undefined && callbacks.length > 0) {
            for (var _i = 0, callbacks_1 = callbacks; _i < callbacks_1.length; _i++) {
                var callback = callbacks_1[_i];
                callback(i, data);
            }
        }
    }
    if (key === -1) {
        FWEventKeys[i] = undefined;
    }
};
GameUI.SetHotKey = function (key, downCallback, upCallback) {
    var command = "DotaGoKey".concat(key).concat(Date.now());
    Game.CreateCustomKeyBind(key, "+".concat(command));
    if (downCallback !== undefined) {
        Game.AddCommand("+".concat(command), function () {
            if (!GameUI.IsAltDown()) {
                downCallback();
            }
        }, '', 1 << 32);
    }
    if (upCallback !== undefined) {
        Game.AddCommand("-".concat(command), function () {
            upCallback();
        }, '', 1 << 32);
    }
};
GameUI.GetTimeStr = function (seconds) {
    seconds = Math.round(seconds);
    var str = '';
    if (seconds >= 60) {
        var minutes = Math.round(seconds / 60);
        if (minutes * 60 > seconds) {
            minutes -= 1;
        }
        seconds -= minutes * 60;
        str += minutes + ':';
    }
    else {
        str += '0:';
    }
    if (seconds > 0 && seconds < 10) {
        str += '0' + seconds;
    }
    else if (seconds === 0) {
        str += '00';
    }
    else {
        str += seconds;
    }
    return str;
};
GameUI.GameTowerStatus = 0;
GameUI.nowChooseCardCost = 0;
GameUI.uiPID = 0;
GameUI.enemyUIPID = 1;
GameUI.GameTower = (_a = {},
    _a[GameUI.uiPID] = [-1, -1, -1],
    _a[GameUI.enemyUIPID] = [-1, -1, -1],
    _a);
GameUI.uiPIsGood = true;
GameUI.uiPIsPlayer = true;
GameUI.uiPHero = Players.GetPlayerHeroEntityIndex(GameUI.uiPID);
GameUI.uiPIsSpectator = true;
GameUI.handCards = [];
GameUI.preHandCards = [];
GameUI.DeckCardPanelOpen = true;
GameUI.manaInfo = {
    maxMana: 10,
    mana: 0,
    speed: 1,
    time: 0,
};
GameUI.ButtonGroupChoose = {};
GameUI.HeroSpInfo = {
    HeroName: '',
    HeroImage: '1',
    SpellCardIndex: -1,
    SpellName: '',
    cost: 1,
    nextUsingTime: -1,
    cooldown: 0,
    num: '',
    usesNum: 0,
};
GameUI.deckCardsMaxNum = 8;
GameUI.initHand = false;
GameUI.buttonBeAble = [0];
var uiElementsToDisable = [
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_TOP_TIMEOFDAY,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_TOP_HEROES,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_TOP_MENU_BUTTONS,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_TOP_BAR_BACKGROUND,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_TOP_BAR_RADIANT_TEAM,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_TOP_BAR_DIRE_TEAM,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_TOP_BAR_SCORE,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_TOP_BAR,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_ACTION_MINIMAP,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_INVENTORY_PANEL,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_INVENTORY_SHOP,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_INVENTORY_ITEMS,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_INVENTORY_QUICKBUY,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_INVENTORY_COURIER,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_INVENTORY_PROTECT,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_INVENTORY_GOLD,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_SHOP_SUGGESTEDITEMS,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_SHOP_COMMONITEMS,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_FLYOUT_SCOREBOARD,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_QUICK_STATS,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_KILLCAM,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_FIGHT_RECAP,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_AGHANIMS_STATUS,
];
uiElementsToDisable.forEach(function (element) {
    GameUI.SetDefaultUIEnabled(element, false);
});
