{"tower_default": {"Level": 5, "UnitType": "building", "AttackType": 0, "AttackDamageMax": 100, "AttackDamageMin": 90, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "ProjectileModel": "particles/base_attacks/ranged_tower_good.vpcf", "ProjectileSpeed": 1100, "AttackAnimationPoint": 0.6, "AttackRange": 800, "AttackRate": 1.4, "AttackAcquisitionRange": 1500, "BaseAttackSpeed": 120, "BaseClass": "npc_dota_tower", "ArmorPhysical": 4, "MagicalResistance": 50, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_TOWER", "HealthBarOffset": 260, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/props_structures/tower_good.vmdl", "ModelScale": 1, "SoundSet": "Tower.Water", "MinimapIcon": "minimap_tower90", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_NONE", "MovementSpeed": 100, "MovementTurnRate": 1, "RingRadius": 120, "StatusHealth": 1500, "StatusHealthRegen": 0, "StatusMana": 0, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "creep_siege", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {}}, "AbilityNum": 1, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": 0, "DeathSound": 0}, "tower_default_aciant": {"Level": 10, "UnitType": "building", "AttackType": 0, "AttackDamageMax": 120, "AttackDamageMin": 100, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "ProjectileModel": "particles/base_attacks/ranged_tower_good.vpcf", "ProjectileSpeed": 1100, "AttackAnimationPoint": 0.6, "AttackRange": 800, "AttackRate": 1.4, "AttackAcquisitionRange": 1600, "BaseAttackSpeed": 140, "BaseClass": "npc_dota_tower", "ArmorPhysical": 6, "MagicalResistance": 50, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_BUILDING", "HealthBarOffset": 260, "HealthBarPips": 0, "AttackHealthPips": 20, "Model": "models/props_structures/radiant_ancient001.vmdl", "ModelScale": 1, "SoundSet": "<PERSON><PERSON><PERSON>_<PERSON>_Melee", "MinimapIcon": "minimap_ancient", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_NONE", "MovementSpeed": 100, "MovementTurnRate": 1, "RingRadius": 350, "StatusHealth": 2000, "StatusHealthRegen": 0, "StatusMana": 0, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "creep_siege", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {}}, "AbilityNum": 1, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": 0, "DeathSound": 0}, "creep_default_melee": {"Level": 2, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 55, "AttackDamageMin": 45, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackAnimationPoint": 0.467, "AttackRange": 100, "AttackRate": 1.2, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 100, "BaseClass": "npc_dota_creature", "ArmorPhysical": 2, "MagicalResistance": 25, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/creeps/lane_creeps/creep_bad_melee/creep_bad_melee.vmdl", "ModelScale": 0.93, "SoundSet": "<PERSON><PERSON><PERSON>_Bad_Melee", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 45, "StatusHealth": 100, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": 0, "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {}}, "AbilityNum": 0, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.small", "DeathSound": "Fw.Cards.Creep.Default.Death.small"}, "creep_default_ranged": {"Level": 2, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 48, "AttackDamageMin": 38, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "ProjectileModel": "particles/base_attacks/ranged_badguy.vpcf", "ProjectileSpeed": 900, "AttackAnimationPoint": 0.5, "AttackRange": 500, "AttackRate": 1.2, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 100, "BaseClass": "npc_dota_creature", "ArmorPhysical": 2, "MagicalResistance": 25, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/creeps/lane_creeps/creep_bad_ranged/lane_dire_ranged.vmdl", "ModelScale": 0.93, "SoundSet": "Creep_Bad_Range", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 45, "StatusHealth": 80, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": 0, "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {}}, "AbilityNum": 0, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.small", "DeathSound": "Fw.Cards.Creep.Default.Death.small"}, "creep_default_siege": {"Level": 4, "UnitType": "ground", "AttackType": 1, "AttackDamageMax": 110, "AttackDamageMin": 90, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "ProjectileModel": "particles/base_attacks/ranged_siege_bad.vpcf", "ProjectileSpeed": 1100, "AttackAnimationPoint": 0.7, "AttackRange": 700, "AttackRate": 2, "AttackAcquisitionRange": 1100, "BaseAttackSpeed": 100, "BaseClass": "npc_dota_creature", "ArmorPhysical": 1, "MagicalResistance": 50, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/creeps/lane_creeps/creep_bad_siege/creep_bad_siege.vmdl", "ModelScale": 0.93, "SoundSet": "Creep_Bad_Engine", "MinimapIcon": "minimap_siege", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 230, "MovementTurnRate": 1, "RingRadius": 100, "StatusHealth": 800, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "creep_siege", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {}}, "AbilityNum": 1, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.Engine", "DeathSound": "Fw.Cards.Creep.Default.Death.siege"}, "creep_default_axe": {"Level": 4, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 70, "AttackDamageMin": 60, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackAnimationPoint": 0.4, "AttackRange": 150, "AttackRate": 1.7, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 120, "BaseClass": "npc_dota_creature", "ArmorPhysical": 4, "MagicalResistance": 25, "ConsideredHero": 1, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 30, "Model": "models/heroes/axe/axe.vmdl", "ModelScale": 1.1, "SoundSet": "Hero_Axe", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 270, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 500, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "axe_counter_helix", "Ability2": "fw_unit_str_hero_health", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 2}, "2": {"ItemDef": 3}, "3": {"ItemDef": 4}, "4": {"ItemDef": 5}}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {}}, "AbilityNum": 2, "UnitDuration": -1, "ParEntUnit": "npc_dota_hero_axe", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.Axe.Entrance.Default", "DeathSound": "Fw.Cards.Hero.Axe.Death.Default"}, "creep_default_juggernaut": {"Level": 3, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 75, "AttackDamageMin": 65, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackAnimationPoint": 0.33, "AttackRange": 150, "AttackRate": 1.4, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 160, "BaseClass": "npc_dota_creature", "ArmorPhysical": 3, "MagicalResistance": 25, "ConsideredHero": 1, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 20, "Model": "models/heroes/juggernaut/juggernaut.vmdl", "ModelScale": 1.1, "SoundSet": "<PERSON><PERSON><PERSON><PERSON>naut", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 250, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "juggernaut_blade_dance", "Ability2": "fw_unit_int_hero", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 6}, "2": {"ItemDef": 7}, "3": {"ItemDef": 8}, "4": {"ItemDef": 9}, "5": {"ItemDef": 62}, "6": {"ItemDef": 811}}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {}}, "AbilityNum": 2, "UnitDuration": -1, "ParEntUnit": "npc_dota_hero_juggernaut", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.Jugg.Entrance.Default", "DeathSound": "Fw.Cards.Hero.Jugg.Death.Default", "SpawnActivityModifiers": "run"}, "creep_granite_golem": {"Level": 5, "UnitType": "ground", "AttackType": 1, "AttackDamageMax": 155, "AttackDamageMin": 145, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackAnimationPoint": 0.3, "AttackRange": 150, "AttackRate": 2, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 120, "BaseClass": "npc_dota_creature", "ArmorPhysical": 5, "MagicalResistance": 25, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 320, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/creeps/neutral_creeps/n_creep_golem_a/neutral_creep_golem_a.vmdl", "ModelScale": 2, "SoundSet": "n_creep_<PERSON>ee", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 200, "MovementTurnRate": 1, "RingRadius": 90, "StatusHealth": 1000, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": 0, "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {}}, "AbilityNum": 0, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.granite", "DeathSound": 0}, "creep_undyingt_tombstone": {"Level": 4, "UnitType": "building", "AttackType": 0, "AttackDamageMax": 0, "AttackDamageMin": 0, "AttackCapabilities": "DOTA_UNIT_CAP_NO_ATTACK", "AttackAnimationPoint": 0.3, "AttackRange": 0, "AttackRate": 0, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 1, "BaseClass": "npc_dota_creature", "ArmorPhysical": 0, "MagicalResistance": 0, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 260, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/heroes/undying/undying_tower.vmdl", "ModelScale": 0.93, "SoundSet": "Undying_Tombstone", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_NONE", "MovementSpeed": 100, "MovementTurnRate": 1, "RingRadius": 45, "StatusHealth": 300, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "fw_summon_zombie", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "1,-1,-1", "SpellAI": {"1": "ai_ab_cast_auto"}}, "AbilityNum": 1, "UnitDuration": 30, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Tombstone.Entrance", "DeathSound": "Fw.Cards.Creep.Tombstone.Death"}, "creep_default_undying_zombie": {"Level": 1, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 40, "AttackDamageMin": 30, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackAnimationPoint": 0.3, "AttackRange": 100, "AttackRate": 1.6, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 160, "BaseClass": "npc_dota_creature", "ArmorPhysical": 1, "MagicalResistance": 25, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/heroes/undying/undying_minion.vmdl", "ModelScale": 0.93, "SoundSet": "Undying_Zombie", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 45, "StatusHealth": 30, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": 0, "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {}}, "AbilityNum": 0, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.undying", "DeathSound": 0}, "creep_default_flesh_golem": {"Level": 1, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 60, "AttackDamageMin": 50, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackAnimationPoint": 0.3, "AttackRange": 100, "AttackRate": 1.7, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 170, "BaseClass": "npc_dota_creature", "ArmorPhysical": 1.5, "MagicalResistance": 25, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 260, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/heroes/undying/undying_flesh_golem.vmdl", "ModelScale": 1.2, "SoundSet": "Hero_Undying", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 300, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "fw_zombie_extra", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {}}, "AbilityNum": 1, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.flesh_golem.Entrance", "DeathSound": 0}, "creep_default_abaddon": {"Level": 3, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 76, "AttackDamageMin": 65, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackAnimationPoint": 0.56, "AttackRange": 150, "AttackRate": 1.5, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 100, "BaseClass": "npc_dota_creature", "ArmorPhysical": 3.5, "MagicalResistance": 25, "ConsideredHero": 1, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 20, "Model": "models/heroes/abaddon/abaddon.vmdl", "ModelScale": 1.3, "SoundSet": "Hero_<PERSON><PERSON>", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 400, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "abaddon_aphotic_shield", "Ability2": "fw_unit_str_hero", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 454}, "2": {"ItemDef": 455}, "3": {"ItemDef": 456}, "4": {"ItemDef": 457}, "5": {"ItemDef": 458}}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {"1": "ai_ab_point_near_tar_friend_500_health_low"}}, "AbilityNum": 2, "UnitDuration": -1, "ParEntUnit": "npc_dota_hero_abaddon", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.A<PERSON>don.Entrance.Default", "DeathSound": "Fw.Cards.Hero<PERSON>.Death.Default"}, "creep_default_ursa": {"Level": 4, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 70, "AttackDamageMin": 60, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackAnimationPoint": 0.3, "AttackRange": 150, "AttackRate": 1.7, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 250, "BaseClass": "npc_dota_creature", "ArmorPhysical": 3, "MagicalResistance": 25, "ConsideredHero": 1, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 20, "Model": "models/heroes/ursa/ursa.vmdl", "ModelScale": 1, "SoundSet": "Hero_Ursa", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 400, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "ursa_fury_swipes", "Ability2": "fw_unit_int_hero", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 85}, "2": {"ItemDef": 86}, "3": {"ItemDef": 87}, "4": {"ItemDef": 88}, "5": {"ItemDef": 584}}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {}}, "AbilityNum": 2, "UnitDuration": -1, "ParEntUnit": "npc_dota_hero_ursa", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.Ursa.Entrance.Default", "DeathSound": "Fw.Cards.Hero.Ursa.Death.Default"}, "creep_default_weaver": {"Level": 3, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 67, "AttackDamageMin": 60, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "ProjectileModel": "particles/units/heroes/hero_weaver/weaver_base_attack.vpcf", "ProjectileSpeed": 900, "AttackAnimationPoint": 0.55, "AttackRange": 425, "AttackRate": 1.1, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 110, "BaseClass": "npc_dota_creature", "ArmorPhysical": 4, "MagicalResistance": 25, "ConsideredHero": 1, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 20, "Model": "models/heroes/weaver/weaver.vmdl", "ModelScale": 1.3, "SoundSet": "<PERSON><PERSON><PERSON>", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 330, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "weaver_geminate_attack", "Ability2": "fw_unit_int_hero", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 277}, "2": {"ItemDef": 278}, "3": {"ItemDef": 279}, "4": {"ItemDef": 280}, "5": {"ItemDef": 585}}}, "UnitAI": {"AutoNum": "2,-1,-1", "SpellAI": {"1": "ai_ab_toggle_auto_2"}}, "AbilityNum": 2, "UnitDuration": -1, "ParEntUnit": "npc_dota_hero_weaver", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero<PERSON>.Entrance.Default", "DeathSound": "Fw.Cards.<PERSON><PERSON>.Death.Default"}, "creep_default_spirit_breaker": {"Level": 3, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 100, "AttackDamageMin": 90, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackAnimationPoint": 0.6, "AttackRange": 150, "AttackRate": 1.9, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 100, "BaseClass": "npc_dota_creature", "ArmorPhysical": 5, "MagicalResistance": 25, "ConsideredHero": 1, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 20, "Model": "models/heroes/spirit_breaker/spirit_breaker.vmdl", "ModelScale": 1.3, "SoundSet": "Hero_spirit_breaker", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 350, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 500, "StatusHealthRegen": 0, "StatusMana": 500, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "spirit_breaker_charge_of_darkness", "Ability2": "spirit_breaker_greater_bash", "Ability3": "fw_unit_str_hero_health", "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 106}, "2": {"ItemDef": 111}, "3": {"ItemDef": 112}, "4": {"ItemDef": 113}, "5": {"ItemDef": 114}, "6": {"ItemDef": 115}, "7": {"ItemDef": 116}}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {"1": "ai_ab_point_far_tar_1000"}}, "AbilityNum": 3, "UnitDuration": -1, "ParEntUnit": "npc_dota_hero_spirit_breaker", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.spirit_breaker.Entrance.Default", "DeathSound": "Fw.Cards.Hero.spirit_breaker.Death.Default"}, "creep_default_huskar": {"Level": 5, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 90, "AttackDamageMin": 80, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "ProjectileModel": "particles/units/heroes/hero_huskar/huskar_base_attack.vpcf", "ProjectileSpeed": 1400, "AttackAnimationPoint": 0.3, "AttackRange": 400, "AttackRate": 1.5, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 110, "BaseClass": "npc_dota_creature", "ArmorPhysical": 3, "MagicalResistance": 25, "ConsideredHero": 1, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/heroes/huskar/huskar.vmdl", "ModelScale": 1, "SoundSet": "<PERSON><PERSON><PERSON><PERSON>", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 800, "StatusHealthRegen": 0.1, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "huskar_burning_spear", "Ability2": "fw_huskar_berserkers_blood", "Ability3": "fw_unit_str_hero_health", "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 268}, "2": {"ItemDef": 269}, "3": {"ItemDef": 270}, "4": {"ItemDef": 271}, "5": {"ItemDef": 272}}}, "UnitAI": {"AutoNum": "1,-1,-1", "SpellAI": {"1": "ai_ab_toggle_auto_2"}}, "AbilityNum": 3, "UnitDuration": -1, "ParEntUnit": "npc_dota_hero_huskar", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.huskar.Entrance.Default", "DeathSound": "Fw.Cards.Hero.huskar.<PERSON>.Default"}, "creep_default_earthshaker": {"Level": 2, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 50, "AttackDamageMin": 40, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackAnimationPoint": 0.467, "AttackRange": 150, "AttackRate": 1.7, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 110, "BaseClass": "npc_dota_creature", "ArmorPhysical": 2, "MagicalResistance": 25, "ConsideredHero": 1, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 20, "Model": "models/heroes/earthshaker/earthshaker.vmdl", "ModelScale": 1, "SoundSet": "Hero_Earthshaker", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 250, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 200, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "earthshaker_echo_slam", "Ability2": "fw_unit_str_hero_health", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 459}, "2": {"ItemDef": 460}, "3": {"ItemDef": 461}, "4": {"ItemDef": 462}}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {"1": "ai_ab_cast_auto"}}, "AbilityNum": 2, "UnitDuration": -1, "ParEntUnit": "npc_dota_hero_earthshaker", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.earthshaker.Entrance.Default", "DeathSound": "Fw.Cards.Hero.earthshaker.Death.Default"}, "creep_default_skywrath_mage": {"Level": 1, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 60, "AttackDamageMin": 50, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "ProjectileModel": "particles/units/heroes/hero_skywrath_mage/skywrath_mage_base_attack.vpcf", "ProjectileSpeed": 1000, "AttackAnimationPoint": 0.4, "AttackRange": 625, "AttackRate": 1.7, "AttackAcquisitionRange": 800, "BaseAttackSpeed": 120, "BaseClass": "npc_dota_creature", "ArmorPhysical": 2, "MagicalResistance": 25, "ConsideredHero": 1, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/heroes/skywrath_mage/skywrath_mage.vmdl", "ModelScale": 1, "SoundSet": "Hero_SkywrathMage", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 300, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "skywrath_mage_concussive_shot", "Ability2": "fw_unit_agi_hero", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 420}, "2": {"ItemDef": 421}, "3": {"ItemDef": 422}, "4": {"ItemDef": 423}, "5": {"ItemDef": 424}, "6": {"ItemDef": 425}}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {"1": "ai_ab_circle_aoe_800"}}, "AbilityNum": 2, "UnitDuration": -1, "ParEntUnit": "npc_dota_hero_skywrath_mage", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.skywrath_mage.Entrance.Default", "DeathSound": "Fw.Cards.Hero.skywrath_mage.Death.Default"}, "creep_default_dark_seer": {"Level": 4, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 90, "AttackDamageMin": 70, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackAnimationPoint": 0.59, "AttackRange": 150, "AttackRate": 1.7, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 120, "BaseClass": "npc_dota_creature", "ArmorPhysical": 4, "MagicalResistance": 25, "ConsideredHero": 1, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 20, "Model": "models/heroes/dark_seer/dark_seer.vmdl", "ModelScale": 1, "SoundSet": "Hero_DarkSeer", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 450, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "dark_seer_ion_shell", "Ability2": "fw_unit_agi_hero", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 348}, "2": {"ItemDef": 349}, "3": {"ItemDef": 350}, "4": {"ItemDef": 351}, "5": {"ItemDef": 352}}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {"1": "ai_ab_point_near_tar_friend_500_hh_dark_shell"}}, "AbilityNum": 2, "UnitDuration": -1, "ParEntUnit": "npc_dota_hero_dark_seer", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.dark_seer.Entrance.Default", "DeathSound": "Fw.Cards.Hero.dark_seer.<PERSON>.Default"}, "creep_default_primal_beast": {"Level": 4, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 90, "AttackDamageMin": 80, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackAnimationPoint": 0.6, "AttackRange": 150, "AttackRate": 1.8, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 100, "BaseClass": "npc_dota_creature", "ArmorPhysical": 5, "MagicalResistance": 25, "ConsideredHero": 1, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 20, "Model": "models/heroes/primal_beast/primal_beast_base.vmdl", "ModelScale": 1.3, "SoundSet": "Hero_PrimalBeast", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 750, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "primal_beast_pulverize", "Ability2": "fw_unit_str_hero_health", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 769}, "2": {"ItemDef": 770}, "3": {"ItemDef": 771}, "4": {"ItemDef": 772}}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {"1": "ai_ab_point_near_tar_300"}}, "AbilityNum": 2, "UnitDuration": -1, "ParEntUnit": "npc_dota_hero_primal_beast", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.primal_beast.Entrance.Default", "DeathSound": "Fw.Cards.Hero.primal_beast.Death.Default"}, "creep_default_tiny": {"Level": 1, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 170, "AttackDamageMin": 150, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackAnimationPoint": 0.4, "AttackRange": 200, "AttackRate": 1.7, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 100, "BaseClass": "npc_dota_creature", "ArmorPhysical": 8, "MagicalResistance": 10, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 350, "HealthBarPips": 0, "AttackHealthPips": 20, "Model": "models/heroes/tiny/tiny_04/tiny_04.vmdl", "ModelScale": 1.8, "SoundSet": "<PERSON>_<PERSON>", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 250, "MovementTurnRate": 0.5, "RingRadius": 100, "StatusHealth": 1000, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "fw_tiny_tree_grab", "Ability2": "fw_unit_tiny_hero", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {"1": "ai_ab_cast_auto"}}, "AbilityNum": 2, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.tiny.Entrance.Default", "DeathSound": "Fw.Cards.Hero.tiny.<PERSON>.Default"}, "creep_default_luna": {"Level": 3, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 60, "AttackDamageMin": 50, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "ProjectileModel": "particles/units/heroes/hero_luna/luna_base_attack.vpcf", "ProjectileSpeed": 900, "AttackAnimationPoint": 0.35, "AttackRange": 400, "AttackRate": 1.7, "AttackAcquisitionRange": 800, "BaseAttackSpeed": 100, "BaseClass": "npc_dota_creature", "ArmorPhysical": 3, "MagicalResistance": 25, "ConsideredHero": 1, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/heroes/luna/luna.vmdl", "ModelScale": 1, "SoundSet": "<PERSON><PERSON><PERSON>", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 400, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "fw_luna_lunar_grace", "Ability2": "fw_unit_luna_hero", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 448}, "2": {"ItemDef": 449}, "3": {"ItemDef": 450}, "4": {"ItemDef": 451}, "5": {"ItemDef": 452}, "6": {"ItemDef": 453}}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {}}, "AbilityNum": 2, "UnitDuration": -1, "ParEntUnit": "npc_dota_hero_luna", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.luna.Entrance.Default", "DeathSound": "Fw.Cards.Hero.luna.Death.Default"}, "creep_default_sniper": {"Level": 1, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 65, "AttackDamageMin": 50, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "ProjectileModel": "particles/units/heroes/hero_sniper/sniper_base_attack.vpcf", "ProjectileSpeed": 3000, "AttackAnimationPoint": 0.17, "AttackRange": 550, "AttackRate": 1.7, "AttackAcquisitionRange": 800, "BaseAttackSpeed": 170, "BaseClass": "npc_dota_creature", "ArmorPhysical": 5, "MagicalResistance": 25, "ConsideredHero": 1, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/heroes/sniper/sniper.vmdl", "ModelScale": 1, "SoundSet": "<PERSON>_<PERSON><PERSON><PERSON>", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 250, "StatusHealthRegen": 0, "StatusMana": 1000, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "sniper_headshot", "Ability2": "sniper_keen_scope", "Ability3": "fw_unit_int_hero", "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 281}, "2": {"ItemDef": 282}, "3": {"ItemDef": 283}, "4": {"ItemDef": 284}, "5": {"ItemDef": 285}}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {}}, "AbilityNum": 3, "UnitDuration": -1, "ParEntUnit": "npc_dota_hero_sniper", "PreUsingSound": 0, "EntranceSound": "Fw.<PERSON>s.Hero.sniper.Entrance.Default", "DeathSound": "Fw.Cards.Hero.sniper.<PERSON>.Default"}, "creep_default_techies": {"Level": 2, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 70, "AttackDamageMin": 60, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "ProjectileModel": "particles/units/heroes/hero_techies/techies_base_attack.vpcf", "ProjectileSpeed": 900, "AttackAnimationPoint": 0.5, "AttackRange": 600, "AttackRate": 1.7, "AttackAcquisitionRange": 800, "BaseAttackSpeed": 100, "BaseClass": "npc_dota_creature", "ArmorPhysical": 5, "MagicalResistance": 25, "ConsideredHero": 1, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/heroes/techies/techies.vmdl", "ModelScale": 0.7, "SoundSet": "Hero_Techies", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 200, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 600, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "fw_techies_summon_bomb", "Ability2": "fw_unit_agi_hero", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 487}, "2": {"ItemDef": 489}, "3": {"ItemDef": 490}, "4": {"ItemDef": 491}, "5": {"ItemDef": 492}}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {"1": "ai_ab_cast_auto"}}, "AbilityNum": 2, "UnitDuration": -1, "ParEntUnit": "npc_dota_hero_techies", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.techies.Entrance.Default", "DeathSound": "Fw.Cards.Hero.techies.Death.Default"}, "creep_default_jakiro": {"Level": 1, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 40, "AttackDamageMin": 35, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "ProjectileModel": "particles/units/heroes/hero_jakiro/jakiro_base_attack.vpcf", "ProjectileSpeed": 1100, "AttackAnimationPoint": 0.4, "AttackRange": 400, "AttackRate": 1.7, "AttackAcquisitionRange": 800, "BaseAttackSpeed": 113, "BaseClass": "npc_dota_creature", "ArmorPhysical": 5, "MagicalResistance": 25, "ConsideredHero": 1, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/heroes/jakiro/jakiro.vmdl", "ModelScale": 1, "SoundSet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 300, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "jakiro_liquid_fire", "Ability2": "jakiro_liquid_ice", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 631}, "2": {"ItemDef": 633}, "3": {"ItemDef": 634}, "4": {"ItemDef": 635}, "5": {"ItemDef": 636}}}, "UnitAI": {"AutoNum": "1,1,-1", "SpellAI": {"1": "ai_ab_toggle_auto_2", "2": "ai_ab_toggle_auto_2"}}, "AbilityNum": 2, "UnitDuration": -1, "ParEntUnit": "npc_dota_hero_jakiro", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.jakiro.Entrance.Default", "DeathSound": "Fw.Cards.Hero.jakiro.Death.Default"}, "creep_default_visage": {"Level": 1, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 55, "AttackDamageMin": 45, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "ProjectileModel": "particles/units/heroes/hero_jakiro/jakiro_base_attack.vpcf", "ProjectileSpeed": 1100, "AttackAnimationPoint": 0.4, "AttackRange": 400, "AttackRate": 1.7, "AttackAcquisitionRange": 800, "BaseAttackSpeed": 100, "BaseClass": "npc_dota_creature", "ArmorPhysical": 5, "MagicalResistance": 25, "ConsideredHero": 1, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/heroes/visage/visage.vmdl", "ModelScale": 1, "SoundSet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 500, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "jaki<PERSON>_dual_breath", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {}}, "AbilityNum": 1, "UnitDuration": -1, "ParEntUnit": "npc_dota_hero_jakiro", "PreUsingSound": 0, "EntranceSound": 0, "DeathSound": 0}, "creep_default_alpha_wolf": {"Level": 3, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 55, "AttackDamageMin": 45, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackAnimationPoint": 0.33, "AttackRange": 100, "AttackRate": 2, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 150, "BaseClass": "npc_dota_creature", "ArmorPhysical": 3, "MagicalResistance": 25, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/creeps/neutral_creeps/n_creep_worg_large/n_creep_worg_large.vmdl", "ModelScale": 0.93, "SoundSet": "n_creep_<PERSON>ee", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 45, "StatusHealth": 250, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "alpha_wolf_command_aura", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {}}, "AbilityNum": 1, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.Worg", "DeathSound": "Fw.Cards.Creep.Default.Death.small2"}, "creep_default_giant_wolf": {"Level": 3, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 35, "AttackDamageMin": 30, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackAnimationPoint": 0.33, "AttackRange": 100, "AttackRate": 2, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 150, "BaseClass": "npc_dota_creature", "ArmorPhysical": 2, "MagicalResistance": 25, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/creeps/neutral_creeps/n_creep_worg_small/n_creep_worg_small.vmdl", "ModelScale": 0.93, "SoundSet": "n_creep_<PERSON>ee", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 45, "StatusHealth": 150, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": 0, "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {}}, "AbilityNum": 0, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.Worg", "DeathSound": "Fw.Cards.Creep.Default.Death.small2"}, "creep_default_forest_troll_high_priest": {"Level": 1, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 40, "AttackDamageMin": 30, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "ProjectileModel": "particles/base_attacks/ranged_badguy.vpcf", "ProjectileSpeed": 900, "AttackAnimationPoint": 0.3, "AttackRange": 600, "AttackRate": 2, "AttackAcquisitionRange": 800, "BaseAttackSpeed": 150, "BaseClass": "npc_dota_creature", "ArmorPhysical": 1, "MagicalResistance": 25, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/creeps/neutral_creeps/n_creep_forest_trolls/n_creep_forest_troll_high_priest.vmdl", "ModelScale": 0.93, "SoundSet": "n_creep_Ranged", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 45, "StatusHealth": 200, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "fw_forest_troll_high_priest_heal", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {"1": "ai_ab_point_near_tar_friend_500_lh"}}, "AbilityNum": 1, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.small2", "DeathSound": "Fw.Cards.Creep.Default.Death.small"}, "creep_default_forest_troll_berserker": {"Level": 1, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 45, "AttackDamageMin": 35, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "ProjectileModel": "particles/base_attacks/ranged_badguy.vpcf", "ProjectileSpeed": 1200, "AttackAnimationPoint": 0.3, "AttackRange": 500, "AttackRate": 2, "AttackAcquisitionRange": 800, "BaseAttackSpeed": 150, "BaseClass": "npc_dota_creature", "ArmorPhysical": 1, "MagicalResistance": 25, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/creeps/neutral_creeps/n_creep_forest_trolls/n_creep_forest_troll_berserker.vmdl", "ModelScale": 0.93, "SoundSet": "n_creep_Ranged", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 45, "StatusHealth": 200, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "berserker_troll_break", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {}}, "AbilityNum": 1, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.small2", "DeathSound": "Fw.Cards.Creep.Default.Death.small"}, "creep_default_dark_troll_warlord": {"Level": 1, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 55, "AttackDamageMin": 45, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "ProjectileModel": "particles/base_attacks/ranged_badguy.vpcf", "ProjectileSpeed": 1200, "AttackAnimationPoint": 0.3, "AttackRange": 500, "AttackRate": 2, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 150, "BaseClass": "npc_dota_creature", "ArmorPhysical": 2, "MagicalResistance": 25, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/creeps/neutral_creeps/n_creep_troll_dark_b/n_creep_troll_dark_b.vmdl", "ModelScale": 0.93, "SoundSet": "n_creep_Ranged", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 45, "StatusHealth": 400, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "fw_summon_zombie_b", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {"1": "ai_ab_cast_hp_low,ai_ab_cast_exceed_river"}}, "AbilityNum": 1, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.small3", "DeathSound": 0}, "creep_default_ghost": {"Level": 1, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 35, "AttackDamageMin": 30, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "ProjectileModel": "particles/neutral_fx/ghost_base_attack.vpcf", "ProjectileSpeed": 900, "AttackAnimationPoint": 0.3, "AttackRange": 400, "AttackRate": 2, "AttackAcquisitionRange": 800, "BaseAttackSpeed": 150, "BaseClass": "npc_dota_creature", "ArmorPhysical": 1, "MagicalResistance": 25, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/creeps/neutral_creeps/n_creep_ghost_a/n_creep_ghost_a.vmdl", "ModelScale": 0.93, "SoundSet": "n_creep_Ranged", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 50, "StatusHealth": 200, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "ghost_frost_attack", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {}}, "AbilityNum": 1, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.ghost", "DeathSound": 0}, "creep_default_mud_golem": {"Level": 1, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 35, "AttackDamageMin": 30, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackAnimationPoint": 0.3, "AttackRange": 100, "AttackRate": 2, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 150, "BaseClass": "npc_dota_creature", "ArmorPhysical": 1, "MagicalResistance": 25, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/creeps/neutral_creeps/n_creep_golem_b/n_creep_golem_b.vmdl", "ModelScale": 1.1, "SoundSet": "n_creep_<PERSON>ee", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 50, "StatusHealth": 300, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "mud_golem_hurl_boulder", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {}}, "AbilityNum": 1, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.golem_rock", "DeathSound": 0}, "creep_default_mud_golem_split": {"Level": 1, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 35, "AttackDamageMin": 30, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackAnimationPoint": 0.3, "AttackRange": 100, "AttackRate": 2, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 150, "BaseClass": "npc_dota_creature", "ArmorPhysical": 0, "MagicalResistance": 25, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/creeps/neutral_creeps/n_creep_golem_b/n_creep_golem_b.vmdl", "ModelScale": 0.8, "SoundSet": "n_creep_<PERSON>ee", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 50, "StatusHealth": 150, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "mud_golem_hurl_boulder", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {}}, "AbilityNum": 1, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.golem_rock", "DeathSound": 0}, "creep_default_polar_furbolg_champion": {"Level": 3, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 70, "AttackDamageMin": 60, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackAnimationPoint": 0.3, "AttackRange": 100, "AttackRate": 2, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 100, "BaseClass": "npc_dota_creature", "ArmorPhysical": 3, "MagicalResistance": 25, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/creeps/neutral_creeps/n_creep_beast/n_creep_beast.vmdl", "ModelScale": 1.1, "SoundSet": "n_creep_<PERSON>ee", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 50, "StatusHealth": 500, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "centaur_khan_endurance_aura", "Ability2": "furbolg_enrage_attack_speed", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {}}, "AbilityNum": 2, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.furbolg_champion", "DeathSound": 0}, "creep_default_polar_furbolg_ursa_warrior": {"Level": 3, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 60, "AttackDamageMin": 50, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackAnimationPoint": 0.3, "AttackRange": 100, "AttackRate": 2, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 100, "BaseClass": "npc_dota_creature", "ArmorPhysical": 3, "MagicalResistance": 25, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/creeps/neutral_creeps/n_creep_furbolg/n_creep_furbolg_disrupter.vmdl", "ModelScale": 1.1, "SoundSet": "n_creep_<PERSON>ee", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 50, "StatusHealth": 500, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "polar_furbolg_ursa_warrior_thunder_clap", "Ability2": "furbolg_enrage_damage", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {"1": "ai_ab_circle_aoe_300_2up"}}, "AbilityNum": 2, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.furbolg_ursa", "DeathSound": 0}, "creep_default_ogre_magi": {"Level": 2, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 50, "AttackDamageMin": 40, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackAnimationPoint": 0.3, "AttackRange": 100, "AttackRate": 2, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 120, "BaseClass": "npc_dota_creature", "ArmorPhysical": 2, "MagicalResistance": 25, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/creeps/neutral_creeps/n_creep_ogre_lrg/n_creep_ogre_lrg.vmdl", "ModelScale": 1, "SoundSet": "n_creep_<PERSON>ee", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 50, "StatusHealth": 400, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "ogre_magi_frost_armor", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {"1": "ai_ab_point_near_tar_friend_500_hh"}}, "AbilityNum": 1, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.ogre", "DeathSound": 0}, "creep_default_ogre_mauler": {"Level": 2, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 60, "AttackDamageMin": 50, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackAnimationPoint": 0.3, "AttackRange": 100, "AttackRate": 2, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 120, "BaseClass": "npc_dota_creature", "ArmorPhysical": 2, "MagicalResistance": 25, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/creeps/neutral_creeps/n_creep_ogre_med/n_creep_ogre_med.vmdl", "ModelScale": 1, "SoundSet": "n_creep_<PERSON>ee", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 50, "StatusHealth": 450, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "ogre_bruiser_ogre_smash", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {"1": "ai_ab_gnd_near_150"}}, "AbilityNum": 1, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.ogre", "DeathSound": 0}, "creep_default_elder_jungle_stalker": {"Level": 1, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 35, "AttackDamageMin": 30, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackAnimationPoint": 0.3, "AttackRange": 100, "AttackRate": 2, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 150, "BaseClass": "npc_dota_creature", "ArmorPhysical": 1, "MagicalResistance": 25, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/creeps/neutral_creeps/n_creep_gargoyle/n_creep_gargoyle.vmdl", "ModelScale": 1, "SoundSet": "n_creep_<PERSON>ee", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 45, "StatusHealth": 300, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": 0, "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {}}, "AbilityNum": 0, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.stalker", "DeathSound": 0}, "creep_default_jungle_stalker": {"Level": 1, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 35, "AttackDamageMin": 30, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackAnimationPoint": 0.3, "AttackRange": 128, "AttackRate": 2, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 150, "BaseClass": "npc_dota_creature", "ArmorPhysical": 1, "MagicalResistance": 25, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/creeps/neutral_creeps/n_creep_jungle_stalker/n_creep_gargoyle_jungle_stalker.vmdl", "ModelScale": 1, "SoundSet": "n_creep_<PERSON>ee", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 45, "StatusHealth": 300, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": 0, "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {}}, "AbilityNum": 0, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.stalker", "DeathSound": 0}, "creep_default_prowler_acolyte": {"Level": 1, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 60, "AttackDamageMin": 50, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackAnimationPoint": 0.83, "AttackRange": 100, "AttackRate": 2, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 180, "BaseClass": "npc_dota_creature", "ArmorPhysical": 5, "MagicalResistance": 25, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/creeps/neutral_creeps/n_creep_satyr_spawn_a/n_creep_satyr_spawn_b.vmdl", "ModelScale": 1, "SoundSet": "n_creep_<PERSON>ee", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 350, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": 0, "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {}}, "AbilityNum": 0, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.satyr", "DeathSound": 0}, "creep_default_prowler_shaman": {"Level": 1, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 85, "AttackDamageMin": 75, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackAnimationPoint": 0.83, "AttackRange": 100, "AttackRate": 2, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 180, "BaseClass": "npc_dota_creature", "ArmorPhysical": 2, "MagicalResistance": 25, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/creeps/neutral_creeps/n_creep_satyr_spawn_a/n_creep_satyr_spawn_a.vmdl", "ModelScale": 1, "SoundSet": "n_creep_<PERSON>ee", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 500, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "spawnlord_master_freeze", "Ability2": "spawnlord_master_stomp", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "2,-1,-1", "SpellAI": {"1": "ai_ab_toggle_auto_2", "2": "ai_ab_circle_aoe_300_2up"}}, "AbilityNum": 2, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.satyr", "DeathSound": 0}, "creep_default_big_thunder_lizard": {"Level": 5, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 75, "AttackDamageMin": 65, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "ProjectileModel": "particles/neutral_fx/thunderlizard_base_attack.vpcf", "ProjectileSpeed": 1500, "AttackAnimationPoint": 0.3, "AttackRange": 350, "AttackRate": 2, "AttackAcquisitionRange": 800, "BaseAttackSpeed": 200, "BaseClass": "npc_dota_creature", "ArmorPhysical": 4, "MagicalResistance": 25, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/creeps/neutral_creeps/n_creep_thunder_lizard/n_creep_thunder_lizard_big.vmdl", "ModelScale": 1, "SoundSet": "n_creep_Ranged", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 450, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "big_thunder_lizard_slam", "Ability2": "big_thunder_lizard_frenzy", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {"1": "ai_ab_circle_aoe_150_2up", "2": "ai_ab_point_near_tar_friend_500_hh"}}, "AbilityNum": 2, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.lizard_big", "DeathSound": 0}, "creep_default_small_thunder_lizard": {"Level": 3, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 65, "AttackDamageMin": 55, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "ProjectileModel": "particles/neutral_fx/thunderlizard_base_attack.vpcf", "ProjectileSpeed": 1500, "AttackAnimationPoint": 0.5, "AttackRange": 350, "AttackRate": 2, "AttackAcquisitionRange": 800, "BaseAttackSpeed": 200, "BaseClass": "npc_dota_creature", "ArmorPhysical": 2, "MagicalResistance": 25, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/creeps/neutral_creeps/n_creep_thunder_lizard/n_creep_thunder_lizard_small.vmdl", "ModelScale": 1, "SoundSet": "n_creep_Ranged", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 400, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "big_thunder_lizard_wardrums_aura", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {"1": "ai_ab_circle_aoe_2up"}}, "AbilityNum": 1, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.lizard_small", "DeathSound": 0}, "creep_default_black_dragon": {"Level": 5, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 120, "AttackDamageMin": 100, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "ProjectileModel": "particles/neutral_fx/black_dragon_attack.vpcf", "ProjectileSpeed": 1500, "AttackAnimationPoint": 0.5, "AttackRange": 450, "AttackRate": 2, "AttackAcquisitionRange": 800, "BaseAttackSpeed": 100, "BaseClass": "npc_dota_creature", "ArmorPhysical": 5, "MagicalResistance": 40, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 300, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/creeps/neutral_creeps/n_creep_black_dragon/n_creep_black_dragon.vmdl", "ModelScale": 1, "SoundSet": "n_creep_Ranged", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 400, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "black_dragon_splash_attack", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {}}, "AbilityNum": 1, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.blackdragon", "DeathSound": 0}, "creep_default_ice_shaman": {"Level": 5, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 80, "AttackDamageMin": 70, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "ProjectileModel": "particles/units/heroes/hero_witchdoctor/witchdoctor_base_attack.vpcf", "ProjectileSpeed": 1500, "AttackAnimationPoint": 0.7, "AttackRange": 500, "AttackRate": 1.8, "AttackAcquisitionRange": 800, "BaseAttackSpeed": 150, "BaseClass": "npc_dota_creature", "ArmorPhysical": 3, "MagicalResistance": 25, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 300, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/creeps/ice_biome/giant/ice_giant01.vmdl", "ModelScale": 1.2, "SoundSet": "n_creep_Ranged", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 400, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "ice_shaman_incendiary_bomb", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {"1": "ai_abc_point_far_tar_1000"}}, "AbilityNum": 1, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.ice_shaman", "DeathSound": 0}, "creep_default_roshan": {"Level": 10, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 100, "AttackDamageMin": 100, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "ProjectileModel": "particles/base_attacks/ranged_goodguy.vpcf", "ProjectileSpeed": 1000, "AttackAnimationPoint": 0.6, "AttackRange": 150, "AttackRate": 2, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 150, "BaseClass": "npc_dota_creature", "ArmorPhysical": 1, "MagicalResistance": 25, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/creeps/roshan/roshan.vmdl", "ModelScale": 2, "SoundSet": "<PERSON><PERSON><PERSON>", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 45, "StatusHealth": 2000, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "roshan_spell_block", "Ability2": "roshan_slam", "Ability3": "roshan_bash", "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {"2": "ai_ab_circle_aoe_150_2up"}}, "AbilityNum": 3, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Roshan.Entrance", "DeathSound": "Fw.Cards.Creep.Roshan.Death", "SpawnActivityModifiers": "frostivus"}, "creep_default_viper": {"Level": 1, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 55, "AttackDamageMin": 45, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackAnimationPoint": 0.17, "AttackRange": 550, "AttackRate": 1.7, "AttackAcquisitionRange": 800, "BaseAttackSpeed": 100, "BaseClass": "npc_dota_creature", "ArmorPhysical": 5, "MagicalResistance": 25, "ConsideredHero": 1, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/heroes/viper/viper.vmdl", "ModelScale": 1, "SoundSet": "Hero_Viper", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 500, "StatusHealthRegen": 0, "StatusMana": 1000, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": 0, "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 519}, "2": {"ItemDef": 611}, "3": {"ItemDef": 623}, "4": {"ItemDef": 654}}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {"1": "ai_ab_point_far_tar_1000"}}, "AbilityNum": 0, "UnitDuration": -1, "ParEntUnit": "npc_dota_hero_viper", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.viper.Entrance.Default", "DeathSound": "Fw.Cards.Hero.viper.<PERSON>.Default"}, "creep_default_gyrocopter": {"Level": 1, "UnitType": "building", "AttackType": 0, "AttackDamageMax": 55, "AttackDamageMin": 45, "AttackCapabilities": "DOTA_UNIT_CAP_NO_ATTACK", "AttackAnimationPoint": 0.17, "AttackRange": 100, "AttackRate": 1.7, "AttackAcquisitionRange": 800, "BaseAttackSpeed": 100, "BaseClass": "npc_dota_creature", "ArmorPhysical": 0, "MagicalResistance": 25, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/heroes/gyro/gyro.vmdl", "ModelScale": 1, "SoundSet": "Hero_Gyrocopter", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_NONE", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 100, "StatusHealthRegen": 0, "StatusMana": 1000, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "gyrocopter_call_down", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 176}, "2": {"ItemDef": 177}, "3": {"ItemDef": 178}, "4": {"ItemDef": 179}, "5": {"ItemDef": 131}, "6": {"ItemDef": 527}, "7": {"ItemDef": 126}}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {"1": "ai_ab_gnd_near__1500_2up"}}, "AbilityNum": 1, "UnitDuration": 9, "ParEntUnit": "npc_dota_hero_gyrocopter", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.gyrocopter.Entrance.Default", "DeathSound": "Fw.Cards.Hero.gyrocopter.Death.Default"}, "creep_default_dark_troll": {"Level": 1, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 40, "AttackDamageMin": 35, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "ProjectileModel": "particles/base_attacks/ranged_badguy.vpcf", "ProjectileSpeed": 1200, "AttackAnimationPoint": 0.3, "AttackRange": 400, "AttackRate": 2, "AttackAcquisitionRange": 700, "BaseAttackSpeed": 120, "BaseClass": "npc_dota_creature", "ArmorPhysical": 1, "MagicalResistance": 25, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 150, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/creeps/neutral_creeps/n_creep_troll_dark_a/n_creep_troll_dark_a.vmdl", "ModelScale": 0.7, "SoundSet": "n_creep_Ranged", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 45, "StatusHealth": 300, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": 0, "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {}}, "AbilityNum": 0, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.small3", "DeathSound": 0}, "creep_default_black_drake": {"Level": 5, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 55, "AttackDamageMin": 45, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "ProjectileModel": "particles/neutral_fx/black_drake_attack.vpcf", "ProjectileSpeed": 1500, "AttackAnimationPoint": 0.5, "AttackRange": 400, "AttackRate": 2, "AttackAcquisitionRange": 800, "BaseAttackSpeed": 130, "BaseClass": "npc_dota_creature", "ArmorPhysical": 3, "MagicalResistance": 25, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 300, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/creeps/neutral_creeps/n_creep_black_drake/n_creep_black_drake.vmdl", "ModelScale": 0.65, "SoundSet": "n_creep_Ranged", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 60, "StatusHealth": 300, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "black_dragon_dragonhide_aura", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {}}, "AbilityNum": 1, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Default.Entrance.blackdrake", "DeathSound": 0}, "creep_default_techies_bomb": {"Level": 1, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 0, "AttackDamageMin": 0, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackAnimationPoint": 0.5, "AttackRange": 100, "AttackRate": 1, "AttackAcquisitionRange": 500, "BaseAttackSpeed": 100, "BaseClass": "npc_dota_creature", "ArmorPhysical": 0, "MagicalResistance": 25, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 80, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/heroes/techies/fx_techies_remote_cart.vmdl", "ModelScale": 1, "SoundSet": "n_creep_<PERSON>ee", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 350, "MovementTurnRate": 1, "RingRadius": 50, "StatusHealth": 100, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "fw_techies_bomb_adsorb", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {}}, "AbilityNum": 1, "UnitDuration": -1, "ParEntUnit": 0, "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Creep.Entrance.Default.techies_bomb", "DeathSound": 0}, "creep_default_shaman": {"Level": 1, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 50, "AttackDamageMin": 40, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "ProjectileModel": "particles/units/heroes/hero_shadowshaman/shadowshaman_base_attack.vpcf", "ProjectileSpeed": 900, "AttackAnimationPoint": 0.3, "AttackRange": 400, "AttackRate": 1.7, "AttackAcquisitionRange": 800, "BaseAttackSpeed": 100, "BaseClass": "npc_dota_creature", "ArmorPhysical": 1, "MagicalResistance": 25, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 80, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/heroes/shadowshaman/shadowshaman.vmdl", "ModelScale": 0.9, "SoundSet": "<PERSON>_<PERSON>Shaman", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 250, "MovementTurnRate": 1, "RingRadius": 50, "StatusHealth": 30, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "fw_shadow_shaman_shackles", "Ability2": "fw_unit_shaman_hero", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 246}, "2": {"ItemDef": 247}, "3": {"ItemDef": 248}, "4": {"ItemDef": 249}, "5": {"ItemDef": 250}, "6": {"ItemDef": 251}}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {"1": "ai_ab_point_close_tar_600"}}, "AbilityNum": 2, "UnitDuration": -1, "ParEntUnit": "npc_dota_hero_shadow_shaman", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.shaman.Entrance.Default", "DeathSound": "Fw.Cards.Hero.shaman.<PERSON>.Default"}, "creep_default_crystal_maiden": {"Level": 1, "UnitType": "ground", "AttackType": 0, "AttackDamageMax": 30, "AttackDamageMin": 20, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "ProjectileModel": "particles/units/heroes/hero_crystalmaiden/maiden_base_attack.vpcf", "ProjectileSpeed": 900, "AttackAnimationPoint": 0.3, "AttackRange": 300, "AttackRate": 1.7, "AttackAcquisitionRange": 800, "BaseAttackSpeed": 100, "BaseClass": "npc_dota_creature", "ArmorPhysical": 1, "MagicalResistance": 25, "ConsideredHero": 0, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "HealthBarOffset": 80, "HealthBarPips": 0, "AttackHealthPips": 10, "Model": "models/heroes/crystal_maiden/crystal_maiden.vmdl", "ModelScale": 0.78, "SoundSet": "hero_<PERSON>", "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 50, "StatusHealth": 30, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1000, "VisionNighttimeRange": 1000, "Ability1": "fw_crystal_maiden_frostbite", "Ability2": "fw_unit_delay_hero", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 38}, "2": {"ItemDef": 39}, "3": {"ItemDef": 40}, "4": {"ItemDef": 41}, "5": {"ItemDef": 311}}}, "UnitAI": {"AutoNum": "-1,-1,-1", "SpellAI": {}}, "AbilityNum": 2, "UnitDuration": -1, "ParEntUnit": "npc_dota_hero_crystal_maiden", "PreUsingSound": 0, "EntranceSound": "Fw.Cards.Hero.cm.Entrance.Default", "DeathSound": "Fw.Cards.Hero.cm.<PERSON>.Default"}}