import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";


export class modifier_fw_fireball_debuff extends BaseModifier {
    
    damage_interval:number;
    damage:number;
    par:ParticleID;
    timer:string;
    OnCreated(keys:any): void {
        if (IsServer()) {
            let victim = this.GetParent()
            let ab = this.GetAbility()
            let attacker = ab.GetCaster()
            let damage_interval = ab.GetSpecialValueFor("damage_interval")
            let damage = ab.GetSpecialValueFor("damage")
            let duration = ab.GetSpecialValueFor("duration")
    
            this.par = ParticleManager.CreateParticle("particles/units/heroes/hero_doom_bringer/doom_infernal_blade_debuff.vpcf", ParticleAttachment_t.PATTACH_ABSORIGIN_FOLLOW, victim)
            ParticleManager.SetParticleControlEnt(this.par, 0, victim, ParticleAttachment_t.PATTACH_ABSORIGIN_FOLLOW, "attach_hitloc", Vector(0,0,0), true)
            ParticleManager.SetParticleControl(this.par, 1, victim.GetAbsOrigin())
            let damageType = keys.damageType
            this.timer = GameRules.FastWarSpell.startIntervalSpell(0,duration,damage_interval,undefined, ()=>{
                if (IsValidEntity(victim) && IsValidEntity(attacker) && victim.IsAlive() && attacker.IsAlive()) {
                    ApplyDamage({
                        victim: victim,
                        attacker: attacker,
                        damage: damage,
                        damage_type: damageType,
                        damage_flags:DOTADamageFlag_t.DOTA_DAMAGE_FLAG_NONE,
                        ability:ab,
                    });
                }
            },undefined)
        }
    } 

    OnDestroy(){
        if (IsServer()) {
            Timers.RemoveTimer(this.timer)
            ParticleManager.DestroyParticle(this.par, false);
        }
    }

    IsHidden() {
        return false;
    }

    IsDebuff(): boolean {
        return true
    }

    IsPurgable(): boolean {
        return true
    }

    GetTexture () {
        return "dragon_knight_fireball"
    }

    GetStatusEffectName(): string {
        return "particles/status_fx/status_effect_doom.vpcf"
    }

    AllowIllusionDuplicate () {
        return true;
    }
    
    CheckState() {
        return {
            // [6]: true,
        }   
    }

    RemoveOnDeath(): boolean {
        return true
    }

    // GetModifierAttackSpeedBonus_Constant(): number {
    //     return -this.attackspeed_slow
    // }
    
    // GetModifierMoveSpeedBonus_Constant(): number {
    //     return -this.movespeed_slow
    // }


    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            // 1,
            // 2,
            ];
    }
    
}
