import { PlayerHeroSpellHandle } from "./playerherospellhandle";


export class PlayerHeroAssassin extends PlayerHeroSpellHandle {
    
    

    private cooldownExtra = 10
    private costSub = 1
    AfterCardUsing(data:{handCards:GoFastWarCardClient[],oldIndex:number}): void {
        let index = data.handCards[0].card_index
        if (index != data.oldIndex) {
            let cost = GameRules.KVUtils.getCardsInfo(index).layCost
            this.nowSpellInfo.cooldown = this.cooldownExtra + cost
            this.nowSpellInfo.SpellCardIndex = index
            this.nowSpellInfo.cost = Math.max(cost - this.costSub, 0)
            this.nowSpellInfo.usesNum = data.handCards[0].usesNum
        }
    }

    OnCardInit(data:{handCards:GoFastWarCardClient[]}): void {
        let index = data.handCards[0].card_index
        let cost = GameRules.KVUtils.getCardsInfo(index).layCost
        this.nowSpellInfo.cooldown = this.cooldownExtra + cost
        this.nowSpellInfo.SpellCardIndex = index
        this.nowSpellInfo.cost = Math.max(cost - this.costSub, 0)
        this.nowSpellInfo.usesNum = data.handCards[0].usesNum
    };


    constructor (playerId:PlayerID,spellInfo:PlayerHeroForKV
    ) {
        super(playerId,spellInfo)
        let sp = GameRules.KVUtils.getSpellInfo(spellInfo.SpellName)
        this.nowSpellInfo = {
            HeroName: spellInfo.HeroName,
            HeroImage: spellInfo.HeroImage,
            SpellCardIndex: -1,
            SpellName: spellInfo.SpellName,
            cost: 0,
            nextUsingTime: GameRules.GetGameTime() + sp.CoolDown,
            cooldown:sp.CoolDown,
            num:"",
            usesNum:0,
        }
    }

}