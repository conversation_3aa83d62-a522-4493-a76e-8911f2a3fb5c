// import { EntityUtils } from "../utils/entityutils";
import { KVUtils } from "../utils/kvutils";
import { SoundUtils } from "../utils/sound_utils";
import { Debug } from "./debug";
import { FastWarCard } from "./fastwarcard";
import { FastWarGamePhaseController } from "./fastwargamephasecontroller";
import { FastWarPlayerHeroSpell } from "./fastwarplayerherospell";
import { FastWarSpell } from "./fastwarspell";
import { NPCUtils } from "./npcutils";
import { PlayerData } from "./playerdata";

export class GameConfig {
    constructor() {
        GameRules.fastWarGameStatus = FastWarGamePhase.None
        GameRules.NPCUtils = new NPCUtils()
        GameRules.FastWarCard = new FastWarCard()
        GameRules.FastWarSpell = new FastWarSpell()
        GameRules.FastWarPlayerHeroSpell = new FastWarPlayerHeroSpell()
        GameRules.KVUtils = new KVUtils()
        GameRules.SoundUtils = new SoundUtils()
        GameRules.EntityUtils = undefined as any
        GameRules.PlayerData = new PlayerData()
       
        GameRules.FastWarPhaseController = new FastWarGamePhaseController()
        GameRules.PlayerIDs = []
        GameRules.playerNum = 0
        GameRules.preCacheNum = 0
        GameRules.treeIndex = 96
        GameRules.timeScaleChange = false
        GameRules.BotGameSituationInfo = {
            towerHealth:[[-1,-1,-1],[-1,-1,-1]],
            halfUnitNum:[[[0,0],[0,0]],[[0,0],[0,0]]],
            halfUnitCost:[[[0,0],[0,0]],[[0,0],[0,0]]],
            halfUnitThreatLevel:[[[0,0],[0,0]],[[0,0],[0,0]]],
            halfUnitHealth:[[[0,0],[0,0]],[[0,0],[0,0]]],
        }
        GameRules.DebugOpen = {
            debugPrint:false,
            debugMana:false,
            debugDamage:0,
        }
        GameRules.preCacheInfos = []
        GameRules.preCacheAbs = []
        GameRules.testPlayer = -1
        GameRules.BotFlag = false
        SendToServerConsole('dota_max_physical_items_purchase_limit 9999'); // 用来解决物品数量限制问题
        GameRules.SetShowcaseTime(5);
        GameRules.SetHeroSelectionTime(20);
        GameRules.SetCustomGameSetupAutoLaunchDelay(3)
        GameRules.LockCustomGameSetupTeamAssignment(true);
        GameRules.EnableCustomGameSetupAutoLaunch(true);
        GameRules.SetCustomGameAllowMusicAtGameStart(false); // 是否允许游戏开始时的音乐
        GameRules.SetCustomGameAllowHeroPickMusic(false); // 是否允许英雄选择阶段的音乐
        GameRules.SetCustomGameAllowBattleMusic(false); // 是否允许战斗阶段音乐
        GameRules.SetPostGameTime(30); // 游戏结束后时长
        GameRules.SetPreGameTime(0); // 进入游戏后号角吹响前的准备时间
        GameRules.SetHideKillMessageHeaders(true); // 是否隐藏顶部的英雄击杀信息
        GameRules.SetSameHeroSelectionEnabled(true)
        

        const mode: CDOTABaseGameMode = GameRules.GetGameModeEntity();
        mode.SetCustomGameForceHero("npc_dota_hero_wisp");
        mode.SetCameraZRange(0,6000);//相机渲染距离
        mode.SetForcedHUDSkin("default");//指定此游戏模式下强制打开的HUD皮肤，防止hud饰品残留
        mode.SetAnnouncerDisabled(true);//静音播音员
        mode.SetAnnouncerGameModeAnnounceDisabled(false);//自定义游戏开始时的模式播报/选择你的英雄
        mode.SetModifyGoldFilter((event: ModifyGoldFilterEvent):boolean=>{
            // print("获得gold")
            // DeepPrintTable(event)
            if (event.gold > 0 && event.reason_const == EDOTA_ModifyGold_Reason.DOTA_ModifyGold_Building) {
                return false
            }
            return true;
        },{});
        mode.SetDamageFilter((event: DamageFilterEvent):boolean=>{
            if (GameRules.fastWarGameStatus == FastWarGamePhase.MainTime 
                || GameRules.fastWarGameStatus == FastWarGamePhase.Overtime) {
                GameRules.NPCUtils.notifierTowerBeAttack(event.entindex_victim_const)
                return true
            } else if (GameRules.fastWarGameStatus == FastWarGamePhase.TempEnd) {
                return false
            } else if (GameRules.DebugOpen.debugDamage > 0) {
                let unit = EntIndexToHScript(event.entindex_attacker_const as EntityIndex) as CDOTA_BaseNPC;
                switch (GameRules.DebugOpen.debugDamage) {
                    case 1:
                        if (unit.GetTeam() == 3) {
                            return false
                        }
                        break
                    case 2:
                         if (unit.GetTeam() == 2) {
                            return false
                        }
                        break
                    case 3:
                        return false
                }
            }
            return true
        },{})
        mode.SetExecuteOrderFilter((event: ExecuteOrderFilterEvent):boolean=>{
            if (GameRules.DebugOpen.debugPrint) {
                let unit = EntIndexToHScript(Object.values(event.units)[0]) as CDOTA_BaseNPC
                if (event.order_type ==  dotaunitorder_t.DOTA_UNIT_ORDER_ATTACK_MOVE) {
                    GameRules.Debug.DebugPrint(unit.GetUnitName() + "移动攻击："+ event.position_x + "," + event.position_y)
                } else if (event.order_type ==  dotaunitorder_t.DOTA_UNIT_ORDER_ATTACK_TARGET) {
                    let a = EntIndexToHScript(event.entindex_target as EntityIndex) as CDOTA_BaseNPC
                    if (a != undefined && a != null) {
                        GameRules.Debug.DebugPrint(unit.GetUnitName() + "攻击准确目标："+ a.GetUnitName())
                    }
                } else {
                    let a = EntIndexToHScript(event.entindex_ability as EntityIndex) as CDOTABaseAbility
                    if (a != undefined && a != null && a.IsActivated) {
                        GameRules.Debug.DebugPrint(unit.GetUnitName() + "释放技能："+a.GetAbilityName())
                    }
                }
            }
            return true
        },{})

        // game.SetRemoveIllusionsOnDeath(true); // 是否在英雄死亡的时候移除幻象
        // game.SetSelectionGoldPenaltyEnabled(false); // 是否启用选择英雄时的金钱惩罚（超时每秒扣钱）
        // game.SetLoseGoldOnDeath(false); // 是否在英雄死亡时扣除金钱
        // game.SetBuybackEnabled(false); // 是否允许买活
        // game.SetDaynightCycleDisabled(true); // 是否禁用白天黑夜循环
        // game.SetForceRightClickAttackDisabled(true); // 是否禁用右键攻击
        // game.SetHudCombatEventsDisabled(true); // 是否禁用战斗事件（左下角的战斗消息）
        // game.SetCustomGameForceHero(`npc_dota_hero_phoenix`); // 设置强制英雄（会直接跳过英雄选择阶段并直接为所有玩家选择这个英雄）
        // game.SetUseCustomHeroLevels(true); // 是否启用自定义英雄等级
        // game.SetCustomHeroMaxLevel(1); // 设置自定义英雄最大等级
        // game.SetCustomXPRequiredToReachNextLevel({
        //     // 设置自定义英雄每个等级所需经验，这里的经验是升级到这一级所需要的*总经验）
        //     1: 0,
        // });
        // game.SetDaynightCycleDisabled(true); // 是否禁用白天黑夜循环
        // game.SetDeathOverlayDisabled(true); // 是否禁用死亡遮罩（灰色的遮罩）

        // 设置自定义的队伍人数上限，这里的设置是10个队伍，每个队伍1人
        GameRules.SetCustomGameTeamMaxPlayers(2, 1);
        GameRules.SetCustomGameTeamMaxPlayers(3, 1);
        // GameRules.SetCustomGameTeamMaxPlayers(number.CUSTOM_1, 1);
        // for (let team = number.CUSTOM_1; team <= number.CUSTOM_8; ++team) {
        //     GameRules.SetCustomGameTeamMaxPlayers(team, 1);
        // }
    }
}
