import { modifier_fw_freezing_field_debuff } from "../../modifiers/abilities/card_spell/modifier_fw_freezing_field_debuff";
import { BaseAbility, registerAbility } from "../../utils/dota_ts_adapter";
import { MathUtils } from "../../utils/math_utils";

export class fw_crystal_maiden_freezing_field extends BaseAbility {
    
    spell:SpellInfoKV;
    GetAOERadius(): number {
        return this.spell.AOERadius
    }

    OnSpellStart(): void {
        let duration = this.GetSpecialValueFor("duration")
        let damage_interval = this.GetSpecialValueFor("damage_interval")
        let damage = this.GetSpecialValueFor("damage")

        let hero = this.GetCaster()
        let ab = this
        let team = hero.GetTeam()
        let tarPos = this.GetCursorPosition()
        let radius = this.GetAOERadius()
        let tarTeam = this.GetAbilityTargetTeam()
        let tarFlag = this.GetAbilityTargetFlags()
        let damageType = this.GetAbilityDamageType()
        let fwTargetType = this.spell.FWTargetType
        let e = GameRules.SoundUtils.getSoundEntity(tarPos)
        
        let par = ParticleManager.CreateParticle("particles/spell/crystal_maiden_freezing_field/cm_persona_freezing_field_snow.vpcf", ParticleAttachment_t.PATTACH_WORLDORIGIN, undefined)
        ParticleManager.SetParticleControl(par, 0, tarPos)
        ParticleManager.SetParticleControl(par, 1, Vector(radius,duration,0))

        GameRules.FastWarSpell.startIntervalSpell(0,duration,damage_interval,
        ()=>{
            e.EmitSound("Fw.Cards.Spell.freezing_field")
        },()=>{
            let tars = FindUnitsInRadius(
                team,
                tarPos,
                undefined,
                radius,
                tarTeam,
                1 + 2 + 4,
                tarFlag,
                0,
                false,
            )
            for (const unit of tars) {
                if (unit.fastWarUnitType != undefined && (fwTargetType == GoFastWarAIUnitTypeEnum.ALL || unit.fastWarUnitType == fwTargetType)) {
                    unit.AddNewModifier(hero, ab, modifier_fw_freezing_field_debuff.name, {duration:damage_interval+0.5}) 
                    ApplyDamage({
                        victim: unit,
                        attacker: hero,
                        damage: damage,
                        damage_type: damageType,
                        damage_flags:DOTADamageFlag_t.DOTA_DAMAGE_FLAG_NONE,
                        ability:ab,
                    });
                }
            }
        },()=>{
            e.StopSound("Fw.Cards.Spell.freezing_field")
            GameRules.SoundUtils.backSoundEntity(e)
            ParticleManager.DestroyParticle(par, false)
        })
    }

    Spawn(): void {
        this.spell = GameRules.KVUtils.getSpellInfo("fw_crystal_maiden_freezing_field")
    }
    
    Precache(context: CScriptPrecacheContext): void {
        PrecacheResource("particle","particles/spell/crystal_maiden_freezing_field/cm_persona_freezing_field_snow.vpcf",context)
        PrecacheResource("particle","particles/spell/crystal_maiden_freezing_field/maiden_frostbite_buff.vpcf",context)
    }
}

