const fs = require('fs');
const path = require('path');

// 定义需要修复的模式和替换 - 使用正确的 Dota 2 枚举名称
const fixes = [
    // 修复单位目标常量 - 使用正确的枚举值
    { pattern: /DOTA_UNIT_TARGET_HERO/g, replacement: 'UnitTargetType.HERO' },
    { pattern: /DOTA_UNIT_TARGET_CREEP/g, replacement: 'UnitTargetType.CREEP' },
    { pattern: /DOTA_UNIT_TARGET_BUILDING/g, replacement: 'UnitTargetType.BUILDING' },
    { pattern: /DOTA_UNIT_TARGET\.DOTA_UNIT_TARGET_HEROES_AND_CREEPS/g, replacement: 'UnitTargetType.HERO | UnitTargetType.CREEP' },
    
    // 修复查找顺序常量
    { pattern: /FIND_ANY_ORDER/g, replacement: 'FindOrder.ANY' },
    { pattern: /FIND_CLOSEST/g, replacement: 'FindOrder.CLOSEST' },
    
    // 修复修饰符属性常量
    { pattern: /MODIFIER_PROPERTY_INCOMING_DAMAGE_PERCENTAGE/g, replacement: 'ModifierFunction.INCOMING_DAMAGE_PERCENTAGE' },
    { pattern: /MODIFIER_PROPERTY_ATTACKSPEED_BONUS_CONSTANT/g, replacement: 'ModifierFunction.ATTACKSPEED_BONUS_CONSTANT' },
    { pattern: /MODIFIER_PROPERTY_MOVESPEED_BONUS_CONSTANT/g, replacement: 'ModifierFunction.MOVESPEED_BONUS_CONSTANT' },
    { pattern: /MODIFIER_PROPERTY_MODEL_SCALE/g, replacement: 'ModifierFunction.MODEL_SCALE' },
    { pattern: /MODIFIER_PROPERTY_MODEL_SCALE_ANIMATE_TIME/g, replacement: 'ModifierFunction.MODEL_SCALE_ANIMATE_TIME' },
    { pattern: /MODIFIER_PROPERTY_MODEL_SCALE_USE_IN_OUT_EASE/g, replacement: 'ModifierFunction.MODEL_SCALE_USE_IN_OUT_EASE' },
    { pattern: /MODIFIER_PROPERTY_INVISIBILITY_LEVEL/g, replacement: 'ModifierFunction.INVISIBILITY_LEVEL' },
    { pattern: /MODIFIER_PROPERTY_PREATTACK_BONUS_DAMAGE/g, replacement: 'ModifierFunction.PREATTACK_BONUS_DAMAGE' },
    { pattern: /MODIFIER_PROPERTY_MAGICAL_RESISTANCE_BONUS/g, replacement: 'ModifierFunction.MAGICAL_RESISTANCE_BONUS' },
    { pattern: /MODIFIER_PROPERTY_OVERRIDE_ANIMATION/g, replacement: 'ModifierFunction.OVERRIDE_ANIMATION' },
    { pattern: /MODIFIER_PROPERTY_TRANSLATE_ACTIVITY_MODIFIERS/g, replacement: 'ModifierFunction.TRANSLATE_ACTIVITY_MODIFIERS' },
    { pattern: /MODIFIER_PROPERTY_VISUAL_Z_DELTA/g, replacement: 'ModifierFunction.VISUAL_Z_DELTA' },
    { pattern: /MODIFIER_PROPERTY_ATTACK_RANGE_BONUS/g, replacement: 'ModifierFunction.ATTACK_RANGE_BONUS' },
    { pattern: /MODIFIER_PROPERTY_INCOMING_DAMAGE_CONSTANT/g, replacement: 'ModifierFunction.INCOMING_DAMAGE_CONSTANT' },
    { pattern: /MODIFIER_PROPERTY_HEALTHBAR_PIPS/g, replacement: 'ModifierFunction.HEALTHBAR_PIPS' },
    { pattern: /MODIFIER_PROPERTY\.MODIFIER_PROPERTY_MOVESPEED_LIMIT/g, replacement: 'ModifierFunction.MOVESPEED_LIMIT' },
    { pattern: /MODIFIER_PROPERTY\.MODIFIER_PROPERTY_TRANSLATE_ATTACK_SOUND/g, replacement: 'ModifierFunction.TRANSLATE_ATTACK_SOUND' },
    { pattern: /MODIFIER_PROPERTY\.MODIFIER_PROPERTY_TOTALDAMAGEOUTGOING_PERCENTAGE/g, replacement: 'ModifierFunction.TOTALDAMAGEOUTGOING_PERCENTAGE' },
    { pattern: /MODIFIER_PROPERTY\.MODIFIER_PROPERTY_STATUS_RESISTANCE_STACKING/g, replacement: 'ModifierFunction.STATUS_RESISTANCE_STACKING' },
    
    // 修复修饰符状态常量
    { pattern: /MODIFIER_STATE_DISARMED/g, replacement: 'ModifierState.DISARMED' },
    { pattern: /MODIFIER_STATE_ATTACK_IMMUNE/g, replacement: 'ModifierState.ATTACK_IMMUNE' },
    { pattern: /MODIFIER_STATE_NO_HEALTH_BAR/g, replacement: 'ModifierState.NO_HEALTH_BAR' },
    { pattern: /MODIFIER_STATE_ROOTED/g, replacement: 'ModifierState.ROOTED' },
    { pattern: /MODIFIER_STATE_CANNOT_BE_MOTION_CONTROLLED/g, replacement: 'ModifierState.CANNOT_BE_MOTION_CONTROLLED' },
    { pattern: /MODIFIER_STATE_NOT_ON_MINIMAP/g, replacement: 'ModifierState.NOT_ON_MINIMAP' },
    { pattern: /MODIFIER_STATE_INVULNERABLE/g, replacement: 'ModifierState.INVULNERABLE' },
    { pattern: /MODIFIER_STATE_OUT_OF_GAME/g, replacement: 'ModifierState.OUT_OF_GAME' },
    { pattern: /MODIFIER_STATE_NO_UNIT_COLLISION/g, replacement: 'ModifierState.NO_UNIT_COLLISION' },
    { pattern: /MODIFIER_STATE_UNSELECTABLE/g, replacement: 'ModifierState.UNSELECTABLE' },
    { pattern: /MODIFIER_STATE_STUNNED/g, replacement: 'ModifierState.STUNNED' },
    { pattern: /MODIFIER_STATE_PASSIVES_DISABLED/g, replacement: 'ModifierState.PASSIVES_DISABLED' },
    { pattern: /MODIFIER_STATE_DEBUFF_IMMUNE/g, replacement: 'ModifierState.DEBUFF_IMMUNE' },
    { pattern: /MODIFIER_STATE_FROZEN/g, replacement: 'ModifierState.FROZEN' },
    { pattern: /MODIFIER_STATE_MAGIC_IMMUNE/g, replacement: 'ModifierState.MAGIC_IMMUNE' },
    
    // 修复修饰符属性常量
    { pattern: /MODIFIER_ATTRIBUTE_PERMANENT/g, replacement: 'ModifierAttribute.PERMANENT' },
    { pattern: /MODIFIER_ATTRIBUTE_IGNORE_INVULNERABLE/g, replacement: 'ModifierAttribute.IGNORE_INVULNERABLE' },
    { pattern: /MODIFIER_ATTRIBUTE_MULTIPLE/g, replacement: 'ModifierAttribute.MULTIPLE' },
    
    // 修复事件常量
    { pattern: /MODIFIER_EVENT_ON_DAMAGE_CALCULATED/g, replacement: 'ModifierFunction.ON_DAMAGE_CALCULATED' },
    { pattern: /MODIFIER_EVENT\.MODIFIER_EVENT_ON_ATTACK_START/g, replacement: 'ModifierFunction.ON_ATTACK_START' },
    { pattern: /MODIFIER_EVENT\.MODIFIER_EVENT_ON_ATTACK_LANDED/g, replacement: 'ModifierFunction.ON_ATTACK_LANDED' },
    { pattern: /MODIFIER_EVENT\.MODIFIER_EVENT_ON_ATTACK/g, replacement: 'ModifierFunction.ON_ATTACK' },
    
    // 修复 FCVAR 常量
    { pattern: /FCVAR_NONE/g, replacement: 'ConVarFlags.NONE' },
    
    // 修复 Lua 修饰符运动类型
    { pattern: /LUA_MODIFIER_MOTION_NONE/g, replacement: 'LuaModifierMotionType.NONE' },
    { pattern: /LUA_MODIFIER_MOTION_BOTH/g, replacement: 'LuaModifierMotionType.BOTH' },
    { pattern: /LUA_MODIFIER_MOTION_HORIZONTAL/g, replacement: 'LuaModifierMotionType.HORIZONTAL' },
    { pattern: /LUA_MODIFIER_MOTION_VERTICAL/g, replacement: 'LuaModifierMotionType.VERTICAL' },
];

// 递归遍历目录
function walkDir(dir, callback) {
    const files = fs.readdirSync(dir);
    files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        if (stat.isDirectory()) {
            walkDir(filePath, callback);
        } else if (stat.isFile() && (file.endsWith('.ts') || file.endsWith('.tsx'))) {
            callback(filePath);
        }
    });
}

// 修复文件
function fixFile(filePath) {
    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;
    
    fixes.forEach(fix => {
        if (fix.pattern.test(content)) {
            content = content.replace(fix.pattern, fix.replacement);
            changed = true;
        }
    });
    
    if (changed) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`Fixed all constants in: ${filePath}`);
    }
}

// 主函数
function main() {
    const srcDir = path.join(__dirname, 'src');
    console.log('Starting all constants fixes...');
    
    walkDir(srcDir, fixFile);
    
    console.log('All constants fixes completed!');
}

main();
