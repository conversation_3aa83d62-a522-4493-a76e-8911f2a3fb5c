import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";
import { MathUtils } from "../../../utils/math_utils";


export class modifier_fw_hero_spell_beastmaster_passive extends BaseModifier {

    par:ParticleID;
    attackspeed:number;
    selfIsGood:boolean;
    OnCreated(keys:any): void {
        let ab = this.GetAbility()
        if (IsServer()) {
            let unit = this.GetParent()
            this.par = ParticleManager.CreateParticle("particles/spell/fw_hero_spell_beastmaster/beastmaster_innerbeast_berserk_passive.vpcf",ParticleAttachment_t.PATTACH_ABSORIGIN_FOLLOW, unit)
            ParticleManager.SetParticleControlEnt(this.par, 0, unit, ParticleAttachment_t.PATTACH_ABSORIGIN_FOLLOW, "attach_hitloc", Vector(0,0,0), true)
            this.selfIsGood = keys.selfIsGood
            this.SetHasCustomTransmitterData(true)
        }
        this.attackspeed = ab.GetSpecialValueFor("attackspeed")
    } 

    GetTexture(): string {
        return "beastmaster_inner_beast"
    }
    
    AddCustomTransmitterData () {
        return {
            selfIsGood:this.selfIsGood,
        }
    }
    
    HandleCustomTransmitterData (data: any) {
        this.selfIsGood = data.selfIsGood
    }

    OnDestroy(): void {
        if (IsServer()) {
            ParticleManager.DestroyParticle(this.par,true)
        }
    }

    IsDebuff(): boolean {
        return false
    }

    IsHidden() {
        let y = this.GetParent().GetAbsOrigin().y
        if ((this.selfIsGood && y > 0) || (!this.selfIsGood && y < 0)) {//过了河道
            return true;
        } else {
            return false;
        }
    }

    IsPurgable(): boolean {
        return false;
    }

    IsPurgeException(): boolean {
        return false
    }

    RemoveOnDeath(): boolean {
        return true
    }

    GetModifierAttackSpeedBonus_Constant(): number {
        let y = this.GetParent().GetAbsOrigin().y
        if ((this.selfIsGood && y > 0) || (!this.selfIsGood && y < 0)) {//过了河道
            return 0;
        } else {
            return this.attackspeed
        }
    }

    DeclareFunctions(): ModifierFunction[] {
        return [
            1,
        ];
    }
    
}
