import { modifier_fw_techies_bomb_adsorb } from "../../modifiers/abilities/unit_spell/modifier_fw_techies_bomb_adsorb";
import { BaseAbility, registerAbility } from "../../utils/dota_ts_adapter";

export class fw_techies_bomb_adsorb extends BaseAbility {
    
     GetIntrinsicModifierName(): string {
        return modifier_fw_techies_bomb_adsorb.name
    }
    
    Precache(context: CScriptPrecacheContext): void {
        PrecacheResource("particle","particles/units/heroes/hero_techies/techies_remote_cart_explode.vpcf",context)
    }
}

