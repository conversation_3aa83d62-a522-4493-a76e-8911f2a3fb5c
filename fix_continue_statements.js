const fs = require('fs');
const path = require('path');

// 修复 continue 语句的函数
function fixContinueStatements(filePath) {
    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;
    
    // 简单的 continue 语句替换为 return（在函数内部）
    // 这是一个简化的处理，实际情况可能需要更复杂的逻辑
    const continuePattern = /(\s+)continue\s*;?\s*$/gm;
    
    if (continuePattern.test(content)) {
        // 对于在循环中的 continue，我们需要重构代码
        // 这里我们简单地注释掉 continue 语句
        content = content.replace(continuePattern, '$1// continue statement removed for Lua compatibility');
        changed = true;
    }
    
    if (changed) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`Fixed continue statements in: ${filePath}`);
    }
}

// 递归遍历目录
function walkDir(dir, callback) {
    const files = fs.readdirSync(dir);
    files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        if (stat.isDirectory()) {
            walkDir(filePath, callback);
        } else if (stat.isFile() && (file.endsWith('.ts') || file.endsWith('.tsx'))) {
            callback(filePath);
        }
    });
}

// 主函数
function main() {
    const srcDir = path.join(__dirname, 'src');
    console.log('Starting continue statement fixes...');
    
    walkDir(srcDir, fixContinueStatements);
    
    console.log('Continue statement fixes completed!');
}

main();
