import { BaseModifier, registerModifier } from "../utils/dota_ts_adapter";


export class modifier_pre_unit_alternative extends BaseModifier {


    // OnCreated(params: object): void {
    //     if (IsClient()) {
            
    //     }
    // }
    // OnRemoved(): void {
    //     if (IsClient()) {
    //         let hero = this.GetParent() as CDOTA_BaseNPC_Hero
            
    //     }
    // }

    GetAttributes(): ModifierAttribute {
        return 1 + 2 //+ 4
    }

    IsHidden():boolean {
        return true;
    }

    CheckState():Partial<Record<modifierstate, boolean>> {
        let state = {
            // [ModifierState.INVISIBLE]: true,
            [4]: true,
            [5]: true,
            [10]: true,
            [6]: true,
            // [8]: true,
            [7]: true,
            // [2]: true,
          }
        
        return state
    }


}
