import { MathUtils } from "./math_utils";


export class SoundUtils {


    private index:number = 0

    private spareEntity:CBaseEntity[] = []

    constructor() {
        
    }

    public getSoundEntity (pos:Vector) {
        let e:CBaseEntity;
        if (this.spareEntity.length <= 0) {
            e = this.createSoundEntity()
        } else {
            e = this.spareEntity.pop()
        }
        e.SetAbsOrigin(GetGroundPosition(pos,undefined).__sub(Vector(0,0,70)))
        return e
    }

    public backSoundEntity (e:CBaseEntity) {
        this.spareEntity.push(e)
    }


    public createSoundEntity () {
        this.index += 1
        return SpawnEntityFromTableSynchronous("prop_dynamic", {
            targetname:"fw_sound_entity_"+this.index,
            model:"maps/backgrounds/models/fairy_showcase_hero_shield/fairy_showcase_hero_shield.vmdl",
        }) 
    }
}