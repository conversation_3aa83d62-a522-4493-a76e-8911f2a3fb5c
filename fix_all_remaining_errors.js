const fs = require('fs');
const path = require('path');

// 定义需要修复的模式和替换
const fixes = [
    // 修复单位目标类型 - 使用正确的常量值
    { pattern: /UnitTargetType\.HERO/g, replacement: 'DOTA_UNIT_TARGET_HERO' },
    { pattern: /UnitTargetType\.CREEP/g, replacement: 'DOTA_UNIT_TARGET_CREEP' },
    { pattern: /UnitTargetType\.BUILDING/g, replacement: 'DOTA_UNIT_TARGET_BUILDING' },
    { pattern: /UnitTargetType\.HERO \| UnitTargetType\.CREEP/g, replacement: 'DOTA_UNIT_TARGET_HERO | DOTA_UNIT_TARGET_CREEP' },
    
    // 修复查找顺序常量
    { pattern: /FindOrder\.ANY/g, replacement: 'FIND_ANY_ORDER' },
    { pattern: /FindOrder\.CLOSEST/g, replacement: 'FIND_CLOSEST' },
    
    // 修复修饰符函数常量
    { pattern: /ModifierFunction\.INCOMING_DAMAGE_PERCENTAGE/g, replacement: 'MODIFIER_PROPERTY_INCOMING_DAMAGE_PERCENTAGE' },
    { pattern: /ModifierFunction\.ATTACKSPEED_BONUS_CONSTANT/g, replacement: 'MODIFIER_PROPERTY_ATTACKSPEED_BONUS_CONSTANT' },
    { pattern: /ModifierFunction\.MOVESPEED_BONUS_CONSTANT/g, replacement: 'MODIFIER_PROPERTY_MOVESPEED_BONUS_CONSTANT' },
    { pattern: /ModifierFunction\.MODEL_SCALE/g, replacement: 'MODIFIER_PROPERTY_MODEL_SCALE' },
    { pattern: /ModifierFunction\.MODEL_SCALE_ANIMATE_TIME/g, replacement: 'MODIFIER_PROPERTY_MODEL_SCALE_ANIMATE_TIME' },
    { pattern: /ModifierFunction\.MODEL_SCALE_USE_IN_OUT_EASE/g, replacement: 'MODIFIER_PROPERTY_MODEL_SCALE_USE_IN_OUT_EASE' },
    { pattern: /ModifierFunction\.INVISIBILITY_LEVEL/g, replacement: 'MODIFIER_PROPERTY_INVISIBILITY_LEVEL' },
    { pattern: /ModifierFunction\.PREATTACK_BONUS_DAMAGE/g, replacement: 'MODIFIER_PROPERTY_PREATTACK_BONUS_DAMAGE' },
    { pattern: /ModifierFunction\.MAGICAL_RESISTANCE_BONUS/g, replacement: 'MODIFIER_PROPERTY_MAGICAL_RESISTANCE_BONUS' },
    { pattern: /ModifierFunction\.OVERRIDE_ANIMATION/g, replacement: 'MODIFIER_PROPERTY_OVERRIDE_ANIMATION' },
    { pattern: /ModifierFunction\.TRANSLATE_ACTIVITY_MODIFIERS/g, replacement: 'MODIFIER_PROPERTY_TRANSLATE_ACTIVITY_MODIFIERS' },
    { pattern: /ModifierFunction\.VISUAL_Z_DELTA/g, replacement: 'MODIFIER_PROPERTY_VISUAL_Z_DELTA' },
    { pattern: /ModifierFunction\.ATTACK_RANGE_BONUS/g, replacement: 'MODIFIER_PROPERTY_ATTACK_RANGE_BONUS' },
    { pattern: /ModifierFunction\.INCOMING_DAMAGE_CONSTANT/g, replacement: 'MODIFIER_PROPERTY_INCOMING_DAMAGE_CONSTANT' },
    { pattern: /ModifierFunction\.HEALTHBAR_PIPS/g, replacement: 'MODIFIER_PROPERTY_HEALTHBAR_PIPS' },
    { pattern: /ModifierFunction\.MOVESPEED_LIMIT/g, replacement: 'MODIFIER_PROPERTY_MOVESPEED_LIMIT' },
    { pattern: /ModifierFunction\.TRANSLATE_ATTACK_SOUND/g, replacement: 'MODIFIER_PROPERTY_TRANSLATE_ATTACK_SOUND' },
    { pattern: /ModifierFunction\.TOTALDAMAGEOUTGOING_PERCENTAGE/g, replacement: 'MODIFIER_PROPERTY_TOTALDAMAGEOUTGOING_PERCENTAGE' },
    { pattern: /ModifierFunction\.STATUS_RESISTANCE_STACKING/g, replacement: 'MODIFIER_PROPERTY_STATUS_RESISTANCE_STACKING' },
    { pattern: /ModifierFunction\.ON_DAMAGE_CALCULATED/g, replacement: 'MODIFIER_EVENT_ON_DAMAGE_CALCULATED' },
    { pattern: /ModifierFunction\.ON_ATTACK_START/g, replacement: 'MODIFIER_EVENT_ON_ATTACK_START' },
    { pattern: /ModifierFunction\.ON_ATTACK_LANDED/g, replacement: 'MODIFIER_EVENT_ON_ATTACK_LANDED' },
    { pattern: /ModifierFunction\.ON_ATTACK/g, replacement: 'MODIFIER_EVENT_ON_ATTACK' },
    
    // 修复修饰符状态常量
    { pattern: /ModifierState\.DISARMED/g, replacement: 'MODIFIER_STATE_DISARMED' },
    { pattern: /ModifierState\.ATTACK_IMMUNE/g, replacement: 'MODIFIER_STATE_ATTACK_IMMUNE' },
    { pattern: /ModifierState\.NO_HEALTH_BAR/g, replacement: 'MODIFIER_STATE_NO_HEALTH_BAR' },
    { pattern: /ModifierState\.ROOTED/g, replacement: 'MODIFIER_STATE_ROOTED' },
    { pattern: /ModifierState\.CANNOT_BE_MOTION_CONTROLLED/g, replacement: 'MODIFIER_STATE_CANNOT_BE_MOTION_CONTROLLED' },
    { pattern: /ModifierState\.NOT_ON_MINIMAP/g, replacement: 'MODIFIER_STATE_NOT_ON_MINIMAP' },
    { pattern: /ModifierState\.INVULNERABLE/g, replacement: 'MODIFIER_STATE_INVULNERABLE' },
    { pattern: /ModifierState\.OUT_OF_GAME/g, replacement: 'MODIFIER_STATE_OUT_OF_GAME' },
    { pattern: /ModifierState\.NO_UNIT_COLLISION/g, replacement: 'MODIFIER_STATE_NO_UNIT_COLLISION' },
    { pattern: /ModifierState\.UNSELECTABLE/g, replacement: 'MODIFIER_STATE_UNSELECTABLE' },
    { pattern: /ModifierState\.STUNNED/g, replacement: 'MODIFIER_STATE_STUNNED' },
    { pattern: /ModifierState\.PASSIVES_DISABLED/g, replacement: 'MODIFIER_STATE_PASSIVES_DISABLED' },
    { pattern: /ModifierState\.DEBUFF_IMMUNE/g, replacement: 'MODIFIER_STATE_DEBUFF_IMMUNE' },
    { pattern: /ModifierState\.FROZEN/g, replacement: 'MODIFIER_STATE_FROZEN' },
    { pattern: /ModifierState\.MAGIC_IMMUNE/g, replacement: 'MODIFIER_STATE_MAGIC_IMMUNE' },
    
    // 修复修饰符属性常量
    { pattern: /ModifierAttribute\.PERMANENT/g, replacement: 'MODIFIER_ATTRIBUTE_PERMANENT' },
    { pattern: /ModifierAttribute\.IGNORE_INVULNERABLE/g, replacement: 'MODIFIER_ATTRIBUTE_IGNORE_INVULNERABLE' },
    { pattern: /ModifierAttribute\.MULTIPLE/g, replacement: 'MODIFIER_ATTRIBUTE_MULTIPLE' },
    
    // 修复 ConVar 标志
    { pattern: /ConVarFlags\.NONE/g, replacement: 'FCVAR_NONE' },
    
    // 修复 Lua 修饰符运动类型
    { pattern: /LuaModifierMotionType\.NONE/g, replacement: 'LUA_MODIFIER_MOTION_NONE' },
    { pattern: /LuaModifierMotionType\.BOTH/g, replacement: 'LUA_MODIFIER_MOTION_BOTH' },
    { pattern: /LuaModifierMotionType\.HORIZONTAL/g, replacement: 'LUA_MODIFIER_MOTION_HORIZONTAL' },
    { pattern: /LuaModifierMotionType\.VERTICAL/g, replacement: 'LUA_MODIFIER_MOTION_VERTICAL' },
    
    // 修复索引类型问题
    { pattern: /\[v\]/g, replacement: '[v as string]' },
    { pattern: /\[s\]/g, replacement: '[s as string]' },
    
    // 修复类型注解
    { pattern: /\(data\) =>/g, replacement: '(data: any) =>' },
    { pattern: /\(stateValue\) =>/g, replacement: '(stateValue: any) =>' },
    
    // 修复 DotaTeam 弃用问题
    { pattern: /DotaTeam/g, replacement: 'DOTATeam_t' },
];

// 递归遍历目录
function walkDir(dir, callback) {
    const files = fs.readdirSync(dir);
    files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        if (stat.isDirectory()) {
            walkDir(filePath, callback);
        } else if (stat.isFile() && (file.endsWith('.ts') || file.endsWith('.tsx'))) {
            callback(filePath);
        }
    });
}

// 修复文件
function fixFile(filePath) {
    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;
    
    fixes.forEach(fix => {
        if (fix.pattern.test(content)) {
            content = content.replace(fix.pattern, fix.replacement);
            changed = true;
        }
    });
    
    if (changed) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`Fixed all remaining errors in: ${filePath}`);
    }
}

// 主函数
function main() {
    const srcDir = path.join(__dirname, 'src');
    console.log('Starting all remaining error fixes...');
    
    walkDir(srcDir, fixFile);
    
    console.log('All remaining error fixes completed!');
}

main();
