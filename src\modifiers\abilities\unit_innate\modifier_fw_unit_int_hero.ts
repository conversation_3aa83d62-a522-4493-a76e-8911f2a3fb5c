import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";


export class modifier_fw_unit_int_hero extends BaseModifier {
    
    IsHidden() {
        return false;
    }
    
    attack_speed:number;
    OnCreated(params: object): void {
        this.attack_speed = this.GetAbility().GetSpecialValueFor("attack_speed")
        if (IsServer()) {
            Timers.CreateTimer(0.3,()=>{
                this.SetStackCount(this.GetParent().cardUsesNum)
            })
        }
    }

    IsDebuff(): boolean {
        return false;
    }

    IsPurgable(): boolean {
        return false;
    }

    IsPurgeException(): boolean {
        return false;
    }

    RemoveOnDeath(): boolean {
        return true
    }
    GetModifierAttackSpeedBonus_Constant(): number {
        return this.GetStackCount() * this.attack_speed
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            1,
        ];
    }
    
}
