import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import 'panorama-polyfill-x/lib/console';
import 'panorama-polyfill-x/lib/timers';
import { render } from 'react-panorama-x';
// 卡牌工具提示组件
function CardTooltip(props) {
    const { heroId, heroLevel } = props;
    return (_jsx(Panel, { id: "CardTooltipContainer", children: _jsxs(Panel, { className: "tooltip-content", children: [_jsxs(Panel, { className: "tooltip-header", children: [_jsx(Label, { text: `Hero: ${heroId || 'Unknown'}` }), _jsx(Label, { text: `Level: ${heroLevel || 1}` })] }), _jsxs(Panel, { className: "tooltip-body", children: [_jsxs(Panel, { className: "hero-info", children: [_jsx(Panel, { className: "hero-image", children: _jsx(DOTAHeroImage, { heroname: heroId || 'elder_titan' }) }), _jsxs(Panel, { className: "hero-details", children: [_jsx(Label, { className: "hero-name", text: heroId || 'Elder Titan' }), _jsx(Label, { className: "hero-level", text: `Level ${heroLevel || 1}` })] })] }), _jsx(Panel, { className: "tooltip-description", children: _jsx(Label, { text: "Card tooltip description goes here..." }) })] })] }) }));
}
// 工具提示加载事件处理
$.GetContextPanel().SetPanelEvent('ontooltiploaded', () => {
    $.Msg('ontooltiploaded');
    // 获取属性
    const heroId = $.GetContextPanel().GetAttributeString('heroId', 'elder_titan');
    const heroLevel = Number($.GetContextPanel().GetAttributeString('heroLevel', '1'));
    // 渲染 React 组件
    render(_jsx(CardTooltip, { heroId: heroId, heroLevel: heroLevel }), $.GetContextPanel());
});
//# sourceMappingURL=script.js.map