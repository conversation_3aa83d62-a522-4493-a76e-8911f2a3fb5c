"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("react");
require("panorama-polyfill-x/lib/console");
require("panorama-polyfill-x/lib/timers");
var react_panorama_x_1 = require("react-panorama-x");
function CardTooltip(props) {
    var heroId = props.heroId, heroLevel = props.heroLevel;
    return (react_1.default.createElement(Panel, { id: "CardTooltipContainer" },
        react_1.default.createElement(Panel, { className: "tooltip-content" },
            react_1.default.createElement(Panel, { className: "tooltip-header" },
                react_1.default.createElement(Label, { text: "Hero: ".concat(heroId || 'Unknown') }),
                react_1.default.createElement(Label, { text: "Level: ".concat(heroLevel || 1) })),
            react_1.default.createElement(Panel, { className: "tooltip-body" },
                react_1.default.createElement(Panel, { className: "hero-info" },
                    react_1.default.createElement(Panel, { className: "hero-image" },
                        react_1.default.createElement(DOTAHeroImage, { heroname: heroId || 'elder_titan' })),
                    react_1.default.createElement(Panel, { className: "hero-details" },
                        react_1.default.createElement(Label, { className: "hero-name", text: heroId || 'Elder Titan' }),
                        react_1.default.createElement(Label, { className: "hero-level", text: "Level ".concat(heroLevel || 1) }))),
                react_1.default.createElement(Panel, { className: "tooltip-description" },
                    react_1.default.createElement(Label, { text: "Card tooltip description goes here..." }))))));
}
$.GetContextPanel().SetPanelEvent('ontooltiploaded', function () {
    $.Msg('ontooltiploaded');
    var heroId = $.GetContextPanel().GetAttributeString('heroId', 'elder_titan');
    var heroLevel = Number($.GetContextPanel().GetAttributeString('heroLevel', '1'));
    (0, react_panorama_x_1.render)(react_1.default.createElement(CardTooltip, { heroId: heroId, heroLevel: heroLevel }), $.GetContextPanel());
});
