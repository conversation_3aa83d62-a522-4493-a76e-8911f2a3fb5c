
export class EntityUtils {

    /**
     *  举例：go_towerbadguys3
     */
    private Entitys:{
        [key:string]:CBaseEntity
    } = {};

    /**
     *  go_onlypos_bot_p6
     *  bot:
     *      p6:
     *          
     *  举例：go_onlypos_tower_badguys3
     *  tower：
     *      badguys1：
     *          {pos,angle}
     *      badguys2：
     *          {pos,angle}
     *      badguys3：
     *          {pos,angle}
     */
    public EntityOnlyPos:{
        [key:string]:{
            [key:string]:{
                pos:Vector,
                angle:QAngle,
            }
        }
    } = {};

    constructor () {
        let es = Entities.FindAllByClassname("prop_dynamic")
        let names:string[];
        print("初始化EntityUtils")
        for (const e of es) {
            names = e.GetName().split("_")
            // print(e.GetName())
            if (names.length > 1 && names[0] == "go") {
                if (names[1] == "onlypos") {
                    if (this.EntityOnlyPos[names[2]] == undefined) {
                        this.EntityOnlyPos[names[2]] = {}
                    }
                    // print("EntityOnlyPos[ "+names[2]+" ][ "+names[3]+" ]={pos:"+e.GetAbsOrigin().x+","+e.GetAbsOrigin().y+","+e.GetAbsOrigin().z+";angle:"+e.GetAngles().x+","+e.GetAngles().y+"}")
                    this.EntityOnlyPos[names[2]][names[3]] = {
                        pos:e.GetAbsOrigin(),
                        angle:e.GetAngles(),
                    }
                    e.Destroy()
                } else {
                    // print("Entitys[ "+names[1]+" ]={pos:"+e.GetAbsOrigin().x+","+e.GetAbsOrigin().y+","+e.GetAbsOrigin().z+";angle:"+e.GetAngles().x+","+e.GetAngles().y+"}")
                    this.Entitys[names[1]] = e
                }
            }
        }
    }

    public getTowerPos (towerIndex:number, isGoodGuy:boolean) {
        let str = (isGoodGuy?"goodguys":"badguys") + towerIndex
        return this.EntityOnlyPos["tower"][str].pos
    }

    public getAICardPos (indexs:number[], towerDestroy:boolean, LorR:boolean, isGood:boolean, attackStr:boolean) {
        GameRules.Debug.DebugPrint("传入点位："+indexs.join(",")+",towerDestroy:"+towerDestroy+",LorR:"+LorR+",isGood:"+isGood+",isAttack:"+attackStr)
        let res = indexs.filter((v,index)=>{
            if (!towerDestroy && v <= 4) {
                return false
            }
            return true
        })
        if (res.length <= 0) {
            if (attackStr) {
                res = towerDestroy?[1,2,3,4,5,6,7]:[5,6,7]
            } else {
                res = [8,9,10,11,12]
            }
        }
        GameRules.Debug.DebugPrint("处理后剩余可用点位："+res.join(","))
        return res.map((v,index)=>{
            let p = this.EntityOnlyPos["bot"]["p"+v].pos
            let res = Vector(p.x,p.y,p.z)
            if (isGood) {
                if (!LorR) {
                    res.x = -p.x
                }
            } else {
                if (LorR) {
                    res.x = -p.x
                }
                res.y = -p.y
            }
            return res
        })
    }

}