import * as tstl from "typescript-to-lua";

const plugin: tstl.Plugin = {
    beforeEmit: (program, options, emitHost, result) => {
        // 添加编译时间戳到输出文件
        const timestamp = new Date().toISOString();
        
        // 为每个输出文件添加时间戳注释
        for (const file of result) {
            if (file.code) {
                file.code = `-- Compiled at: ${timestamp}\n${file.code}`;
            }
        }
    }
};

export default plugin;
