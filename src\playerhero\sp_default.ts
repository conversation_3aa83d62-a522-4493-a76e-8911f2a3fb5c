import { modifier_fw_hero_spell_beastmaster_passive } from "../modifiers/abilities/hero_spell/modifier_fw_hero_spell_beastmaster_passive";
import { modifier_fw_hero_spell_blacksmith } from "../modifiers/abilities/hero_spell/modifier_fw_hero_spell_blacksmith";
import { modifier_fw_hero_spell_lycan } from "../modifiers/abilities/hero_spell/modifier_fw_hero_spell_lycan";
import { PlayerHeroSpellHandle } from "./playerherospellhandle";


/**
 * 基本技能
 * 1. 固定释放逻辑
 */
export class PlayerHeroDefault extends PlayerHeroSpellHandle {

    AfterSpellUsing(): void {
        
    }


    GetCardBuff(data:{cardUIType:string}):ModifierData[] { 
        if (this.ab == undefined) {
            this.ab = this.hero.FindAbilityByName(this.spellInfo.SpellName)
        }
        if (this.spellInfo.SpellName == "fw_hero_spell_lycan") {
            if (data.cardUIType == "creep") {
                return [
                    {
                        ab:this.ab,
                        caster:this.hero,
                        modifierName:modifier_fw_hero_spell_lycan.name,
                        data:{} 
                    }
                ]
            }
        } else if (this.spellInfo.SpellName == "fw_hero_spell_beastmaster") {
                return [
                    {
                        ab:this.ab,
                        caster:this.hero,
                        modifierName:modifier_fw_hero_spell_beastmaster_passive.name,
                        data:{selfIsGood:this.selfIsGood} 
                    }
                ]
        }
        return []
    }

    private cardInfo:CardInfoKV;
    private ab:CDOTABaseAbility;
    private selfIsGood:boolean;
    constructor (playerId:PlayerID,spellInfo:PlayerHeroForKV
    ) {
        super(playerId,spellInfo)
        this.selfIsGood = !GameRules.FastWarPhaseController.GameTeamGroup[PlayerResource.GetTeam(playerId)].enemyIsGoodGuy
        this.cardInfo = GameRules.KVUtils.getCardsInfo(spellInfo.CardIndex[0])
        let sp = GameRules.KVUtils.getSpellInfo(spellInfo.SpellName)
        this.nowSpellInfo = {
            HeroName: spellInfo.HeroName,
            HeroImage: spellInfo.HeroImage,
            SpellCardIndex: spellInfo.CardIndex[0],
            SpellName: spellInfo.SpellName,
            cost: this.cardInfo.layCost,
            nextUsingTime: GameRules.GetGameTime() + sp.CoolDown,
            cooldown:sp.CoolDown,
            num:"",
            usesNum:0,
        }
    }


}