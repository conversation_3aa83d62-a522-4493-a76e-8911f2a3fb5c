import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";


export class modifier_fw_time_travel extends BaseModifier {


    exModel:CBaseEntity;
    exV:Vector;
    OnCreated(params: any): void {
        if (IsServer()) {
            let posX = params.posX
            let posY = params.posY
            let hero = this.GetParent()
            // let par = ParticleManager.CreateParticle("particles/spell/time_travel/travel_start_alter.vpcf", ParticleAttachment_t.PATTACH_ABSORIGIN_FOLLOW, undefined)
            // ParticleManager.SetParticleControlEnt(par, 0 ,hero, ParticleAttachment_t.PATTACH_ABSORIGIN_FOLLOW, "attach_hitloc", Vector(0,0,0), true)
            // ParticleManager.SetParticleControl(par, 1 ,GetGroundPosition(Vector(posX, posY, 0), hero))
            let ex = GameRules.KVUtils.getSpecialUnitInfoTreated(hero.GetUnitName()).ExtraModel
            for (const v of hero.GetChildren()) {
                if (v.GetModelName() == ex) {
                    this.exModel = v
                }
            }
            if (this.exModel != undefined) {
                this.exV = this.exModel.GetAbsOrigin()
                this.exModel.SetAbsOrigin(Vector(0,0,-200));
            }
            hero.AddNoDraw()
        }
    }
    OnDestroy(): void {
        if (IsServer()) {
            if (this.exModel != undefined) {
                this.exModel.SetAbsOrigin(this.exV);
            }
            this.GetParent().RemoveNoDraw()
        }
    }

    GetAttributes(): ModifierAttribute {
        return 1  //+ 4
    }
    IsHidden():boolean {
        return false;
    }
    RemoveOnDeath(): boolean {
        return true;
    }
    IsDebuff(): boolean {
        return false;
    }
    IsPurgable(): boolean {
        return false
    }
    IsPurgeException(): boolean {
        return false
    }

    CheckState():Partial<Record<modifierstate, boolean>> {
        let state = {
            [3]: true,
            [13]: true,
            [4]: true,
            [5]: true,
            [10]: true,
            [6]: true,
            [14]: true,
            [1]: true,
            [12]: true,
            [8]: true,
            [7]: true,
            [2]: true,
          }
        
        return state
    }


}
