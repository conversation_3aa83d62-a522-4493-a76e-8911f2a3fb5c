
import { modifier_fw_luna_eclipse } from "../../modifiers/abilities/card_spell/modifier_fw_luna_eclipse";
import { BaseAbility, registerAbility } from "../../utils/dota_ts_adapter";
import { fw_dragon_knight_fireball } from "./fw_dragon_knight_fireball";

export class fw_luna_eclipse extends BaseAbility {
    
    spell:SpellInfoKV;
    GetAOERadius(): number {
        return this.spell.AOERadius
    }

    OnSpellStart(): void {
        let duration = this.GetSpecialValueFor("duration")
        let damage_interval = this.GetSpecialValueFor("damage_interval")
        let damage = this.GetSpecialValueFor("damage")

        let hero = this.GetCaster()
        let ab = this
        let team = hero.GetTeam()
        let tarPos = this.GetCursorPosition()
        let radius = this.GetAOERadius()
        let tarTeam = this.GetAbilityTargetTeam()
        let tarFlag = this.GetAbilityTargetFlags()
        let damageType = this.GetAbilityDamageType()
        let fwTargetType = this.spell.FWTargetType
        let e = GameRules.SoundUtils.getSoundEntity(tarPos)
        
        let par = ParticleManager.CreateParticle("particles/spell/luna_eclipse/tbomb_bomb.vpcf", ParticleAttachment_t.PATTACH_WORLDORIGIN, undefined)
        ParticleManager.SetParticleControl(par, 0, tarPos)
        ParticleManager.SetParticleControl(par, 1, Vector(radius,duration,0))

        GameRules.FastWarSpell.startIntervalSpell(0,duration,damage_interval,
        ()=>{
            e.EmitSound("Fw.Cards.Spell.eclipse.cast")
        },()=>{
            let tars = FindUnitsInRadius(
                team,
                tarPos,
                undefined,
                radius,
                tarTeam,
                1 + 2 + 4,
                tarFlag,
                0,
                false,
            )
            if (tars.length > 0) {
                let u: any[] = []
                for (const unit of tars) {
                    if (unit.GetTeam() == hero.GetTeam()) {
                        unit.AddNewModifier(hero, ab, modifier_fw_luna_eclipse.name, {duration:damage_interval+1}) 
                    } else {
                        if (unit.fastWarUnitType != undefined && (fwTargetType == GoFastWarAIUnitTypeEnum.ALL || unit.fastWarUnitType == fwTargetType)) {
                            u.push(unit)
                        }
                    }
                }
                if (u.length > 0) {
                    e.EmitSound("Fw.Cards.Spell.eclipse.impact")
                    let tar = u[RandomInt(0,u.length-1)]
                    let par2 = ParticleManager.CreateParticle("particles/units/heroes/hero_luna/luna_eclipse_impact.vpcf", ParticleAttachment_t.PATTACH_WORLDORIGIN, undefined)
                    ParticleManager.SetParticleControl(par2, 1, tar.GetAbsOrigin())
                    ParticleManager.SetParticleControl(par2, 5, tar.GetAbsOrigin())
                    ApplyDamage({
                        victim: tar,
                        attacker: hero,
                        damage: damage,
                        damage_type: damageType,
                        damage_flags:DOTADamageFlag_t.DOTA_DAMAGE_FLAG_NONE,
                        ability:ab,
                    });
                    return 
                }
            }
            e.EmitSound("Fw.Cards.Spell.eclipse.miss")
            let par3 = ParticleManager.CreateParticle("particles/units/heroes/hero_luna/luna_eclipse_impact.vpcf", ParticleAttachment_t.PATTACH_WORLDORIGIN, undefined)
            let p = tarPos.__add(RandomVector(RandomInt(0,radius)))
            ParticleManager.SetParticleControl(par3, 1, p)
            ParticleManager.SetParticleControl(par3, 5, p)

        },()=>{
            e.StopSound("Fw.Cards.Spell.eclipse.cast")
            GameRules.SoundUtils.backSoundEntity(e)
            ParticleManager.DestroyParticle(par, false)
        })


    }

    Spawn(): void {
        this.spell = GameRules.KVUtils.getSpellInfo("fw_luna_eclipse")
    }
    
    Precache(context: CScriptPrecacheContext): void {
        PrecacheResource("particle","particles/spell/luna_eclipse/tbomb_bomb.vpcf",context)
        PrecacheResource("particle","particles/units/heroes/hero_luna/luna_eclipse_impact.vpcf",context)
    }
}

