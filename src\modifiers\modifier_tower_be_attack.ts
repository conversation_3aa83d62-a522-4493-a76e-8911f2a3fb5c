import { BaseModifier, registerModifier } from "../utils/dota_ts_adapter";


export class modifier_tower_be_attack extends BaseModifier {
    

    GetTexture(): string {
        return "backdoor_protection"
    }
    
    IsHidden() {
        return false;
    }

    AllowIllusionDuplicate () {
        return true;
    }
    
    CheckState() {
        return {
            [0]: true,
        }   
    }

    OnDamageCalculated(event: ModifierAttackEvent): void {
        if (event.target.entindex() == this.GetParent().entindex()) {
            print("防御塔受伤！")
            this.Destroy()
        }
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
                19,
            ];
    }

   
    
}
