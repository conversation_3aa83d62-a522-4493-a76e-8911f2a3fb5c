import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useEffect, useState } from 'react';
import { render } from 'react-panorama-x';
// 游戏状态管理
class GameStore {
    constructor() {
        this.state = {
            currentTurn: 1,
            player<PERSON>ana: 10,
            playerHealth: 30,
            handCards: [],
            selectedCard: undefined,
            hoveredCard: undefined,
            draggedCard: undefined,
        };
        this.listeners = [];
    }
    getState() {
        return Object.assign({}, this.state);
    }
    setState(updater) {
        updater(this.state);
        this.notifyListeners();
    }
    subscribe(listener) {
        this.listeners.push(listener);
        return () => {
            const index = this.listeners.indexOf(listener);
            if (index > -1) {
                this.listeners.splice(index, 1);
            }
        };
    }
    notifyListeners() {
        this.listeners.forEach(listener => listener());
    }
    // Actions
    setPlayerMana(mana) {
        this.setState(state => {
            state.playerMana = mana;
        });
    }
    setPlayerHealth(health) {
        this.setState(state => {
            state.playerHealth = health;
        });
    }
    addCardToHand(card) {
        this.setState(state => {
            state.handCards.push(card);
        });
    }
    removeCardFromHand(cardIndex) {
        this.setState(state => {
            state.handCards.splice(cardIndex, 1);
        });
    }
    playCard(cardIndex) {
        this.setState(state => {
            const card = state.handCards[cardIndex];
            if (card && state.playerMana >= card.LayCost) {
                state.playerMana -= card.LayCost;
                state.handCards.splice(cardIndex, 1);
                // 通知服务器端播放卡牌
                GameEvents.SendCustomGameEventToServer('play_card', {
                    cardName: card.Name,
                    cardIndex: cardIndex
                });
            }
        });
    }
    selectCard(card) {
        this.setState(state => {
            state.selectedCard = card;
        });
    }
    hoverCard(card) {
        this.setState(state => {
            state.hoveredCard = card;
        });
    }
    startDragCard(card) {
        this.setState(state => {
            state.draggedCard = card;
        });
    }
    endDragCard() {
        this.setState(state => {
            state.draggedCard = undefined;
        });
    }
    initializeGame() {
        console.log('=== Initializing Game State ===');
        this.setState(state => {
            state.currentTurn = 1;
            state.playerMana = 10;
            state.playerHealth = 30;
            // 添加一些测试卡牌数据
            console.log('Adding test cards to hand...');
            state.handCards = [
                {
                    Name: 'test_unit_1',
                    ChName: '测试单位',
                    LayCost: 3,
                    CardType: 'unit',
                    CardUIType: 'creep',
                    Enable: 1,
                    EnableForDeckCards: 1,
                    UsingAreaLimit: 0,
                    UnitCardInfo: {
                        Delay: 2,
                        LayType: 'lay_row',
                        CoverTemplate: 0,
                        LayGroupNum: 1,
                        Template1: 'npc_dota_creep_goodguys_melee',
                        Num1: 2,
                    },
                    SpellCardInfo: {},
                    ExtraData: {},
                    BotCardAI: { CardTag: 'test' },
                },
                {
                    Name: 'test_spell_1',
                    ChName: '测试法术',
                    LayCost: 5,
                    CardType: 'spell',
                    CardUIType: 'spell',
                    Enable: 1,
                    EnableForDeckCards: 1,
                    UsingAreaLimit: 0,
                    UnitCardInfo: {
                        Delay: 0,
                        LayType: 'lay_row',
                        CoverTemplate: 0,
                        LayGroupNum: 1,
                        Template1: '',
                        Num1: 1,
                    },
                    SpellCardInfo: {
                        SpellTemplate: 'fireball_spell',
                    },
                    ExtraData: {},
                    BotCardAI: { CardTag: 'spell' },
                },
                {
                    Name: 'test_hero_1',
                    ChName: '测试英雄',
                    LayCost: 7,
                    CardType: 'unit',
                    CardUIType: 'hero',
                    Enable: 1,
                    EnableForDeckCards: 1,
                    UsingAreaLimit: 0,
                    UnitCardInfo: {
                        Delay: 3,
                        LayType: 'lay_row',
                        CoverTemplate: 0,
                        LayGroupNum: 1,
                        Template1: 'npc_dota_hero_pudge',
                        Num1: 1,
                    },
                    SpellCardInfo: {},
                    ExtraData: {},
                    BotCardAI: { CardTag: 'hero' },
                },
            ];
            state.selectedCard = undefined;
            state.hoveredCard = undefined;
            state.draggedCard = undefined;
            console.log('Game state initialized:', {
                currentTurn: state.currentTurn,
                playerMana: state.playerMana,
                playerHealth: state.playerHealth,
                handCardsCount: state.handCards.length,
                handCards: state.handCards.map(card => card.ChName)
            });
        });
        console.log('=== Game State Initialization Complete ===');
    }
}
// 全局游戏状态实例
const gameStore = new GameStore();
// 自定义 Hook 用于订阅状态变化
function useGameStore() {
    const [state, setState] = useState(gameStore.getState());
    useEffect(() => {
        const unsubscribe = gameStore.subscribe(() => {
            setState(gameStore.getState());
        });
        return unsubscribe;
    }, []);
    return Object.assign(Object.assign({}, state), { setPlayerMana: gameStore.setPlayerMana.bind(gameStore), setPlayerHealth: gameStore.setPlayerHealth.bind(gameStore), addCardToHand: gameStore.addCardToHand.bind(gameStore), removeCardFromHand: gameStore.removeCardFromHand.bind(gameStore), playCard: gameStore.playCard.bind(gameStore), selectCard: gameStore.selectCard.bind(gameStore), hoverCard: gameStore.hoverCard.bind(gameStore), startDragCard: gameStore.startDragCard.bind(gameStore), endDragCard: gameStore.endDragCard.bind(gameStore), initializeGame: gameStore.initializeGame.bind(gameStore) });
}
const CardComponent = ({ card, index, isPlayable, isSelected, isHovered, onPlay, onSelect, onHover, onDragStart, onDragEnd }) => {
    const handleClick = () => {
        onSelect(card);
        if (isPlayable) {
            onPlay(index);
        }
    };
    const handleMouseEnter = () => {
        onHover(card);
    };
    const handleMouseLeave = () => {
        onHover(undefined);
    };
    const handleDragStart = () => {
        onDragStart(card);
    };
    const handleDragEnd = () => {
        onDragEnd();
    };
    const getCardTypeIcon = () => {
        switch (card.CardUIType) {
            case 'hero': return '👤';
            case 'creep': return '🐾';
            case 'building': return '🏢';
            case 'spell': return '✨';
            default: return '❓';
        }
    };
    const cardClasses = [
        'card',
        `card--${card.CardType}`,
        `card--${card.CardUIType}`,
        isSelected && 'card--selected',
        isHovered && 'card--hovered',
        isPlayable && 'card--playable',
        !isPlayable && 'card--unplayable'
    ].filter(Boolean).join(' ');
    return (_jsxs(Panel, { className: cardClasses, onactivate: handleClick, onmouseover: handleMouseEnter, onmouseout: handleMouseLeave, draggable: isPlayable, children: [_jsxs(Panel, { className: "card__header", children: [_jsx(Label, { className: "card__cost", text: card.LayCost.toString() }), _jsx(Label, { className: "card__type-icon", text: getCardTypeIcon() })] }), _jsxs(Panel, { className: "card__body", children: [_jsx(Panel, { className: "card__image", children: _jsx(Panel, { className: "card__placeholder-image", children: _jsx(Label, { text: getCardTypeIcon() }) }) }), _jsxs(Panel, { className: "card__info", children: [_jsx(Label, { className: "card__name", text: card.ChName }), _jsx(Label, { className: "card__description", text: card.CardType === 'unit'
                                    ? card.UnitCardInfo.Template1
                                    : card.SpellCardInfo.SpellTemplate || '' })] })] }), card.CardType === 'unit' && (_jsxs(Panel, { className: "card__stats", children: [card.UnitCardInfo.Num1 > 1 && (_jsx(Label, { className: "card__count", text: `×${card.UnitCardInfo.Num1}` })), card.UnitCardInfo.Delay && (_jsx(Label, { className: "card__delay", text: `⏱${card.UnitCardInfo.Delay}s` }))] })), _jsx(Panel, { className: "card__footer", children: _jsx(Label, { className: "card__rarity", text: card.CardUIType }) })] }));
};
// 主应用组件
const FastWarApp = () => {
    const { currentTurn, playerMana, playerHealth, handCards, selectedCard, hoveredCard, setPlayerMana, setPlayerHealth, addCardToHand, playCard, selectCard, hoverCard, startDragCard, endDragCard, initializeGame, } = useGameStore();
    useEffect(() => {
        // 初始化游戏
        initializeGame();
        // 监听游戏事件
        const gameEventListeners = [
            GameEvents.Subscribe('player_mana_changed', (event) => {
                setPlayerMana(event.mana);
            }),
            GameEvents.Subscribe('player_health_changed', (event) => {
                setPlayerHealth(event.health);
            }),
            GameEvents.Subscribe('card_added_to_hand', (event) => {
                addCardToHand(event.card);
            }),
        ];
        // 监听 NetTable 变化
        const netTableListener = CustomNetTables.SubscribeNetTableListener('game_state', (tableName, key, value) => {
            if (key === 'player_mana') {
                setPlayerMana(value);
            }
            else if (key === 'player_health') {
                setPlayerHealth(value);
            }
        });
        // 清理函数
        return () => {
            gameEventListeners.forEach(id => GameEvents.Unsubscribe(id));
            CustomNetTables.UnsubscribeNetTableListener(netTableListener);
        };
    }, []);
    const handleCardPlay = (cardIndex) => {
        playCard(cardIndex);
    };
    const handleCardSelect = (card) => {
        selectCard((selectedCard === null || selectedCard === void 0 ? void 0 : selectedCard.Name) === card.Name ? undefined : card);
    };
    const handleCardHover = (card) => {
        hoverCard(card);
    };
    const handleCardDragStart = (card) => {
        startDragCard(card);
    };
    const handleCardDragEnd = () => {
        endDragCard();
    };
    return (_jsxs(Panel, { className: "fast-war-app", children: [_jsx(Panel, { className: "game-info", children: _jsxs(Panel, { className: "player-stats", children: [_jsxs(Panel, { className: "stat", children: [_jsx(Label, { className: "stat__label", text: "\u56DE\u5408" }), _jsx(Label, { className: "stat__value", text: currentTurn.toString() })] }), _jsxs(Panel, { className: "stat", children: [_jsx(Label, { className: "stat__label", text: "\u6CD5\u529B" }), _jsx(Label, { className: "stat__value", text: playerMana.toString() })] }), _jsxs(Panel, { className: "stat", children: [_jsx(Label, { className: "stat__label", text: "\u751F\u547D" }), _jsx(Label, { className: "stat__value", text: playerHealth.toString() })] })] }) }), _jsx(Panel, { className: "battlefield", children: _jsxs(Panel, { className: "battlefield__content", children: [_jsx(Label, { text: "\u6218\u573A" }), _jsx(Label, { text: "\u62D6\u62FD\u5361\u724C\u5230\u6B64\u5904\u4F7F\u7528" })] }) }), _jsxs(Panel, { className: "hand-area", children: [_jsx(Label, { text: `手牌 (${handCards.length})` }), _jsx(Panel, { className: "hand-cards", children: handCards.map((card, index) => (_jsx(CardComponent, { card: card, index: index, isPlayable: playerMana >= card.LayCost, isSelected: (selectedCard === null || selectedCard === void 0 ? void 0 : selectedCard.Name) === card.Name, isHovered: (hoveredCard === null || hoveredCard === void 0 ? void 0 : hoveredCard.Name) === card.Name, onPlay: handleCardPlay, onSelect: handleCardSelect, onHover: handleCardHover, onDragStart: handleCardDragStart, onDragEnd: handleCardDragEnd }, `${card.Name}-${index}`))) })] }), selectedCard && (_jsxs(Panel, { className: "card-detail", children: [_jsx(Label, { text: "\u5361\u724C\u8BE6\u60C5" }), _jsxs(Panel, { className: "card-detail__content", children: [_jsx(Label, { text: selectedCard.ChName }), _jsx(Label, { text: `费用: ${selectedCard.LayCost}` }), _jsx(Label, { text: `类型: ${selectedCard.CardUIType}` }), _jsx(Label, { text: `内部名称: ${selectedCard.Name}` }), selectedCard.CardType === 'unit' && (_jsxs(_Fragment, { children: [_jsx(Label, { text: `模板: ${selectedCard.UnitCardInfo.Template1}` }), _jsx(Label, { text: `数量: ${selectedCard.UnitCardInfo.Num1}` }), _jsx(Label, { text: `延迟: ${selectedCard.UnitCardInfo.Delay}s` })] })), selectedCard.CardType === 'spell' && (_jsx(Label, { text: `法术: ${selectedCard.SpellCardInfo.SpellTemplate || ''}` }))] })] }))] }));
};
// 初始化和渲染
function initializeFastWar() {
    console.log('=== Fast War Initialization Started ===');
    const rootPanel = $.GetContextPanel();
    console.log('Root panel:', rootPanel);

    // 查找目标容器
    let targetPanel = rootPanel.FindChildInLayoutFile('FastWarApp');
    console.log('Found existing FastWarApp panel:', targetPanel);

    if (!targetPanel) {
        // 如果没有找到指定容器，创建一个
        console.log('Creating new FastWarApp panel...');
        targetPanel = $.CreatePanel('Panel', rootPanel, 'FastWarApp');
        targetPanel.AddClass('fast-war-app');
        console.log('Created panel:', targetPanel);
    }

    // 设置面板属性
    targetPanel.SetHasClass('hidden', false);
    targetPanel.style.width = '100%';
    targetPanel.style.height = '100%';
    targetPanel.style.backgroundColor = '#1a1a2e';

    console.log('Target panel properties:', {
        id: targetPanel.id,
        visible: targetPanel.visible,
        width: targetPanel.style.width,
        height: targetPanel.style.height
    });

    // 渲染 React 应用到 Panorama
    console.log('Rendering React app...');
    render(_jsx(FastWarApp, {}), targetPanel);

    console.log('=== Fast War UI initialized successfully ===');
}
// 当面板加载完成时初始化
(function () {
    if ($.GetContextPanel()) {
        initializeFastWar();
    }
    else {
        $.Schedule(0.1, initializeFastWar);
    }
})();
//# sourceMappingURL=fast_war.js.map