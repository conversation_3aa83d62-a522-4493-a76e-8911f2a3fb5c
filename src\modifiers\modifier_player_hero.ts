import { BaseModifier, registerModifier } from "../utils/dota_ts_adapter";


export class modifier_player_hero extends BaseModifier {


    // OnCreated(params: object): void {
    //     if (IsClient()) {
            
    //     }
    // }
    // OnRemoved(): void {
    //     if (IsClient()) {
    //         let hero = this.GetParent() as CDOTA_BaseNPC_Hero
            
    //     }
    // }

    IsDebuff(): boolean {
        return false
    }
    
    IsPurgable(): boolean {
        return false
    }

    IsPurgeException(): boolean {
        return false;
    }

    GetAttributes(): ModifierAttribute {
        return 1 + 2 //+ 4
    }

    IsHidden():boolean {
        return false;
    }



    CheckState():Partial<Record<modifierstate, boolean>> {
        let state = {
            // [ModifierState.INVISIBLE]: true,
            // [15]: true,
            // [4]: true,
            [5]: true,
            // [10]: true,
            // [6]: true,
            [8]: true,
            [7]: true,
            [0]: true,
            [2]: true,
            // [14]: true,
            // [12]: true,
            [9]: true,
            [4]: true,
          }
        
        return state
    }

    GetModifierIncomingDamageConstant(event: ModifierAttackEvent): number {
        if (IsClient()) {
            return 0
        } else {
            return -event.damage
        }
    }

    RemoveOnDeath(): boolean {
        return true
    }


    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            13,
            ];
    }


}
