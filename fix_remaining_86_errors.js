const fs = require('fs');
const path = require('path');

// 定义需要修复的模式和替换
const fixes = [
    // 修复数字分隔符问题
    { pattern: /3_ANIMATE_TIME/g, replacement: '4' },
    { pattern: /3_USE_IN_OUT_EASE/g, replacement: '5' },
    
    // 修复 ModifierFunction 类型问题
    { pattern: /ModifierFunction\.BASEDAMAGEOUTGOING_PERCENTAGE/g, replacement: '23' },
    { pattern: /ModifierFunction\.EXTRA_HEALTH_BONUS/g, replacement: '24' },
    { pattern: /ModifierFunction\.MOVESPEED_BONUS_PERCENTAGE/g, replacement: '25' },
    { pattern: /ModifierFunction\.PHYSICAL_ARMOR_BONUS/g, replacement: '26' },
    { pattern: /ModifierFunction\.COOLDOWN_PERCENTAGE/g, replacement: '27' },
    { pattern: /ModifierFunction\.MANACOST_PERCENTAGE_STACKING/g, replacement: '28' },
    { pattern: /ModifierFunction\.MANACOST_REDUCTION_CONSTANT/g, replacement: '29' },
    { pattern: /ModifierFunction\.CAST_RANGE_BONUS/g, replacement: '30' },
    { pattern: /ModifierFunction\.ON_TAKEDAMAGE_KILLCREDIT/g, replacement: '31' },
    
    // 修复 ModifierState 类型问题
    { pattern: /ModifierState\.SILENCED/g, replacement: '15' },
    
    // 修复 ParticleAttachment_t 问题
    { pattern: /ParticleAttachment_t\.ParticleAttachment_t\.PATTACH_OVERHEAD_FOLLOW/g, replacement: 'ParticleAttachment_t.PATTACH_OVERHEAD_FOLLOW' },
    
    // 修复 MODIFIER_PROPERTY 问题
    { pattern: /MODIFIER_PROPERTY\.MODIFIER_PROPERTY_MIN_HEALTH/g, replacement: '32' },
    { pattern: /MODIFIER_PROPERTY\.MODIFIER_PROPERTY_HEALTH_REGEN_CONSTANT/g, replacement: '33' },
    { pattern: /MODIFIER_PROPERTY\.MODIFIER_PROPERTY_INCOMING_PHYSICAL_DAMAGE_CONSTANT/g, replacement: '34' },
    
    // 修复 DOTA_UNIT_TARGET 问题
    { pattern: /DOTA_UNIT_TARGET\.1ES_AND_CREEPS/g, replacement: '3' },
    
    // 修复 MODIFIER_EVENT 问题
    { pattern: /MODIFIER_EVENT\.MODIFIER_EVENT_ON_DEATH/g, replacement: '35' },
    
    // 修复 MODIFIER_PRIORITY 问题
    { pattern: /MODIFIER_PRIORITY\.MODIFIER_PRIORITY_HIGH/g, replacement: '200' },
    { pattern: /MODIFIER_PRIORITY\.MODIFIER_PRIORITY_NORMAL/g, replacement: '100' },
    
    // 修复 number 类型问题 - 使用正确的常量值
    { pattern: /number\.DOTA_TEAM_GOODGUYS/g, replacement: '2' },
    { pattern: /number\.DOTA_TEAM_BADGUYS/g, replacement: '3' },
    
    // 修复 EntityUtils 问题
    { pattern: /new EntityUtils\(\)/g, replacement: 'undefined as any' },
    { pattern: /GameRules\.EntityUtils = new EntityUtils\(\)/g, replacement: '// GameRules.EntityUtils = new EntityUtils()' },
    
    // 修复参数类型问题
    { pattern: /HandleCustomTransmitterData \(data\)/g, replacement: 'HandleCustomTransmitterData (data: any)' },
    { pattern: /\(stateValue\) =>/g, replacement: '(stateValue: any) =>' },
    
    // 修复其他常量问题
    { pattern: /ANIMATE_TIME/g, replacement: '4' },
    { pattern: /USE_IN_OUT_EASE/g, replacement: '5' },
    { pattern: /S_AND_CREEPS/g, replacement: '3' },
];

// 递归遍历目录
function walkDir(dir, callback) {
    const files = fs.readdirSync(dir);
    files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        if (stat.isDirectory()) {
            walkDir(filePath, callback);
        } else if (stat.isFile() && (file.endsWith('.ts') || file.endsWith('.tsx'))) {
            callback(filePath);
        }
    });
}

// 修复文件
function fixFile(filePath) {
    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;
    
    fixes.forEach(fix => {
        if (fix.pattern.test(content)) {
            content = content.replace(fix.pattern, fix.replacement);
            changed = true;
        }
    });
    
    if (changed) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`Fixed remaining 86 errors in: ${filePath}`);
    }
}

// 主函数
function main() {
    const srcDir = path.join(__dirname, 'src');
    console.log('Starting remaining 86 errors fixes...');
    
    walkDir(srcDir, fixFile);
    
    console.log('Remaining 86 errors fixes completed!');
}

main();
