const fs = require('fs');
const path = require('path');

// 定义需要修复的模式和替换
const fixes = [
    // 修复类型注解问题
    { pattern: /execute\(unit: CDOTA_BaseNPC, deltaTime: number\) \{/g, replacement: 'execute(unit: CDOTA_BaseNPC, deltaTime: number): any {' },
    { pattern: /handleMessage\(unit:CDOTA_BaseNPC, message: AIMessage\): void \{/g, replacement: 'handleMessage(unit: CDOTA_BaseNPC, message: AIMessage): void {' },
    
    // 修复索引类型问题
    { pattern: /let (\w+) = \{\}/g, replacement: 'let $1: Record<string, any> = {}' },
    
    // 修复 API 名称问题
    { pattern: /CustomNetTables\.SetTableValue/g, replacement: 'CustomNetTables.SetTableValue' },
    
    // 修复常量问题
    { pattern: /FindOrder\.CLOSEST/g, replacement: 'FIND_CLOSEST' },
    { pattern: /ConVarFlags\.NONE/g, replacement: 'FCVAR_NONE' },
    
    // 修复游戏状态常量
    { pattern: /GameState\.PRE_GAME/g, replacement: 'DOTA_GAMERULES_STATE_PRE_GAME' },
    { pattern: /GameState\.HERO_SELECTION/g, replacement: 'DOTA_GAMERULES_STATE_HERO_SELECTION' },
    { pattern: /GameState\.CUSTOM_GAME_SETUP/g, replacement: 'DOTA_GAMERULES_STATE_CUSTOM_GAME_SETUP' },
    
    // 修复修饰符状态
    { pattern: /ModifierState\.STUNNED/g, replacement: 'MODIFIER_STATE_STUNNED' },
    { pattern: /ModifierState\.PASSIVES_DISABLED/g, replacement: 'MODIFIER_STATE_PASSIVES_DISABLED' },
    
    // 修复粒子附着类型
    { pattern: /ParticleAttachment\.WORLDORIGIN/g, replacement: 'ParticleAttachment_t.PATTACH_WORLDORIGIN' },
    { pattern: /ParticleAttachment\.OVERHEAD_FOLLOW/g, replacement: 'ParticleAttachment_t.PATTACH_OVERHEAD_FOLLOW' },
    { pattern: /ParticleAttachment\.ABSORIGIN/g, replacement: 'ParticleAttachment_t.PATTACH_ABSORIGIN' },
    { pattern: /ParticleAttachment\.POINT_FOLLOW/g, replacement: 'ParticleAttachment_t.PATTACH_POINT_FOLLOW' },
    
    // 修复单位目标类型
    { pattern: /UnitTargetTeam\.ENEMY/g, replacement: 'DOTA_UNIT_TARGET_TEAM_ENEMY' },
    { pattern: /UnitTargetType\.HERO/g, replacement: 'DOTA_UNIT_TARGET_HERO' },
    { pattern: /UnitTargetFlags\.NONE/g, replacement: 'DOTA_UNIT_TARGET_FLAG_NONE' },
    
    // 修复单位攻击能力
    { pattern: /UnitAttackCapability\.NO_ATTACK/g, replacement: 'DOTA_UNIT_CAP_NO_ATTACK' },
    
    // 修复游戏活动
    { pattern: /GameActivity\.DOTA_SPAWN/g, replacement: 'ACT_DOTA_SPAWN' },
    
    // 修复修饰符运动类型
    { pattern: /LuaModifierMotionType\.NONE/g, replacement: 'LUA_MODIFIER_MOTION_NONE' },
    { pattern: /LuaModifierMotionType\.BOTH/g, replacement: 'LUA_MODIFIER_MOTION_BOTH' },
    { pattern: /LuaModifierMotionType\.HORIZONTAL/g, replacement: 'LUA_MODIFIER_MOTION_HORIZONTAL' },
    { pattern: /LuaModifierMotionType\.VERTICAL/g, replacement: 'LUA_MODIFIER_MOTION_VERTICAL' },
    
    // 修复金币修改原因
    { pattern: /ModifyGoldReason\.BUILDING/g, replacement: 'DOTA_ModifyGold_Building' },
];

// 递归遍历目录
function walkDir(dir, callback) {
    const files = fs.readdirSync(dir);
    files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        if (stat.isDirectory()) {
            walkDir(filePath, callback);
        } else if (stat.isFile() && (file.endsWith('.ts') || file.endsWith('.tsx'))) {
            callback(filePath);
        }
    });
}

// 修复文件
function fixFile(filePath) {
    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;
    
    fixes.forEach(fix => {
        if (fix.pattern.test(content)) {
            content = content.replace(fix.pattern, fix.replacement);
            changed = true;
        }
    });
    
    if (changed) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`Fixed additional errors in: ${filePath}`);
    }
}

// 主函数
function main() {
    const srcDir = path.join(__dirname, 'src');
    console.log('Starting additional error fixes...');
    
    walkDir(srcDir, fixFile);
    
    console.log('Additional error fixes completed!');
}

main();
