import { KVUtils } from "../utils/kvutils";


enum BotOffensiveStrategyEnum {
    Attack = 1,
    Defance = 2,
}
interface BattleSituation {
    mana: number;
    enemyMana:number;
    waitManaFlag:Boolean;
    OffensiveLorR:boolean,
    OffensiveStrategy:BotOffensiveStrategyEnum,
    nowChooseCardStrIndex:{
        strIndex:number,
        costSoon:number,
        usingSoonCards:number[],
        Tars:number[],
    },

}
interface BotCardInfo {
    card_id:number,
    card_index:number,
    chName:string,
    score:number,
    cost:number,
    botCardAI:BotCardAI,
}
/**
 * 一个AIController控制一个机器人
 预加载策略：
根据当前出战卡组，加载所有卡牌组合，并给卡组评分
	-评分越高代表这张牌不能轻易出，评分越低越是过渡牌。
	-基础评分等于卡牌的费用
	-根据包含组合的优先级额外加分。例如一个卡牌组合优先级为2，则给组合包含的牌分别加2分
局内思考逻辑：
1、判断当前局面状态，决定当前总策略应该为：进攻左路，进攻右路，防守左路，防守右路
2、根据总策略决定当前行为，用牌或攒费
	-校验所有已提前加载的组合，结合当前局势，手牌是否包含组合，费用是否足够，选择最优策略
	-当策略包含的卡牌已经出过时，会优先打完完整组合后再重新思考
	-如果当前牌没完全上手，需要用掉一些非关键牌，根据卡牌评分用掉低分牌
 */
export class AIController {
    /**
     * 当前卡组，有顺序
     */
    private deckCards:BotCardInfo[] = [];
    private cardInfo:{
        [keys:number]:{
            id:number
            info:CardInfoKV,
        }
    } = {};
    private BotStrategys:CardComboStrategy[] = [];
    private battleSituation:BattleSituation = {
        mana: 0,
        enemyMana:0,
        waitManaFlag:true,
        OffensiveLorR:true,
        OffensiveStrategy:BotOffensiveStrategyEnum.Attack,
        nowChooseCardStrIndex:undefined,
    };
    public playerID:PlayerID;
    public isGood:boolean;
    public team:number;
    public enemyP:PlayerID

    constructor(playerID:PlayerID, enemyP:PlayerID) {
        this.playerID = playerID
        this.enemyP = enemyP
        this.team = PlayerResource.GetPlayer(playerID).GetTeam()
        this.isGood = (!GameRules.FastWarPhaseController.GameTeamGroup[this.team].enemyIsGoodGuy)
    }

    public initAI () {
        //加载出战卡牌
        this.deckCards = []
        GameRules.FastWarCard.getNowGameHandCardsForPlayer(this.playerID).forEach((v,index)=>{
            let cardInfo = GameRules.KVUtils.getCardsInfo(v.card_index)
            this.cardInfo[v.card_index] = {
                id:v.card_id,
                info:cardInfo,
            }
            this.deckCards.push({
                card_id:v.card_id,
                card_index:v.card_index,
                chName:cardInfo.chName,
                score:cardInfo.layCost,
                cost:cardInfo.layCost,
                botCardAI:cardInfo.BotCardAI,
            })
        })
        // DeepPrintTable(this.deckCards)
        //根据出战卡牌加载策略
        let botStr = GameRules.KVUtils.getBotCardsStr(this.deckCards.map((v)=>v.card_index))
        this.BotStrategys = botStr.strRes
        this.deckCards.forEach((v)=>{
            if (botStr.cardRes[v.card_index] != undefined) {
                v.score += botStr.cardRes[v.card_index]
            }
        })
        // DeepPrintTable(this.BotStrategys)
        this.startThink() 
    }
    public DestroyThink () {
        GameRules.Debug.DebugPrint("关闭AI思考！！！！！！！！！！！！")
        Timers.RemoveTimer(this.timerId)
    }
    private timerId:string ;
    public startThink () {
        let AIC = this
        GameRules.Debug.DebugPrint("开启AI思考，需要注意以下描述中的左右都为相对方向，与所在队伍有关")
        GameRules.Debug.DebugPrint("威胁程度计算公式：单位费用*单位所在位置深度比例*单位当前生命值比例，绝对值越大威胁越大，负数代表己方受到威胁，正数代表对敌有威胁")
        GameRules.Debug.DebugPrint("我方为："+this.isGood?"夜宴方！注意方向相反！":"天辉方！")
        this.timerId = Timers.CreateTimer(0.5,()=>{
            return AIC.think()
        })
    }
    // 核心思考逻辑
    private dealBattleSituationLongInterval:number = 2
    private dealBattleSituationShortInterval:number = 0.3
    private think(): number {
        if (GameRules.DebugOpen.debugMana) {
            return 0.1
        }
        let m = (CustomNetTables.GetTableValue("card_mana","card_mana"));
        this.battleSituation.mana = m[this.playerID.toString()].mana
        this.battleSituation.enemyMana = m[this.enemyP.toString()].mana
        if (this.battleSituation.nowChooseCardStrIndex != undefined && this.battleSituation.nowChooseCardStrIndex.usingSoonCards != undefined
            && this.battleSituation.nowChooseCardStrIndex.usingSoonCards.length > 0) {
            if (this.battleSituation.mana >= this.battleSituation.nowChooseCardStrIndex.costSoon) {
                let choose = this.battleSituation.nowChooseCardStrIndex.usingSoonCards[0]
                let chooseInfo = this.cardInfo[choose]
                GameRules.Debug.DebugPrint("*************************************************************************")
                GameRules.Debug.DebugPrint("费用够了，使用："+chooseInfo.info.chName)
                this.battleSituation.nowChooseCardStrIndex.usingSoonCards = [...this.battleSituation.nowChooseCardStrIndex.usingSoonCards.filter((v,index)=>index>0)]
                if (this.battleSituation.nowChooseCardStrIndex.usingSoonCards.length > 0) {
                    let nextChoose = this.cardInfo[this.battleSituation.nowChooseCardStrIndex.usingSoonCards[0]]
                    this.battleSituation.nowChooseCardStrIndex.costSoon = nextChoose.info.layCost
                    GameRules.Debug.DebugPrint("下张牌将在费用到达"+nextChoose.info.layCost+"时使用："+nextChoose.info.chName)
                } else {
                    GameRules.Debug.DebugPrint("当前组合使用完毕，重新思考应对方式")
                }
                let attackStr = (this.battleSituation.OffensiveStrategy == BotOffensiveStrategyEnum.Attack)
                let towerDes:boolean = GameRules.FastWarPhaseController.GameTeamGroup[this.team].enemyTower[this.battleSituation.OffensiveLorR?0:1] == 0
                let ps = GameRules.EntityUtils.getAICardPos(this.battleSituation.nowChooseCardStrIndex.Tars, towerDes, this.battleSituation.OffensiveLorR, this.isGood, attackStr)
                let p = ps[RandomInt(0,ps.length-1)]
                if (chooseInfo.info.BotCardAI.CardTag == 1) {
                    p = p.__add(Vector(RandomInt(-100,100), this.isGood?RandomInt(25,125):RandomInt(-125,-25),0))
                } else if (chooseInfo.info.BotCardAI.CardTag == 2) {
                    p = p.__add(Vector(RandomInt(-100,100), this.isGood?RandomInt(-125,25):RandomInt(25,125),0))
                }
                let newC = GameRules.FastWarCard.useCard(this.playerID, chooseInfo.id, GetGroundPosition(Vector(p.x,p.y,p.z), undefined),true, false)
                if (newC == undefined) {
                    print("卡牌错序，重新开启思考")
                    this.DestroyThink()
                    this.initAI()
                } else {
                    this.cardInfo[choose].id = newC.card_id;
                    let newCForBot = (this.deckCards.filter((v,index)=>v.card_index==chooseInfo.info.Index))[0]
                    // GameRules.Debug.DebugPrint("使用与修改id的卡牌，新id："+newC.card_id)
                    // newCForBot.card_id = newC.card_id
                    // DeepPrintTable(newCForBot)
                    this.deckCards = [...this.deckCards.filter((v,index)=>v.card_index!=chooseInfo.info.Index),newCForBot]
                    // GameRules.Debug.DebugPrint("其余卡牌")
                    // DeepPrintTable(this.deckCards.filter((v,index)=>v.card_index!=chooseInfo.info.Index))
                    // GameRules.Debug.DebugPrint("当前卡牌信息")
                    // DeepPrintTable(this.deckCards)
                }
            }
            return this.dealBattleSituationShortInterval
        } else {
            GameRules.Debug.DebugPrint("*************************************************************************")
            this.dealBattleSituation()
            return this.decideCardStr()
        }
    }

    private decideCardStr (): number {
        //遍历卡牌组合
        const nowhands = [...this.deckCards.filter((s,index)=>index<4).sort((a,b)=>b.score - a.score)]
        nowhands.forEach((v)=>{
            GameRules.Debug.DebugPrint("当前手牌为："+v.chName+"，评分："+v.score)
        })
        this.deckCards.filter((s,index)=>index>=4).forEach((v)=>{
            GameRules.Debug.DebugPrint("--卡组剩余卡牌："+v.chName+"，评分："+v.score)
        })
        // 目前可使用的组合
        let cards = nowhands.map((i)=>parseInt(i.card_index.toString()))
        const candidateStrategies = this.BotStrategys
            .filter((v)=>this.getMatchingCardsForStrategy(cards, v))
            .sort((a, b) => {
                if (b.AttackLevel == a.AttackLevel) {
                    return b.Priority - a.Priority
                } else {
                    if (this.battleSituation.OffensiveStrategy == BotOffensiveStrategyEnum.Attack) {
                        return b.AttackLevel - a.AttackLevel
                    } else {
                        return a.AttackLevel - b.AttackLevel
                    }
                }
            });
        if (candidateStrategies == undefined || candidateStrategies.length <= 0) {
            if (this.battleSituation.waitManaFlag && this.battleSituation.mana < 9) {
                GameRules.Debug.DebugPrint("目前手牌不包含组合，局势不紧张且费用不满，将选择攒费，等待2秒")
                return this.dealBattleSituationLongInterval
            } else {
                // 优先使用策略中的低费卡牌
                let cs = nowhands.filter((v)=>v.cost <= this.battleSituation.mana)
                if (cs == undefined || cs.length <= 0) {
                    GameRules.Debug.DebugPrint("目前手牌不包含组合，情况紧急但不够费打出任何牌，攒费，等待2秒")
                    return this.dealBattleSituationLongInterval
                } else {
                    let choose = cs[RandomInt(0,cs.length-1)]
                    GameRules.Debug.DebugPrint("目前手牌不包含组合，但又必须出牌，随机出牌："+choose.chName)
                    this.battleSituation.nowChooseCardStrIndex = {
                        strIndex: -1,
                        costSoon:Math.min(choose.cost,10),
                        usingSoonCards: [choose.card_index],
                        Tars:(this.battleSituation.OffensiveStrategy == BotOffensiveStrategyEnum.Attack)?choose.botCardAI.TarAttack:choose.botCardAI.TarDefense,
                    }
                    return this.dealBattleSituationShortInterval
                }
            }
        } else {
            candidateStrategies.forEach((v)=>{
                GameRules.Debug.DebugPrint("目前手牌包含组合："+v.RelatedCards.map((s)=>this.cardInfo[s as any].info.chName).join(";")+"，进攻性为："+v.AttackLevel+"，优先级为："+v.Priority)
            })
            let choose = candidateStrategies[0]
            this.battleSituation.nowChooseCardStrIndex = {
                strIndex: choose.Index,
                costSoon:Math.min(choose.minCost,10),
                usingSoonCards: [...choose.RelatedCards],
                Tars:(this.battleSituation.OffensiveStrategy == BotOffensiveStrategyEnum.Attack)?choose.TarAttack:choose.TarDefense,
            }
            GameRules.Debug.DebugPrint("选择组合："+choose.RelatedCards.map((v)=>this.cardInfo[v as any].info.chName).join(";")+"，将在费用到达"+choose.minCost+"时使用："+this.cardInfo[choose.RelatedCards[0]].info.chName)
            return this.dealBattleSituationShortInterval
        }
    }

    private CounthalfUnitSit (halfIsGood:boolean, unitIsGood:boolean, lOrR:boolean, num:number[][][]) {
        let atGood = halfIsGood?0:1
        // let enemyGood = !halfIsGood?0:1
        let selfUnitGood = unitIsGood?0:1
        let enemyUnitGood = !unitIsGood?0:1
        let selfLOrR = lOrR?0:1
        let enemyLOrR = !lOrR?0:1
        return num[atGood][selfUnitGood][selfLOrR] - num[atGood][enemyUnitGood][enemyLOrR]
    }

    private CounthalfUnitHealthAvg (halfIsGood:boolean, unitIsGood:boolean, lOrR:boolean, unitNum:number[][][], data:number[][][]) {
        let atGood = halfIsGood?0:1
        let selfUnitGood = unitIsGood?0:1
        let selfLOrR = lOrR?0:1
        return data[atGood][selfUnitGood][selfLOrR] / unitNum[atGood][selfUnitGood][selfLOrR]
    }

    private dealBattleSituation () {
        //获取当前局面状态
        
        let a = GameRules.BotGameSituationInfo.halfUnitNum
        let b = GameRules.BotGameSituationInfo.halfUnitCost
        let c = GameRules.BotGameSituationInfo.towerHealth
        let d = GameRules.BotGameSituationInfo.halfUnitThreatLevel
        let e = GameRules.BotGameSituationInfo.halfUnitHealth
        GameRules.Debug.DebugPrint("在goodGuy半场有good："+a[0][0][0]+","+a[0][0][1]+"个，总费用"+b[0][0][0]+","+b[0][0][1]+"，bad："+a[0][1][0]+","+a[0][1][1]+"个，总费用"+b[0][1][0]+","+b[0][1][1]+"。")
        GameRules.Debug.DebugPrint("good防御塔：左"+c[0][0]+"血，右"+c[0][1]+"血，基地："+c[0][2]+"血。")
        GameRules.Debug.DebugPrint("在badGuy半场有good："+a[1][0][0]+","+a[1][0][1]+"个，总费用"+b[1][0][0]+","+b[1][0][1]+"，bad："+a[1][1][0]+","+a[1][1][1]+"个，总费用"+b[1][1][0]+","+b[1][1][1]+"。")
        GameRules.Debug.DebugPrint("bad防御塔：左"+c[1][0]+"血，右"+c[1][1]+"血，基地："+c[1][2]+"血。")
        let LorR = true
        let LSC = this.CounthalfUnitSit(this.isGood, this.isGood,LorR,b)//左路在己方半场赚几费
        let LEC = this.CounthalfUnitSit(!this.isGood, this.isGood,LorR,b)//左路在敌方半场赚几费
        let absLC = LSC + LEC//左路赚几费
        let RSC = this.CounthalfUnitSit(this.isGood, this.isGood,!LorR,b)//右路在己方半场赚几费
        let REC = this.CounthalfUnitSit(!this.isGood, this.isGood,!LorR,b)//右路在敌方半场赚几费
        let absRC = RSC + REC//右路赚几费
        let RST = c[this.isGood?0:1][!LorR?0:1]//右路己方塔几血
        let RET = c[!this.isGood?0:1][LorR?0:1]//右路敌方塔几血
        let absRT = RST - RET//右路塔赚几血
        let LST = c[this.isGood?0:1][LorR?0:1]//左路己方塔几血
        let LET = c[!this.isGood?0:1][!LorR?0:1]//左路敌方塔几血
        let absLT = LST - LET//左路塔赚几血
        let allC = absLC+absRC+this.battleSituation.mana-this.battleSituation.enemyMana
        GameRules.Debug.DebugPrint("当前我方左路，己方半场赚"+LSC+"费，敌方半场赚"+LEC+"费，共"+absLC+"费，防御塔己方"+LST+"血，敌方"+LET+"血")
        GameRules.Debug.DebugPrint("当前我方右路，己方半场赚"+RSC+"费，敌方半场赚"+REC+"费，共"+absRC+"费，防御塔己方"+RST+"血，敌方"+RET+"血")
        GameRules.Debug.DebugPrint("当前我方费用"+this.battleSituation.mana+"，敌方费用"+this.battleSituation.enemyMana+"，目前局面为赚"+(allC))
        let LSTL = this.CounthalfUnitSit(this.isGood, this.isGood,LorR,d)//左路己方受威胁程度
        let LETL = this.CounthalfUnitSit(!this.isGood, this.isGood,LorR,d)//左路对敌方威胁程度
        let RSTL = this.CounthalfUnitSit(this.isGood, this.isGood,!LorR,d)//右路己方受威胁程度
        let RETL = this.CounthalfUnitSit(!this.isGood, this.isGood,!LorR,d)//右路对敌方威胁程度
        GameRules.Debug.DebugPrint("左路己方半场威胁程度为"+LSTL+"，敌方半场威胁程度为"+LETL)
        GameRules.Debug.DebugPrint("右路己方半场威胁程度为"+RSTL+"，敌方半场威胁程度为"+RETL)
        let LSSH = this.CounthalfUnitHealthAvg(this.isGood, this.isGood,LorR,a,e)//左路己方半场友方平均生命值比例
        let LSEH = this.CounthalfUnitHealthAvg(this.isGood, !this.isGood,LorR,a,e)//左路己方半场敌方平均生命值比例
        let RSSH = this.CounthalfUnitHealthAvg(this.isGood, this.isGood,!LorR,a,e)//右路己方半场友方平均生命值比例
        let RSEH = this.CounthalfUnitHealthAvg(this.isGood, !this.isGood,!LorR,a,e)//右路己方半场敌方平均生命值比例
        if (LSTL < 0 || RSTL < 0) {
            if (Math.abs(LSTL) > LSC || LST < 0.2) {
                GameRules.Debug.DebugPrint("威胁值大于费用差或防御塔血量较低，攒费优先级降低")
                GameRules.Debug.DebugPrint("己方半场受威胁，左路防守点位用牌")
                this.battleSituation.OffensiveLorR= true
                this.battleSituation.OffensiveStrategy = BotOffensiveStrategyEnum.Defance
                this.battleSituation.waitManaFlag = false
            } else if (Math.abs(RSTL) > RSC || RST < 0.2) {
                GameRules.Debug.DebugPrint("威胁值大于费用差或防御塔血量较低，攒费优先级降低")
                GameRules.Debug.DebugPrint("己方半场受威胁，右路防守点位用牌")
                this.battleSituation.OffensiveLorR= false
                this.battleSituation.OffensiveStrategy = BotOffensiveStrategyEnum.Defance
                this.battleSituation.waitManaFlag = false
            } else {
                this.battleSituation.waitManaFlag = true
                if (LSTL < RSTL) {
                    GameRules.Debug.DebugPrint("己方半场受威胁，左路防守点位用牌")
                    this.battleSituation.OffensiveLorR= true
                    this.battleSituation.OffensiveStrategy = BotOffensiveStrategyEnum.Defance
                } else {
                    GameRules.Debug.DebugPrint("己方半场受威胁，右路防守点位用牌")
                    this.battleSituation.OffensiveLorR= false
                    this.battleSituation.OffensiveStrategy = BotOffensiveStrategyEnum.Defance
                }
            }
        } else {
            this.battleSituation.waitManaFlag = true
            if (LET < 0.2 || RET < 0.2) {
                if (LET <= RET) {
                    GameRules.Debug.DebugPrint("己方半场无威胁，且敌方左路塔血量较低，选择左路进攻")
                    this.battleSituation.OffensiveLorR= true
                    this.battleSituation.OffensiveStrategy = BotOffensiveStrategyEnum.Attack
                } else if (LET > RET) {
                    GameRules.Debug.DebugPrint("己方半场无威胁，且敌方右路塔血量较低，选择右路进攻")
                    this.battleSituation.OffensiveLorR= false
                    this.battleSituation.OffensiveStrategy = BotOffensiveStrategyEnum.Attack
                }
            } else {
                if (Math.abs(LETL) < Math.abs(RETL)) {
                    GameRules.Debug.DebugPrint("己方半场无威胁，预防进攻，左路进攻点位用牌")
                    this.battleSituation.OffensiveLorR= true
                    this.battleSituation.OffensiveStrategy = BotOffensiveStrategyEnum.Attack
                } else if (Math.abs(LETL) < Math.abs(RETL)) {    
                    GameRules.Debug.DebugPrint("己方半场无威胁，预防进攻，右路进攻点位用牌")
                    this.battleSituation.OffensiveLorR= false
                    this.battleSituation.OffensiveStrategy = BotOffensiveStrategyEnum.Attack
                } else {
                    if (Math.abs(LSTL) >= Math.abs(RSTL)) {
                        GameRules.Debug.DebugPrint("己方半场无威胁，巩固优势，左路进攻点位用牌")
                        this.battleSituation.OffensiveLorR= true
                        this.battleSituation.OffensiveStrategy = BotOffensiveStrategyEnum.Attack
                    } else {    
                        GameRules.Debug.DebugPrint("己方半场无威胁，巩固优势，右路进攻点位用牌")
                        this.battleSituation.OffensiveLorR= false
                        this.battleSituation.OffensiveStrategy = BotOffensiveStrategyEnum.Attack
                    }
                }
            } 
        }
    }

    //辅助方法
    private getMatchingCardsForStrategy(cards: number[], strategy: CardComboStrategy): boolean {
        let res = true
        strategy.RelatedCards.forEach((v,index)=>{
            if (cards.indexOf(v) == -1) {
                res = false
            }
        })
        return res
    }
}

