import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";


export class modifier_fw_zombie_extra_debuff extends BaseModifier {
    
    OnCreated(keys:any): void {
        if (IsServer()) {
            // print("小僵尸传播瘟疫！")
        }
    } 

    IsHidden() {
        return false;
    }
    
    CheckState() {
        return {
            // [6]: true,
        }   
    }

    RemoveOnDeath(): boolean {
        return true
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            // 1,
            // 2,
            ];
    }
    
}
