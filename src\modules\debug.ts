import { reloadable } from '../utils/tstl-utils';
import { NPCUtils } from './npcutils';

export class Debug {
    // 在线测试白名单
    OnlineDebugWhiteList = [
        360739143, 
        135962932, 
    ];

    constructor() {
        // 工具模式下开启调试
        GameRules.DebugOpen = {
            debugPrint:false,
            debugMana:false,
            debugDamage:0
        }
        ListenToGameEvent(`player_chat`, keys => this.OnPlayerChat(keys), this);
    }

    DebugPrint (str:string) : void { 
        if (GameRules.DebugOpen.debugPrint) {
            print(str)
        }
    }

    OnPlayerChat(keys: GameEventProvidedProperties & PlayerChatEvent): void {
        const strs = keys.text.split(' ');
        const cmd = strs[0];
        const args = strs.slice(1);
        const steamid = PlayerResource.GetSteamAccountID(keys.playerid);

        if (cmd === 'fwNoCost') {
            if (GameRules.IsCheatMode() || this.OnlineDebugWhiteList.includes(steamid)) {
                GameRules.DebugOpen.debugMana = !GameRules.DebugOpen.debugMana
                Say(HeroList.GetHero(0), "FastWarDebug用牌无消耗功能:"+(GameRules.DebugOpen.debugMana?"开启":"关闭"), false);
            } else {
                Say(HeroList.GetHero(0), "当前未开启作弊模式，无法开启用牌无消耗，当然你也可以向作者申请白名单来在任意情况下开启~", false);
            }
        }
        if (cmd === 'fwShowBotThink') {
            if (this.OnlineDebugWhiteList.includes(steamid)) {
                GameRules.DebugOpen.debugPrint = !GameRules.DebugOpen.debugPrint
            }
            Say(HeroList.GetHero(0), "FastWarDebug展示打印:"+(GameRules.DebugOpen.debugPrint?"开启":"关闭"), false);
        }
        
        // 只在允许调试的时候才执行以下指令
        // commands that only work in debug mode below:
        if (!GameRules.DebugOpen.debugPrint) return;

        if (cmd === 'test1') {
            
        }
        if (cmd === 'testFow') {
            // print(2222222222)
            AddFOWViewer(2, Vector(0,0,0), 2500, 9999, true)
        }
        // 其他的测试指令写在下面
        if (cmd === 'get_key_v3') {
            const version = args[0];
            const key = GetDedicatedServerKeyV3(version);
            Say(HeroList.GetHero(0), `${version}: ${key}`, true);
        }
        if (cmd === 'get_key_v2') {
            const version = args[0];
            const key = GetDedicatedServerKeyV2(version);
            Say(HeroList.GetHero(0), `${version}: ${key}`, true);
        }
    }
}
