import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";
import { modifier_fw_zombie_extra_debuff } from "./modifier_fw_zombie_extra_debuff";


export class modifier_fw_weaver_geminate_attack extends BaseModifier {
    
    IsHidden() {
        return true;
    }

    OnCreated(params: object): void {
        if (IsClient()) {
            this.StartIntervalThink(0.1)
        }
    }
    
    OnAttack (event: ModifierAttackEvent): void {
        if (event.attacker.entindex() == this.GetParent().entindex()) {
            let ab = this.GetAbility()
            if (ab.IsCooldownReady()) {
                this.GetAbility().StartCooldown(6)
                this.GetParent().PerformAttack (event.target, true, true,true, false, true, false, false)
            }
        }
    }

    CheckState() {
        return {
            // [6]: true,
        }   
    }

    RemoveOnDeath(): boolean {
        return true
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            22,
            // 2,
        ];
    }
    
}
