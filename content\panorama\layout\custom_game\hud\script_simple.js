// Simple Panorama Script - No imports/requires
console.log('Simple script loaded');

// 基本的事件注册系统
var registFWEvents = {};
var FWEventKeys = {};
var index = 0;

// 注册自定义事件
GameUI.registFWEvent = function (eventName, callback) {
    if (registFWEvents[eventName] === undefined) {
        registFWEvents[eventName] = {};
    }
    var key = 'event_' + index++;
    registFWEvents[eventName][key] = callback;
    FWEventKeys[key] = eventName;
    return key;
};

// 触发自定义事件
GameUI.fireFWEvent = function (eventName, data) {
    if (registFWEvents[eventName] !== undefined) {
        for (var key in registFWEvents[eventName]) {
            if (registFWEvents[eventName].hasOwnProperty(key)) {
                try {
                    registFWEvents[eventName][key](data);
                } catch (e) {
                    console.error('Error in event handler:', e);
                }
            }
        }
    }
};

// 取消注册事件
GameUI.unregistFWEvent = function (key) {
    var eventName = FWEventKeys[key];
    if (eventName && registFWEvents[eventName] && registFWEvents[eventName][key]) {
        delete registFWEvents[eventName][key];
        delete FWEventKeys[key];
    }
};

console.log('Simple script initialized');
