

export class FastWarSpell {

    public timers:string[] = []

    public clearTimer () {
        for (const t of this.timers) {
            Timers.RemoveTimer(t)
        }
        this.timers = []
    }

    public startIntervalSpell (delay:number,duration:number, interval:number, funStart:()=>void, funMain:()=>void,funEnd:()=>void) {
        let i = Math.round((duration+interval/2)/interval) - 1
        let flag = true
        // print("将作用："+i+"次")
        let res = Timers.CreateTimer(delay,()=>{
            if (flag) {
                if (funStart != undefined) {
                    funStart()
                }
                flag = false
                return interval
            } else {
                if (i > 0) {
                    if (funMain != undefined) {
                        // print("执行！"+i)
                        funMain()
                    }
                } else if (i <= 0) {
                    if (funEnd != undefined) {
                        funEnd()
                    }
                    return
                }
                i--
                return interval
            }
        })
        this.timers.push(res)
        return res
    }

}
