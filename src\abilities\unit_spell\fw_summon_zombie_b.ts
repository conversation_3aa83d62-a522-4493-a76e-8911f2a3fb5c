import { modifier_fw_summon_zombie } from "../../modifiers/abilities/unit_spell/modifier_fw_summon_zombie";
import { BaseAbility, registerAbility } from "../../utils/dota_ts_adapter";
import { MathUtils } from "../../utils/math_utils";

export class fw_summon_zombie_b extends BaseAbility {
    

    spell:SpellInfoKV;
    OnSpellStart(): void {
        let unit = this.GetCaster()
        let playerID = unit.GetPlayerOwnerID()
        let team = unit.GetTeam()
        let pos = unit.GetAbsOrigin()
        let num = this.GetSpecialValueFor("num")
        let creepTempName = "creep_default_undying_zombie"
        let tempInfo = GameRules.KVUtils.getUnitTemplateInfoTreated(creepTempName)
        let unitName = GameRules.PlayerData.getPlayerSpecialCardInfo(playerID, creepTempName, team)
        // print("召唤："+unitName)
        GameRules.NPCUtils.createUnitForSpell(playerID,unit,team,unit.cardId,1,tempInfo,num,unitName,pos,0, unit.cardUsesNum)
    }

    Spawn(): void {
        this.spell = GameRules.KVUtils.getSpellInfo("fw_summon_zombie")
        if (this.spell.UnitTemplate.length > 0 ) {
            if (IsServer()) {
                let unit = this.GetCaster()
                let team = unit.GetTeam()
                let playerID = unit.GetPlayerOwnerID()
                for (const temp of this.spell.UnitTemplate) {
                    let unitName = GameRules.PlayerData.getPlayerSpecialCardInfo(playerID, temp, team)
                    GameRules.preCacheInfos.push(unitName)
                }
                PrecacheItemByNameAsync("item_for_precache", ()=>{
                    // print("预载单位完成")
                })
            }
        }
    }
    
    Precache(context: CScriptPrecacheContext): void {
        PrecacheResource("particle","",context)
    }
}

