const fs = require('fs');
const path = require('path');

// 修复装饰器问题 - 完全移除装饰器
const fixes = [
    // 移除 registerAbility 装饰器
    { pattern: /@registerAbility\(\)\s*\n/g, replacement: '// @registerAbility()\n' },
    
    // 移除 registerModifier 装饰器
    { pattern: /@registerModifier\(\)\s*\n/g, replacement: '// @registerModifier()\n' },
    
    // 移除 reloadable 装饰器
    { pattern: /@reloadable\s*\n/g, replacement: '// @reloadable\n' },
];

// 递归遍历目录
function walkDir(dir, callback) {
    const files = fs.readdirSync(dir);
    files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        if (stat.isDirectory()) {
            walkDir(filePath, callback);
        } else if (stat.isFile() && (file.endsWith('.ts') || file.endsWith('.tsx'))) {
            callback(filePath);
        }
    });
}

// 修复文件
function fixFile(filePath) {
    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;
    
    fixes.forEach(fix => {
        if (fix.pattern.test(content)) {
            content = content.replace(fix.pattern, fix.replacement);
            changed = true;
        }
    });
    
    if (changed) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`Fixed final decorators in: ${filePath}`);
    }
}

// 主函数
function main() {
    const srcDir = path.join(__dirname, 'src');
    console.log('Starting final decorator fixes...');
    
    walkDir(srcDir, fixFile);
    
    console.log('Final decorator fixes completed!');
}

main();
