import { KVUtils } from "../utils/kvutils"
import { MathUtils } from "../utils/math_utils"



export class FastWarCard {
    private unqCardId = 0 
    /**
     * 绑定在玩家主英雄上的技能记录
     */
    private PlayerHeroSpell:{
        [key:string]:{
            [key:string]:number
        }
    } = {}
    /**
     * 玩家当前卡组配置
     */
    private PlayerDeckCards:{
        [key:string]:GoFastWarCardClient[]
    } = {}
    /**
     * 玩家当前库存配置
     */
    private PlayerReserveCards:{
        [key:string]:GoFastWarCardClient[]
    } = {}
    /**
     * 玩家当前对局牌组
     */
    private PlayerNowGameHandCards:{
        [key:string]:GoFastWarCardClient[]
    } = {}
    //当前对局涉及的全部卡牌
    private PlayerNowGameAllCards:{
        [key:string]:{
            [key:number]:GoFastWarCardClient
        }
    } = {}

    constructor() {}

    public debugPrintNowDeckCards () {
        Object.keys(this.PlayerDeckCards).forEach((p)=>{
            let dks = this.PlayerDeckCards[p.toString()]
            print("玩家："+p+"当前卡组为：")
            print(dks.map((v)=>v.card_index).join(","))
            print(dks.map((v)=>GameRules.KVUtils.getCardsInfo(v.card_index).chName).join(","))
        })  
    }

    public replaceSpellDeckCards (pid:PlayerID) {
        let spells = this.PlayerDeckCards[pid.toString()].filter((v)=>GameRules.KVUtils.getCardsInfo(v.card_index).cardType == "spell").map((v)=>v.card_id)
        let randomUnits = MathUtils.getRandomElements(
            this.PlayerReserveCards[pid.toString()].filter((v)=>GameRules.KVUtils.getCardsInfo(v.card_index).cardType == "unit").map((v)=>v.card_id) ,spells.length)
        for (const s of spells) {
            this.changeDeckCardToReserveNetTable(pid, s)
        }
        for (const s of randomUnits) {
            this.changeReserveCardToDeckNetTable(pid, s)
        }
    }

    public completionDeck (ps:PlayerID[], change:boolean, filterType:0|1|2) {
        for (const p of ps) {
            let l = 8 - this.PlayerDeckCards[p.toString()].length
            let cs = this.PlayerReserveCards[p.toString()]
                    .filter((v)=>{
                        if (filterType == 1) {
                            return GameRules.KVUtils.getCardsInfo(v.card_index).cardType == "unit"
                        } else if (filterType == 2) {
                            return GameRules.KVUtils.getCardsInfo(v.card_index).cardType == "spell"
                        }
                        return true
                    })
                    .map((v)=>v.card_id)
            if (l > 0) {
                // DeepPrintTable(this.PlayerReserveCards[p.toString()].map((v)=>v.card_id))
                let rs = MathUtils.getRandomElements(cs,l)
                // DeepPrintTable(rs)
                for (const r of rs) {
                     this.changeReserveCardToDeckNetTable(p, r)
                }
            } else if (change) {
                let nowC= this.PlayerDeckCards[p.toString()].map((v)=>v.card_id)
                for (const c of nowC) {
                    this.changeDeckCardToReserveNetTable(p, c)
                }
                let rs = MathUtils.getRandomElements(cs,8)
                // DeepPrintTable(rs)
                for (const r of rs) {
                     this.changeReserveCardToDeckNetTable(p, r)
                }
            }
        }
    }

    public initDeckCardNetTable (playerId:PlayerID) {
        if (playerId == -1) {
            return;
        }
        let p = playerId.toString();
        let deck_cards = CustomNetTables.GetTableValue("deck_cards","deck_cards");
        if (deck_cards == undefined) {
            deck_cards = {changedPlayer:-1,deck:{},reserve:{}};
        }
        let dCards = this.getFixDeckCards(playerId, true)
        this.PlayerDeckCards[p] = [...dCards]
        this.PlayerNowGameAllCards[p] = {}
        let rCards = this.getFixDeckCards(playerId, false)
        this.PlayerReserveCards[p] = [...rCards]
        this.PlayerHeroSpell[p] = {}

        deck_cards.changedPlayer = playerId
        deck_cards.deck[p] = dCards
        deck_cards.reserve[p] = rCards
        CustomNetTables.SetTableValue("deck_cards", "deck_cards", deck_cards);
    }

    public changeDeckCardToReserveNetTable (playerId:PlayerID, cardId:number) {
        if (playerId == -1) {
            return;
        }
        let p = playerId.toString();
        let cards = this.PlayerDeckCards[p].filter((v,index)=>v.card_id == cardId)
        if (cards == undefined || cards.length < 1) {
            return 
        }
        let card = cards[0]
        let dCards = this.PlayerDeckCards[p]
        this.PlayerDeckCards[p] = [...dCards.filter((v,index)=>v.card_id != cardId)]
        let rCards = this.PlayerReserveCards[p]
        this.PlayerReserveCards[p] = [...rCards.filter((v,index)=>v.card_id != cardId),card]

        let deck_cards = CustomNetTables.GetTableValue("deck_cards","deck_cards");
        deck_cards.changedPlayer = playerId
        deck_cards.deck[p] = this.PlayerDeckCards[p]
        deck_cards.reserve[p] = this.PlayerReserveCards[p]
        CustomNetTables.SetTableValue("deck_cards", "deck_cards", deck_cards);
    }

    public changeReserveCardToDeckNetTable (playerId:PlayerID, cardId:number) {
        if (playerId == -1) {
            return;
        }
        let p = playerId.toString();
        let cards = this.PlayerReserveCards[p].filter((v,index)=>v.card_id == cardId)
        if (cards == undefined || cards.length < 1) {
            return 
        }
        let card = cards[0]
        let rCards = this.PlayerReserveCards[p]
        this.PlayerReserveCards[p] = [...rCards.filter((v,index)=>v.card_id != cardId)]
        let dCards = this.PlayerDeckCards[p]
        this.PlayerDeckCards[p] = [...dCards.filter((v,index)=>v.card_id != cardId),card]

        let deck_cards = CustomNetTables.GetTableValue("deck_cards","deck_cards");
        deck_cards.changedPlayer = playerId
        deck_cards.deck[p] = this.PlayerDeckCards[p]
        deck_cards.reserve[p] = this.PlayerReserveCards[p]
        CustomNetTables.SetTableValue("deck_cards", "deck_cards", deck_cards);
    }
   
    public initAllPlayerHandCardNetTable () {
        GameRules.FastWarPlayerHeroSpell.registerAllPlayerSpell()
        for (const id of GameRules.PlayerIDs) {
            this.precacheHandCard(id)
        }
        if (GameRules.preCacheAbs.length > 0) {
            GameRules.preCacheNum += 1
            PrecacheItemByNameAsync("item_for_precache", ()=>{
                GameRules.preCacheNum -= 1
            })
        }
        let hand_cards = CustomNetTables.GetTableValue("hand_cards","hand_cards");
        if (hand_cards == undefined) {
            hand_cards = {changedPlayer:-1,data:{}, heroSpell:{}};
        }
        hand_cards.changedPlayer = -1

        let fastWarCard = this
        Timers.CreateTimer(0.03,()=>{
            if (GameRules.preCacheNum <= 0) {
                for (const id of GameRules.PlayerIDs) {
                    let p = id.toString()
                    let dCards = fastWarCard.PlayerDeckCards[p]    
                    let res:GoFastWarCardClient[] = []       
                    for (let i = 0 ; i < dCards.length ; i ++) {
                        res.push(fastWarCard.createCardResourceWithCardId(id, dCards[i].card_index, -1, 0, 0, -1))
                    }
                    res = MathUtils.shuffle(res)
                    for (let i = 0; i < 4 ; i++) {
                        //处理手牌cd
                        fastWarCard.dealCardExtraCD(res[i])
                    }
                    fastWarCard.PlayerNowGameHandCards[p] = res
                    hand_cards.data[p] = fastWarCard.PlayerNowGameHandCards[p]

                    let info = GameRules.FastWarPlayerHeroSpell.getSpellNowInfo(id, PlayerHeroSpellFunction.CARD_INIT,{handCards:this.PlayerNowGameHandCards[p]})
                    hand_cards.heroSpell[p] = {
                        heroInfo:info
                    }
                    if (info.SpellCardIndex != -1) {
                        fastWarCard.unqCardId++
                        let newC:GoFastWarCardClient = fastWarCard.createCardResourceWithCardId(id,info.SpellCardIndex, info.nextUsingTime, info.cooldown, info.usesNum, info.cost)
                        hand_cards.heroSpell[p].cardInfo = newC
                    }
                }
                // print("预载完成")
                // DeepPrintTable(fastWarCard.PlayerNowGameHandCards)
                CustomNetTables.SetTableValue("hand_cards", "hand_cards", hand_cards);
                return 
            }
            return 0.03
        })
    }

    public precacheHandCard (playerId:PlayerID) {
        if (playerId == -1) {
            return;
        }
        let p = playerId.toString();
        let dCards = this.PlayerDeckCards[p]
        let index = [...dCards.map((v)=>v.card_index),...GameRules.FastWarPlayerHeroSpell.getAllInvolvedCardInfoForPrecache(playerId)]
        this.precacheCardResource(playerId, index)
    }

    private precacheCardResource(playerId:PlayerID, cards:number[]) {
        let p = playerId.toString();
        let player = PlayerResource.GetPlayer(playerId)
        let hero = player.GetAssignedHero()

        let unitName:string;
        let fun = ()=>{
            GameRules.preCacheNum -= 1
        }
        for (let i = 0 ; i < cards.length ; i++) {
            let cardInfo = GameRules.KVUtils.getCardsInfo(cards[i])
            if (cardInfo.cardType == "unit") {
                for (const t of cardInfo.UnitCardInfo.Template) {
                    unitName = GameRules.PlayerData.getPlayerSpecialCardInfo(playerId, t.Template, player.GetTeam())
                    GameRules.preCacheNum += 1
                    PrecacheUnitByNameAsync(unitName, () => fun(), playerId)

                    let temp = GameRules.KVUtils.getUnitTemplateInfoTreated(t.Template)
                    if (temp.ParEntUnit != undefined && temp.ParEntUnit != "") {
                        GameRules.preCacheNum += 1
                        PrecacheUnitByNameAsync(temp.ParEntUnit, () => fun(), playerId)
                    }
                }
            } else if (cardInfo.cardType == "spell") {
                if (this.PlayerHeroSpell[p][cardInfo.SpellCardInfo.SpellTemplate] == undefined) {
                    if (hero.FindAbilityByName(cardInfo.SpellCardInfo.SpellTemplate) == undefined) {
                        let ab = hero.AddAbility(cardInfo.SpellCardInfo.SpellTemplate)
                        ab.SetLevel(1)
                        ab.SetActivated(true)
                        this.PlayerHeroSpell[p][cardInfo.SpellCardInfo.SpellTemplate] = ab.GetAbilityIndex()
                        GameRules.preCacheAbs.push(ab)
                    } else {
                        let ab = hero.FindAbilityByName(cardInfo.SpellCardInfo.SpellTemplate)
                        this.PlayerHeroSpell[p][cardInfo.SpellCardInfo.SpellTemplate] = ab.GetAbilityIndex()
                    }
                }
            }
        }
       
    }

    public getDeckCardsForPlayer (playerId:PlayerID):GoFastWarCardClient[] {
        return this.PlayerDeckCards[playerId.toString()]
    }
    public getNowGameHandCardsForPlayer (playerId:PlayerID):GoFastWarCardClient[] {
        return this.PlayerNowGameHandCards[playerId.toString()]
    }

    public useCard (playerId:PlayerID, cardId:number,aimPos:Vector, bot:boolean, heroSpell:boolean):GoFastWarCardClient {
        let p = playerId.toString();
        let usesNum = 0;
        if (heroSpell) {
            if (!GameRules.FastWarPlayerHeroSpell.checkCooldown(playerId)) {
                print("英雄技能还在冷却")
                return 
            }
        } else {
            let cIndex = this.PlayerNowGameHandCards[p].map((v)=>v.card_id).indexOf(cardId)
            if (cIndex == -1 || cIndex > 3) {
                print("不允许使用不在手牌的卡牌！")
                return 
            }
        }
        let player = PlayerResource.GetPlayer(playerId)
        // DeepPrintTable(this.PlayerNowGameAllCards[p])
        let cardIndex:number;
        if (heroSpell) {
            let info = GameRules.FastWarPlayerHeroSpell.getSpellNowInfo(playerId, PlayerHeroSpellFunction.NONE,{})
            cardIndex = info.SpellCardIndex
        } else {
            cardIndex = this.PlayerNowGameAllCards[p][cardId].card_index
        }
        let cardInfo = GameRules.KVUtils.getCardsInfo(cardIndex)
        let cost = this.PlayerNowGameAllCards[p][cardId].cost
        //减去消耗
        let m = (CustomNetTables.GetTableValue("card_mana","card_mana"));
        if (m == undefined || m[p].mana < cost) {
            return
        }
        if (!GameRules.DebugOpen.debugMana) {
            m[p].mana = m[p].mana - cost
        }
        CustomNetTables.SetTableValue("card_mana", "card_mana", m)
        //播放预备序列动画
        if (cardInfo.cardType == "unit") {
            let buffs = GameRules.FastWarPlayerHeroSpell.getCardUsingBuff(playerId, PlayerHeroSpellFunction.CARD_BUFF, {cardUIType:cardInfo.CardUIType})
            GameRules.NPCUtils.usePreCreatedUnit(aimPos, cardId, cardIndex, cardInfo.UnitCardInfo.Delay, player.GetTeam(), buffs)
        } else if (cardInfo.cardType == "spell") {
            let hero = player.GetAssignedHero()
            ExecuteOrderFromTable({
                UnitIndex:hero.entindex(),
                AbilityIndex:hero.GetAbilityByIndex(this.PlayerHeroSpell[p][cardInfo.SpellCardInfo.SpellTemplate]).entindex(),
                OrderType:dotaunitorder_t.DOTA_UNIT_ORDER_CAST_POSITION,
                Position:aimPos,
                Queue:true,
            })
            // GameRules.FastWarSpell.castSpellOnPos(aimPos, cardId, player, cardInfo.spellTemplate, cardInfo.delay)
        }
        //除去当前使用卡牌，更新cardId后加到队列末尾，更新netTable数据
        let hand_cards = CustomNetTables.GetTableValue("hand_cards","hand_cards");
        hand_cards.changedPlayer = playerId
        let res:GoFastWarCardClient;
        if (heroSpell) {
            let info = GameRules.FastWarPlayerHeroSpell.getSpellNowInfo(playerId, PlayerHeroSpellFunction.AFTER_SPELL_USING, {})
            info = GameRules.FastWarPlayerHeroSpell.getSpellNowInfo(GameRules.FastWarPhaseController.GameTeamGroup[player.GetTeam()].enemyPlayer, PlayerHeroSpellFunction.AFTER_ENEMY_SPELL_USING, {playerId:playerId,info:info, UsingCardType:cardInfo.cardType})
            let newC:GoFastWarCardClient = undefined
            if (info.SpellCardIndex != -1) {
                newC = this.createCardResourceWithCardId(playerId,info.SpellCardIndex, info.nextUsingTime, info.cooldown, info.usesNum, info.cost)
            }
            hand_cards.heroSpell[p] = {
                heroInfo:info,
                cardInfo:newC
            }
            res = newC
        } else {
            let oldCards = this.PlayerNowGameHandCards[p]
            let newCards: any[] = []
            let newCardsEX: any[] = []
            let newCardsEnd: any[] = []
            let flag = oldCards.length
            let indexU = 4
            let usesNum = 0
            for (let i = 0 ; i < oldCards.length ; i++) {
                if (cardId == oldCards[i].card_id) {
                    flag = i
                    usesNum = oldCards[i].usesNum + 1
                }
                if (i < flag) {
                    newCards.push(oldCards[i])
                } else if (i > flag && i < indexU) {
                    newCardsEX.push(oldCards[i])
                } else if (i == indexU) {
                    newCards.push(oldCards[i])
                    this.dealCardExtraCD(oldCards[i])
                } else if (i > indexU) {
                    newCardsEnd.push(oldCards[i])
                }
            }
            let newC:GoFastWarCardClient = this.createCardResourceWithCardId(playerId,cardIndex,-1,0, usesNum, -1)
            res = newC
            this.PlayerNowGameHandCards[p] = [...newCards,...newCardsEX,...newCardsEnd,newC]
            hand_cards.data[p] = this.PlayerNowGameHandCards[p]
            
            let oldIndex = hand_cards.heroSpell[p].heroInfo.SpellCardIndex
            let info = GameRules.FastWarPlayerHeroSpell.getSpellNowInfo(playerId, PlayerHeroSpellFunction.AFTER_CARD_USING, {handCards:this.PlayerNowGameHandCards[p], oldIndex:oldIndex, UsingCardType:cardInfo.cardType})         
            info = GameRules.FastWarPlayerHeroSpell.getSpellNowInfo(GameRules.FastWarPhaseController.GameTeamGroup[player.GetTeam()].enemyPlayer, PlayerHeroSpellFunction.AFTER_ENEMY_CARD_USING, {playerId:playerId,info:info, UsingCardType:cardInfo.cardType})
            // DeepPrintTable(info)
            if (info.SpellCardIndex != oldIndex) {
                if (oldIndex != -1 && GameRules.KVUtils.getCardsInfo(hand_cards.heroSpell[p].heroInfo.SpellCardIndex).cardType == "unit") {
                    GameRules.NPCUtils.clearCardByID(hand_cards.heroSpell[p].cardInfo!.card_id)
                }
                let newSpellCard:GoFastWarCardClient = undefined
                if (info.SpellCardIndex != -1) {
                    newSpellCard = this.createCardResourceWithCardId(playerId,info.SpellCardIndex, info.nextUsingTime, info.cooldown, info.usesNum, info.cost)
                }
                hand_cards.heroSpell[p] = {
                    heroInfo:info,
                    cardInfo:newSpellCard
                }
            } else {
                hand_cards.heroSpell[p].heroInfo = info
            }
        }
        CustomNetTables.SetTableValue("hand_cards","hand_cards", hand_cards);
        return res
    }

    

    public createCardResource (playerId:PlayerID, cardIndex:number, nextUsingTime:number, cooldown:number,usesNum:number,cost:number) :GoFastWarCardClient {
        return this.createCardResourceWithCardId(playerId, cardIndex, nextUsingTime, cooldown, usesNum, cost)
    }

    /**
     * 根据cardId预创建单位，用于ui特效引用
     */
    private createCardResourceWithCardId(playerId:PlayerID, cardIndex:number, nextUsingTime:number, cooldown:number, usesNum:number, cost:number):GoFastWarCardClient {
        this.unqCardId++
        let cardId:number = this.unqCardId
        // print("玩家："+playerId+"预创建卡牌："+cardIndex+",cardId为："+cardId)
        let player = PlayerResource.GetPlayer(playerId)
        let cardInfo = GameRules.KVUtils.getCardsInfo(cardIndex)
        let entityIndexs = ""
        let unitName = ""
        if (cardInfo.cardType == "unit") {
            let ts: any[] = []
            for (const t of cardInfo.UnitCardInfo.Template) {
                let templateInfo = GameRules.KVUtils.getUnitTemplateInfoTreated(t.Template)
                let u = GameRules.PlayerData.getPlayerSpecialCardInfo(playerId, t.Template, player.GetTeam())
                unitName += u 
                unitName += ","
                ts.push({
                    data: templateInfo,
                    unitName: u,
                    num:t.Num,
                })
            }
            entityIndexs = GameRules.NPCUtils.preCreateCardWithUnitName(player,cardId,cardInfo,ts, usesNum)
        }
        if (cost == -1) {
            cost = cardInfo.layCost
        }
        let res = {
            card_id:cardId,
            card_index:cardIndex,
            entity_indexs:entityIndexs,
            card_unit_name:unitName,
            card_subsidiary_unit_name:"",
            next_using_time:nextUsingTime,
            cooldown:cooldown,
            cost: this.dealCardExtraCost(cost, usesNum, cardInfo.ExtraData.HandCardCost_UsesCheckNum, cardInfo.ExtraData.HandCardCost, cardInfo.ExtraData.HandCardCost_MaxNum),
            usesNum:usesNum
        }
        this.PlayerNowGameAllCards[playerId.toString()][cardId] = res
        // DeepPrintTable(this.PlayerNowGameAllCards)
        return res
    }


    public getCardInfo (cardIds:number[],playerId:PlayerID) {
        let res:GoFastWarCardClient[] = []
        let team = PlayerResource.GetTeam(playerId)
        for (const c of cardIds) {
            this.unqCardId ++
            let info = GameRules.KVUtils.getCardsInfo(c)
            if (!info.Enable || !info.EnableForDeckCards) {
                // Skip this iteration - continue not supported in Lua
            } else {
            let unitName:string[] = []
            let subsidiaryUnits = new Set<string>()
            if (info.cardType == "unit") {
                for (const v of info.UnitCardInfo.Template) {
                    unitName.push(GameRules.PlayerData.getPlayerSpecialCardInfo(playerId, v.Template, team))
                    this.getSubUnitByTempName(v.Template,subsidiaryUnits)
                }
            } else if (info.cardType == "spell") {
                let spellInfo = GameRules.KVUtils.getSpellInfo(info.SpellCardInfo.SpellTemplate)
                if (spellInfo.UnitTemplate != undefined && spellInfo.UnitTemplate.length > 0) {
                    for (const t of spellInfo.UnitTemplate) {
                        this.getSubUnitByTempName(t,subsidiaryUnits)
                    }
                }
            }
            let sub = [...subsidiaryUnits].map((v)=>GameRules.PlayerData.getPlayerSpecialCardInfo(playerId, v, team)).filter((v)=>unitName.indexOf(v)==-1).join(",")
            res.push({
                card_id: this.unqCardId,
                card_index: c,
                entity_indexs:"",
                card_unit_name:unitName.join(","),
                card_subsidiary_unit_name:sub,
                next_using_time:-1,
                cost:info.layCost,
                cooldown:0,
                usesNum:0,
            })
            } // End of else block
        }
        return res
    }

    public getFixDeckCards (playerId:PlayerID, deckOrReserve:boolean) {
        let fixCards = GameRules.KVUtils.getFixDeckCards(playerId, deckOrReserve)
        return this.getCardInfo(fixCards,playerId)
    }

    private getSubUnitByTempName (template:string, res:Set<string>):Set<string> {
        if (!res.has(template)) {
            res.add(template)
            let t = GameRules.KVUtils.getUnitTemplateInfoTreated(template)
            if (t != undefined) {
                for (const ab of t.Abilities) {
                    let spellInfo = GameRules.KVUtils.getSpellInfo(ab)
                    if (spellInfo != undefined && spellInfo.UnitTemplate.length > 0) {
                        for (const t of spellInfo.UnitTemplate) {
                            res = this.getSubUnitByTempName(t,res)
                        }                    
                    }
                }
            }
        }
        return res
    }

    public clearCard () {
        for (const pid of GameRules.PlayerIDs) {
            let p = pid.toString()
            let hero = PlayerResource.GetPlayer(pid).GetAssignedHero()
            Object.keys(this.PlayerHeroSpell[p]).forEach((v)=>{
                if (hero.FindAbilityByName(v)) {
                    hero.RemoveAbility(v)
                }
            })
            this.PlayerHeroSpell[p] = {}
        }
        
    }

    public dealCardExtraCD (cardInfo:GoFastWarCardClient):GoFastWarCardClient {
        let cInfo = GameRules.KVUtils.getCardsInfo(cardInfo.card_index)
        cardInfo.cooldown = cInfo.ExtraData.HandCardCooldown + cardInfo.usesNum * cInfo.ExtraData.HandCardCooldown_UseAdd
        cardInfo.next_using_time = GameRules.GetGameTime() + cardInfo.cooldown
        return cardInfo
    }

    public dealCardExtraCost (cost:number, usesNum:number, HandCardCost_UsesCheckNum:number, HandCardCost:number, HandCardCost_MaxNum:number):number {
        return Math.max( cost + Math.min(MathUtils.div(usesNum, HandCardCost_UsesCheckNum), HandCardCost_MaxNum) * HandCardCost, 0)
    }

}


