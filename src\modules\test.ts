import { modifier_healthbar_pip } from "../modifiers/modifier_healthbar_pip"
import { modifier_pre_unit_alternative } from "../modifiers/modifier_pre_unit_alternative"
import { modifier_test } from "../modifiers/modifier_test"
import { AIController } from "./fastwarai"
import { FastWarCard } from "./fastwarcard"
import { FastWarGamePhaseController } from "./fastwargamephasecontroller"
import { NPCUtils } from "./npcutils"
import { PlayerData } from "./playerdata"

export class Test {

    public static test() {
        
        Convars.RegisterCommand( "testBot", (_name, _arg1)=>{
            GameRules.testAI = new AIController(1,0)
            GameRules.testAI.initAI()

        }, "[dotago]1*1*1*1*1*1**1", 0 ) 


        Convars.RegisterCommand( "test2", (_name, _arg1)=>{
            let player = PlayerResource.GetPlayer(0)
            let hero = player.GetAssignedHero()

            let p = ParticleManager.CreateParticle("particles/units/heroes/heroes_underlord/underlord_pitofmalice.vpcf",  ParticleAttachment_t.PATTACH_ABSORIGIN, hero)
            ParticleManager.SetParticleControl(p, 0, hero.GetAbsOrigin())
            ParticleManager.SetParticleControl(p, 1, Vector(400,0,0))
            ParticleManager.SetParticleControl(p, 2, Vector(10,0,0))

            Timers.CreateTimer(0,()=>{
                ParticleManager.SetParticleControl(p, 0, hero.GetAbsOrigin())
                return 0.03
            })
            
        }, "[dotago]1*1*1*1*1*1**1", 0 ) 

        Convars.RegisterCommand( "testDefaultGame", (_name, _arg1)=>{
            GameRules.FastWarPhaseController.startDefaultGame()
            
        }, "[dotago]1*1*1*1*1*1**1", 0 ) 

        Convars.RegisterCommand( "testUnit", (_name, _arg1)=>{
            let hero = PlayerResource.GetPlayer(0).GetAssignedHero()
            let u = CreateUnitByName("npc_dota_hero_juggernaut",hero.GetAbsOrigin(), true, hero, hero, hero.GetTeam())
            
            let par = ParticleManager.CreateParticle("particles/gui/choose_card/unit_one/model2.vpcf", ParticleAttachment_t.PATTACH_WORLDORIGIN, undefined)
            ParticleManager.SetParticleAlwaysSimulate(par)
            ParticleManager.SetParticleControlEnt(par, 0, u, ParticleAttachment_t.PATTACH_POINT_FOLLOW, "attach_hitloc", Vector(0,0,0), true)
            ParticleManager.SetParticleControl(par, 0, u.GetAbsOrigin())
            //x:半径，y数量，z模型比例
            ParticleManager.SetParticleControl(par, 1, Vector(150,1,1))
            //x角度，y单位角度
            ParticleManager.SetParticleControl(par, 2, Vector(0,180,0))
            


        }, "[dotago]1*1*1*1*1*1**1", 0 ) 

        Convars.RegisterCommand( "testProcessEvent", (_name, _arg1)=>{
            let t = parseInt(_arg1) as 1|2|3|4

           CustomGameEventManager.Send_ServerToAllClients("go_game_process_change",{type:t,p1:0,p2:1})
        }, "[dotago]1*1*1*1*1*1**1", 0 ) 


        Convars.RegisterCommand( "testPurge", (_name, _arg1)=>{
            let hero = PlayerResource.GetPlayer(0).GetAssignedHero()
            hero.Purge(true, false, false, false, false)

            Timers.CreateTimer(3,()=>{
                hero.Purge(false, true, false, false, false)
            })
            
        }, "[dotago]1*1*1*1*1*1**1", 0 ) 


        Convars.RegisterCommand( "testPrintHandCards", (_name, _arg1)=>{
            let h = CustomNetTables.GetTableValue("hand_cards","hand_cards");
            DeepPrintTable(h.heroSpell)

        }, "[dotago]1*1*1*1*1*1**1", 0 ) 

        Convars.RegisterCommand( "testPrintNowCards", (_name, _arg1)=>{
            GameRules.FastWarCard.debugPrintNowDeckCards()
        }, "[dotago]1*1*1*1*1*1**1", 0 ) 
        
        Convars.RegisterCommand( "testNoCost", (_name, _arg1)=>{
            GameRules.DebugOpen.debugMana = !GameRules.DebugOpen.debugMana
            print("FastWarDebug用牌无消耗功能:"+(GameRules.DebugOpen.debugMana?"开启":"关闭"))
        }, "[dotago]1*1*1*1*1*1**1", 0 ) 

        
        Convars.RegisterCommand( "testNoDamage", (_name, _arg1)=>{
            let t = parseInt(_arg1) as 0|1|2|3
            GameRules.DebugOpen.debugDamage = t
            switch (GameRules.DebugOpen.debugDamage) {
                case 0:
                    print("FastWarDebug完全开启伤害")
                case 1:
                    print("FastWarDebug BadGuy无伤害")
                case 2:
                    print("FastWarDebug GoodGuy无伤害")
                case 3:
                    print("FastWarDebug全无伤害")
            }
        }, "[dotago]1*1*1*1*1*1**1", 0 ) 

    }

}


{
type Vector3 = { x: number; y: number; z: number };
type PhysicsFlags = {
    isGrounded: boolean;
    isJumping: boolean;
    inAirTime: number;
};
interface IPhysicsEntity {
    Origin: Vector3;
    Angles: Vector3;
    Physics: {
        velocity: Vector3;
        acceleration: Vector3;
        mass: number;
        flags: PhysicsFlags;
    };
}

class PhysicsSimulator {
    private entities: IPhysicsEntity[] = [];
    private gravity: Vector3 = { x: 0, y: 0, z: -9.81 };
    private simulationBehaviors: ((entity: IPhysicsEntity, deltaTime: number) => void)[] = [];

    constructor() {
        this.registerCoreBehaviors();
    }

    // 核心物理行为注册
    private registerCoreBehaviors() {
        this.simulationBehaviors.push(
            this.applyGravity.bind(this),
            this.applyGroundDetection.bind(this),
            this.basicMotion.bind(this)
        );
    }

    // 添加可扩展的物理行为
    public registerBehavior(behavior: (entity: IPhysicsEntity, deltaTime: number) => void) {
        this.simulationBehaviors.push(behavior);
    }

    public addEntity(entity: IPhysicsEntity) {
        this.entities.push(entity);
    }

    public update(deltaTime: number) {
        for (const entity of this.entities) {
            this.simulationBehaviors.forEach(behavior => behavior(entity, deltaTime));
            this.integrate(entity, deltaTime);
        }
    }

    // 基础运动积分计算
    private integrate(entity: IPhysicsEntity, deltaTime: number) {
        // 速度积分
        entity.Physics.velocity.x += entity.Physics.acceleration.x * deltaTime;
        entity.Physics.velocity.y += entity.Physics.acceleration.y * deltaTime;
        entity.Physics.velocity.z += entity.Physics.acceleration.z * deltaTime;

        // 位置积分
        entity.Origin.x += entity.Physics.velocity.x * deltaTime;
        entity.Origin.y += entity.Physics.velocity.y * deltaTime;
        entity.Origin.z += entity.Physics.velocity.z * deltaTime;

        // 重置加速度
        entity.Physics.acceleration = { x: 0, y: 0, z: 0 };
    }

    /********************
     * 核心物理行为实现 *
     ********************/
    
    // 重力应用
    private applyGravity(entity: IPhysicsEntity) {
        if (!entity.Physics.flags.isGrounded) {
            entity.Physics.acceleration.z += this.gravity.z;
        }
    }

    // 地面检测（简化版）
    private applyGroundDetection(entity: IPhysicsEntity) {
        const groundLevel = 0; // 实际项目应根据场景数据判断
        if (entity.Origin.z <= groundLevel) {
            entity.Origin.z = groundLevel;
            entity.Physics.flags.isGrounded = true;
            entity.Physics.velocity.z = 0;
        } else {
            entity.Physics.flags.isGrounded = false;
        }
    }

    // 基础运动阻尼
    private basicMotion(entity: IPhysicsEntity) {
        const airResistance = 0.98;  // 空气阻力系数
        const groundFriction = 0.85;  // 地面摩擦力

        if (entity.Physics.flags.isGrounded) {
            entity.Physics.velocity.x *= groundFriction;
            entity.Physics.velocity.y *= groundFriction;
        } else {
            entity.Physics.velocity.x *= airResistance;
            entity.Physics.velocity.y *= airResistance;
        }
    }

    /********************
     * 预置行为扩展示例 *
     ********************/
    
    // 跳跃行为（可作为独立模块注册）
    public static createJumpBehavior(jumpForce: number): (entity: IPhysicsEntity) => void {
        return (entity: IPhysicsEntity) => {
            if (entity.Physics.flags.isGrounded) {
                const jumpAcceleration = jumpForce / entity.Physics.mass;
                entity.Physics.velocity.z = jumpAcceleration;
                entity.Physics.flags.isGrounded = false;
            }
        };
    }

    // 落地缓冲系统
    public static createLandingBuffer(bufferFactor: number): (entity: IPhysicsEntity, deltaTime: number) => void {
        return (entity: IPhysicsEntity, deltaTime: number) => {
            if (entity.Physics.flags.isGrounded && Math.abs(entity.Physics.velocity.z) > 0.1) {
                const impactReduction = 1 - (bufferFactor * deltaTime);
                entity.Physics.velocity.z *= impactReduction;
                
                // 角度缓冲效果
                const tiltAngle = Math.min(15, Math.abs(entity.Physics.velocity.z) * 2);
                entity.Angles.x = -tiltAngle * impactReduction;
            }
        };
    }
}

// 使用示例
const simulator = new PhysicsSimulator();

// 注册扩展行为
simulator.registerBehavior(PhysicsSimulator.createJumpBehavior(4.5));
simulator.registerBehavior(PhysicsSimulator.createLandingBuffer(0.8));

// 创建实体
const playerEntity: IPhysicsEntity = {
    Origin: { x: 0, y: 0, z: 0 },
    Angles: { x: 0, y: 0, z: 0 },
    Physics: {
        velocity: { x: 0, y: 0, z: 0 },
        acceleration: { x: 0, y: 0, z: 0 },
        mass: 75,
        flags: {
            isGrounded: true,
            isJumping: false,
            inAirTime: 0
        }
    }
};

simulator.addEntity(playerEntity);

}