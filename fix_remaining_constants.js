const fs = require('fs');
const path = require('path');

// 定义需要修复的模式和替换
const fixes = [
    // 修复单位目标常量 - 使用正确的枚举名称
    { pattern: /DOTA_UNIT_TARGET\.DOTA_UNIT_TARGET_HERO/g, replacement: 'DOTA_UNIT_TARGET_HERO' },
    { pattern: /DOTA_UNIT_TARGET\.DOTA_UNIT_TARGET_CREEP/g, replacement: 'DOTA_UNIT_TARGET_CREEP' },
    { pattern: /DOTA_UNIT_TARGET\.DOTA_UNIT_TARGET_BUILDING/g, replacement: 'DOTA_UNIT_TARGET_BUILDING' },
    
    // 修复查找顺序常量
    { pattern: /FIND_ORDER_TYPE\.FIND_ANY_ORDER/g, replacement: 'FIND_ANY_ORDER' },
    { pattern: /FIND_ORDER_TYPE\.FIND_CLOSEST/g, replacement: 'FIND_CLOSEST' },
    
    // 修复游戏活动常量
    { pattern: /ACT_DOTA_CAST_ABILITY_1/g, replacement: 'GameActivity_t.ACT_DOTA_CAST_ABILITY_1' },
    { pattern: /ACT_DOTA_CAST_ABILITY_3/g, replacement: 'GameActivity_t.ACT_DOTA_CAST_ABILITY_3' },
    { pattern: /GameActivity_t\.GameActivity_t\.ACT_DOTA_SPAWN/g, replacement: 'GameActivity_t.ACT_DOTA_SPAWN' },
    
    // 修复修饰符属性常量 - 移除错误的命名空间
    { pattern: /MODIFIER_PROPERTY\.MODIFIER_PROPERTY_ATTACKSPEED_BONUS_CONSTANT/g, replacement: 'MODIFIER_PROPERTY_ATTACKSPEED_BONUS_CONSTANT' },
    { pattern: /MODIFIER_PROPERTY\.MODIFIER_PROPERTY_MOVESPEED_BONUS_CONSTANT/g, replacement: 'MODIFIER_PROPERTY_MOVESPEED_BONUS_CONSTANT' },
    { pattern: /MODIFIER_PROPERTY\.MODIFIER_PROPERTY_PREATTACK_BONUS_DAMAGE/g, replacement: 'MODIFIER_PROPERTY_PREATTACK_BONUS_DAMAGE' },
    { pattern: /MODIFIER_PROPERTY\.MODIFIER_PROPERTY_MAGICAL_RESISTANCE_BONUS/g, replacement: 'MODIFIER_PROPERTY_MAGICAL_RESISTANCE_BONUS' },
    { pattern: /MODIFIER_PROPERTY\.MODIFIER_PROPERTY_OVERRIDE_ANIMATION/g, replacement: 'MODIFIER_PROPERTY_OVERRIDE_ANIMATION' },
    { pattern: /MODIFIER_PROPERTY\.MODIFIER_PROPERTY_TRANSLATE_ACTIVITY_MODIFIERS/g, replacement: 'MODIFIER_PROPERTY_TRANSLATE_ACTIVITY_MODIFIERS' },
    { pattern: /MODIFIER_PROPERTY\.MODIFIER_PROPERTY_VISUAL_Z_DELTA/g, replacement: 'MODIFIER_PROPERTY_VISUAL_Z_DELTA' },
    { pattern: /MODIFIER_PROPERTY\.MODIFIER_PROPERTY_ATTACK_RANGE_BONUS/g, replacement: 'MODIFIER_PROPERTY_ATTACK_RANGE_BONUS' },
    { pattern: /MODIFIER_PROPERTY\.MODIFIER_PROPERTY_INCOMING_DAMAGE_CONSTANT/g, replacement: 'MODIFIER_PROPERTY_INCOMING_DAMAGE_CONSTANT' },
    { pattern: /MODIFIER_PROPERTY\.MODIFIER_PROPERTY_HEALTHBAR_PIPS/g, replacement: 'MODIFIER_PROPERTY_HEALTHBAR_PIPS' },
    
    // 修复修饰符状态常量 - 移除错误的命名空间
    { pattern: /MODIFIER_STATE\.MODIFIER_STATE_DISARMED/g, replacement: 'MODIFIER_STATE_DISARMED' },
    { pattern: /MODIFIER_STATE\.MODIFIER_STATE_ATTACK_IMMUNE/g, replacement: 'MODIFIER_STATE_ATTACK_IMMUNE' },
    { pattern: /MODIFIER_STATE\.MODIFIER_STATE_NO_HEALTH_BAR/g, replacement: 'MODIFIER_STATE_NO_HEALTH_BAR' },
    { pattern: /MODIFIER_STATE\.MODIFIER_STATE_ROOTED/g, replacement: 'MODIFIER_STATE_ROOTED' },
    { pattern: /MODIFIER_STATE\.MODIFIER_STATE_CANNOT_BE_MOTION_CONTROLLED/g, replacement: 'MODIFIER_STATE_CANNOT_BE_MOTION_CONTROLLED' },
    { pattern: /MODIFIER_STATE\.MODIFIER_STATE_NOT_ON_MINIMAP/g, replacement: 'MODIFIER_STATE_NOT_ON_MINIMAP' },
    { pattern: /MODIFIER_STATE\.MODIFIER_STATE_INVULNERABLE/g, replacement: 'MODIFIER_STATE_INVULNERABLE' },
    { pattern: /MODIFIER_STATE\.MODIFIER_STATE_OUT_OF_GAME/g, replacement: 'MODIFIER_STATE_OUT_OF_GAME' },
    { pattern: /MODIFIER_STATE\.MODIFIER_STATE_NO_UNIT_COLLISION/g, replacement: 'MODIFIER_STATE_NO_UNIT_COLLISION' },
    { pattern: /MODIFIER_STATE\.MODIFIER_STATE_UNSELECTABLE/g, replacement: 'MODIFIER_STATE_UNSELECTABLE' },
    
    // 修复修饰符属性常量 - 移除错误的命名空间
    { pattern: /MODIFIER_ATTRIBUTE\.MODIFIER_ATTRIBUTE_PERMANENT/g, replacement: 'MODIFIER_ATTRIBUTE_PERMANENT' },
    { pattern: /MODIFIER_ATTRIBUTE\.MODIFIER_ATTRIBUTE_IGNORE_INVULNERABLE/g, replacement: 'MODIFIER_ATTRIBUTE_IGNORE_INVULNERABLE' },
    { pattern: /MODIFIER_ATTRIBUTE\.MODIFIER_ATTRIBUTE_MULTIPLE/g, replacement: 'MODIFIER_ATTRIBUTE_MULTIPLE' },
    
    // 修复 FCVAR 常量
    { pattern: /FCVAR\.FCVAR_NONE/g, replacement: 'FCVAR_NONE' },
    
    // 修复修饰符状态的错误引用
    { pattern: /modifierstate\.modifierstate\.MODIFIER_STATE_STUNNED/g, replacement: 'MODIFIER_STATE_STUNNED' },
    { pattern: /modifierstate\.modifierstate\.MODIFIER_STATE_PASSIVES_DISABLED/g, replacement: 'MODIFIER_STATE_PASSIVES_DISABLED' },
    
    // 修复粒子附着常量
    { pattern: /ParticleAttachment_t\.ParticleAttachment_t\.PATTACH_OVERHEAD_FOLLOW/g, replacement: 'ParticleAttachment_t.PATTACH_OVERHEAD_FOLLOW' },
    
    // 修复 Lua 修饰符运动类型
    { pattern: /LuaModifierMotionType\.LUA_MODIFIER_MOTION_NONE/g, replacement: 'LUA_MODIFIER_MOTION_NONE' },
    { pattern: /LuaModifierMotionType\.LUA_MODIFIER_MOTION_BOTH/g, replacement: 'LUA_MODIFIER_MOTION_BOTH' },
    { pattern: /LuaModifierMotionType\.LUA_MODIFIER_MOTION_HORIZONTAL/g, replacement: 'LUA_MODIFIER_MOTION_HORIZONTAL' },
    { pattern: /LuaModifierMotionType\.LUA_MODIFIER_MOTION_VERTICAL/g, replacement: 'LUA_MODIFIER_MOTION_VERTICAL' },
    
    // 修复其他常量
    { pattern: /MODIFIER_STATE_DEBUFF_IMMUNE/g, replacement: 'MODIFIER_STATE_DEBUFF_IMMUNE' },
    { pattern: /MODIFIER_STATE_FROZEN/g, replacement: 'MODIFIER_STATE_FROZEN' },
    { pattern: /MODIFIER_EVENT_ON_DAMAGE_CALCULATED/g, replacement: 'MODIFIER_EVENT_ON_DAMAGE_CALCULATED' },
    { pattern: /ModifierState\.MAGIC_IMMUNE/g, replacement: 'MODIFIER_STATE_MAGIC_IMMUNE' },
];

// 递归遍历目录
function walkDir(dir, callback) {
    const files = fs.readdirSync(dir);
    files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        if (stat.isDirectory()) {
            walkDir(filePath, callback);
        } else if (stat.isFile() && (file.endsWith('.ts') || file.endsWith('.tsx'))) {
            callback(filePath);
        }
    });
}

// 修复文件
function fixFile(filePath) {
    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;
    
    fixes.forEach(fix => {
        if (fix.pattern.test(content)) {
            content = content.replace(fix.pattern, fix.replacement);
            changed = true;
        }
    });
    
    if (changed) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`Fixed remaining constants in: ${filePath}`);
    }
}

// 主函数
function main() {
    const srcDir = path.join(__dirname, 'src');
    console.log('Starting remaining constants fixes...');
    
    walkDir(srcDir, fixFile);
    
    console.log('Remaining constants fixes completed!');
}

main();
