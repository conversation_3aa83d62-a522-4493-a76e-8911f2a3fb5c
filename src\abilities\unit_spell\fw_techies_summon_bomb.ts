import { BaseAbility, registerAbility } from "../../utils/dota_ts_adapter";

export class fw_techies_summon_bomb extends BaseAbility {
    
    spell:SpellInfoKV;
    OnSpellStart(): void {
        let unit = this.GetCaster()
        let playerID = unit.GetPlayerOwnerID()
        let team = unit.GetTeam()
        let pos = unit.GetAbsOrigin().__add(unit.GetForwardVector().Normalized().__mul(90))
        let num = 1
        let creepTempName = "creep_default_techies_bomb"
        let tempInfo = GameRules.KVUtils.getUnitTemplateInfoTreated(creepTempName)
        let unitName = GameRules.PlayerData.getPlayerSpecialCardInfo(playerID, creepTempName, team)
        // print("召唤："+unitName)
        GameRules.NPCUtils.createUnitForSpell(playerID,unit,team,unit.cardId,1,tempInfo,num,unitName,pos,0, unit.cardUsesNum)
    }

    Spawn(): void {
        this.spell = GameRules.KVUtils.getSpellInfo("fw_techies_summon_bomb")
        if (this.spell.UnitTemplate.length > 0 ) {
            if (IsServer()) {
                let unit = this.GetCaster()
                let team = unit.GetTeam()
                let playerID = unit.GetPlayerOwnerID()
                for (const temp of this.spell.UnitTemplate) {
                    let unitName = GameRules.PlayerData.getPlayerSpecialCardInfo(playerID, temp, team)
                    GameRules.preCacheInfos.push(unitName)
                }
                // print("初始化炸弹小车附属单位，来自玩家："+playerID)
                // DeepPrintTable(GameRules.preCacheInfos)
                PrecacheItemByNameAsync("item_for_precache", ()=>{
                    // print("预载炸弹小车完成")
                })
            }
        }
    }
    
    Precache(context: CScriptPrecacheContext): void {
        PrecacheResource("particle","",context)
    }
}

