import 'panorama-polyfill-x/lib/console';
import 'panorama-polyfill-x/lib/timers';
// 事件系统实现
let registFWEvents = {};
let FWEventKeys = {};
let index = 0;
GameUI.registFWEvent = function (eventName, callback) {
    // $.Msg("注册事件：" + eventName)
    if (registFWEvents[eventName] === undefined) {
        registFWEvents[eventName] = [];
    }
    registFWEvents[eventName].push(callback);
};
GameUI.dispatchFWEvent = function (eventName, key, data) {
    let i = key;
    if (i === -1) {
        i = index++;
    }
    if (FWEventKeys[i] === undefined || FWEventKeys[i].indexOf(eventName) === -1) {
        if (FWEventKeys[i] === undefined) {
            FWEventKeys[i] = [];
        }
        FWEventKeys[i].push(eventName);
        const callbacks = registFWEvents[eventName];
        // $.Msg("触发事件：" + eventName)
        if (callbacks !== undefined && callbacks.length > 0) {
            for (const callback of callbacks) {
                callback(i, data);
            }
        }
    }
    if (key === -1) {
        FWEventKeys[i] = undefined;
    }
};
GameUI.SetHotKey = function (key, downCallback, upCallback) {
    const command = `DotaGoKey${key}${Date.now()}`;
    Game.CreateCustomKeyBind(key, `+${command}`);
    if (downCallback !== undefined) {
        Game.AddCommand(`+${command}`, () => {
            if (!GameUI.IsAltDown()) {
                downCallback();
            }
        }, '', 1 << 32);
    }
    if (upCallback !== undefined) {
        Game.AddCommand(`-${command}`, () => {
            upCallback();
        }, '', 1 << 32);
    }
};
GameUI.GetTimeStr = function (seconds) {
    seconds = Math.round(seconds);
    let str = '';
    if (seconds >= 60) {
        let minutes = Math.round(seconds / 60);
        if (minutes * 60 > seconds) {
            minutes -= 1;
        }
        seconds -= minutes * 60;
        str += minutes + ':';
    }
    else {
        str += '0:';
    }
    if (seconds > 0 && seconds < 10) {
        str += '0' + seconds;
    }
    else if (seconds === 0) {
        str += '00';
    }
    else {
        str += seconds;
    }
    return str;
};
// 游戏状态初始化
GameUI.GameTowerStatus = 0;
GameUI.nowChooseCardCost = 0;
GameUI.uiPID = 0;
GameUI.enemyUIPID = 1;
GameUI.GameTower = {
    [GameUI.uiPID]: [-1, -1, -1],
    [GameUI.enemyUIPID]: [-1, -1, -1],
};
GameUI.uiPIsGood = true;
GameUI.uiPIsPlayer = true;
GameUI.uiPHero = Players.GetPlayerHeroEntityIndex(GameUI.uiPID);
GameUI.uiPIsSpectator = true;
GameUI.handCards = [];
GameUI.preHandCards = [];
GameUI.DeckCardPanelOpen = true;
GameUI.manaInfo = {
    maxMana: 10,
    mana: 0,
    speed: 1,
    time: 0,
};
GameUI.ButtonGroupChoose = {};
GameUI.HeroSpInfo = {
    HeroName: '',
    HeroImage: '1',
    SpellCardIndex: -1,
    SpellName: '',
    cost: 1,
    nextUsingTime: -1,
    cooldown: 0,
    num: '',
    usesNum: 0,
};
GameUI.deckCardsMaxNum = 8;
GameUI.initHand = false;
GameUI.buttonBeAble = [0];
// 禁用默认 UI 元素
const uiElementsToDisable = [
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_TOP_TIMEOFDAY,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_TOP_HEROES,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_TOP_MENU_BUTTONS,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_TOP_BAR_BACKGROUND,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_TOP_BAR_RADIANT_TEAM,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_TOP_BAR_DIRE_TEAM,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_TOP_BAR_SCORE,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_TOP_BAR,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_ACTION_MINIMAP,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_INVENTORY_PANEL,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_INVENTORY_SHOP,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_INVENTORY_ITEMS,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_INVENTORY_QUICKBUY,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_INVENTORY_COURIER,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_INVENTORY_PROTECT,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_INVENTORY_GOLD,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_SHOP_SUGGESTEDITEMS,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_SHOP_COMMONITEMS,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_FLYOUT_SCOREBOARD,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_QUICK_STATS,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_KILLCAM,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_FIGHT_RECAP,
    DotaDefaultUIElement_t.DOTA_DEFAULT_UI_AGHANIMS_STATUS,
];
// 批量禁用 UI 元素
uiElementsToDisable.forEach(element => {
    GameUI.SetDefaultUIEnabled(element, false);
});
//# sourceMappingURL=script.js.map