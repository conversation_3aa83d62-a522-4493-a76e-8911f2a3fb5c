// 游戏配置接口
interface GameConfig {
    prepareDuration: number,
    mainDuration: number,
    overTimeDuration: number,
}

// 基础游戏阶段抽象类
abstract class GamePhaseHandler {
    constructor(
        protected controller: FastWarGamePhaseController,
        protected config: GameConfig,
        protected player1:CDOTAPlayerController,
        protected player2:CDOTAPlayerController,
    ) {}
    /**
     * 返回本阶段持续时间
     */
    abstract onEnter(prevPhase: FastWarGamePhase): number;
    /**
     * 返回下阶段状态
     */
    abstract onExit(e?:FastWarGameEvent): FastWarGamePhase;
    abstract phaseCheck(currentTime:number): void;
}

// 预准备阶段
class DefaultPreparePhaseHandler extends GamePhaseHandler {
   
    onEnter(prevPhase: FastWarGamePhase) {
        GameRules.FastWarCard.initAllPlayerHandCardNetTable()

        return this.config.prepareDuration
    }
    onExit(e?:FastWarGameEvent) {
        print("准备阶段，初始化防御塔")
        let team1 = this.player1.GetTeam()
        let str1 = (team1 == 2)?"goodguys":"badguys"
        let team2 = this.player2.GetTeam()
        let str2 = (team2 == 2)?"goodguys":"badguys"

        //为双方玩家建造防御塔和基地
        let ps = GameRules.EntityUtils.EntityOnlyPos["tower"]
        let t1 = GameRules.NPCUtils.CreateTower(1,team1 == 2, ps[str1+"1"].pos, ps[str1+"1"].angle, "tower_default", team1, this.player1)
        let t2 = GameRules.NPCUtils.CreateTower(2,team1 == 2, ps[str1+"2"].pos, ps[str1+"2"].angle, "tower_default", team1, this.player1)
        let t3 = GameRules.NPCUtils.CreateTower(3,team1 == 2, ps[str1+"3"].pos, ps[str1+"3"].angle, "tower_default_aciant", team1, this.player1)
        let t4 = GameRules.NPCUtils.CreateTower(1,team2 == 2, ps[str2+"1"].pos, ps[str2+"1"].angle, "tower_default", team2, this.player2)
        let t5 = GameRules.NPCUtils.CreateTower(2,team2 == 2, ps[str2+"2"].pos, ps[str2+"2"].angle, "tower_default", team2, this.player2)
        let t6 = GameRules.NPCUtils.CreateTower(3,team2 == 2, ps[str2+"3"].pos, ps[str2+"3"].angle, "tower_default_aciant", team2, this.player2)
        let p: Record<string, any> = {}
        p[this.player2.GetPlayerID().toString()] = {
            tower1:t1.entindex(),
            tower2:t2.entindex(),
            tower3:t3.entindex(),
        }
        p[this.player1.GetPlayerID().toString()] = {
            tower1:t4.entindex(),
            tower2:t5.entindex(),
            tower3:t6.entindex(),
        }
        CustomNetTables.SetTableValue("fast_war_game_phase","fast_war_game_tower",p)

        return FastWarGamePhase.MainTime
    }
    phaseCheck() {
        
    }
}

// 主计时阶段处理
class DefaultMainPhaseHandler extends GamePhaseHandler {

    onEnter(prevPhase: FastWarGamePhase) {
        print("主阶段开始：")
        GameRules.SetTimeOfDay(0.4)
        GameRules.FastWarManaUpSpeed = 2
        return this.config.mainDuration
    }
    onExit(e?:FastWarGameEvent) {
        if (e != undefined && e == FastWarGameEvent.AcientDestroyed) {
            return FastWarGamePhase.TempEnd
        } 
        let p2t = this.controller.GameTeamGroup[this.player1.GetTeam()]
        let p1t = this.controller.GameTeamGroup[this.player2.GetTeam()]
        let t2= p2t.enemyTower[0] + p2t.enemyTower[1]
        let t1= p1t.enemyTower[0] + p1t.enemyTower[1]
        if (t2 != t1) {
            CustomGameEventManager.Send_ServerToAllClients("go_game_process_change",{type:(t2 > t1)?(p2t.enemyIsGoodGuy?3:4):(p1t.enemyIsGoodGuy?3:4)})
            return FastWarGamePhase.TempEnd
        }
        return FastWarGamePhase.Overtime
    }
    phaseCheck(currentTime:number) {
        
    }
}
// 加时赛阶段处理
class DefaultOvertimePhaseHandler extends GamePhaseHandler {

    onEnter(prevPhase: FastWarGamePhase) {
        this.player1.SetMusicStatus(2,1.0)
        this.player2.SetMusicStatus(2,1.0)
        print("进入加时赛阶段")
        CustomGameEventManager.Send_ServerToAllClients("go_game_process_change",{type:5})
        //双倍圣水
        GameRules.FastWarManaUpSpeed = 1
        return this.config.overTimeDuration
    }
    onExit(e?:FastWarGameEvent) {
        let flag = GameRules.NPCUtils.checkTowerHealthWhoWin(this.player1.GetTeam(), this.player2.GetTeam())
        CustomGameEventManager.Send_ServerToAllClients("go_game_process_change",{type:this.controller.GameTeamGroup[flag == 1?this.player1.GetTeam():this.player2.GetTeam()].enemyIsGoodGuy?3:4})
        return FastWarGamePhase.TempEnd
    }
    phaseCheck(currentTime:number) {
        
    }
}
// 结束阶段处理
class DefaultEndPhaseHandler extends GamePhaseHandler {

    onEnter(prevPhase: FastWarGamePhase) {
        print("进入结束阶段")
        this.player1.SetMusicStatus(3,1.0)
        this.player2.SetMusicStatus(3,1.0)
        return this.config.overTimeDuration
    }
    onExit(e?:FastWarGameEvent) {
        GameRules.NPCUtils.clearUnits()
        return FastWarGamePhase.None
    }
    phaseCheck() {
        
    }
}
abstract class GameEventsHandler {
    constructor(
        protected controller: FastWarGamePhaseController,
        protected handleEventType: FastWarGameEvent,
    ) {}

    handleMessage (event: FastWarGameEvent, data:any) {
        if (this.handleEventType != event) {
            return
        }
        this.handler(event,data)
    }
    protected abstract handler(event: FastWarGameEvent, data: any):void;
}

// 防御塔摧毁事件
class TowerDestroyEventHandle extends GameEventsHandler {
    handler(event: FastWarGameEvent, data:any): void {
        print("触发防御塔摧毁事件："+data.towerIndex+","+data.teamNumber)
        let p = CustomNetTables.GetTableValue("fast_war_game_phase","fast_war_game_tower")
        let s = ""
        switch (data.towerIndex) {
            case 1:
                p[data.enemyPID.toString()].tower1 = -1
                if (GameRules.fastWarGameStatus == FastWarGamePhase.Overtime) {
                    this.controller.changePhase(FastWarGameEvent.TowerDestroyed)
                    CustomGameEventManager.Send_ServerToAllClients("go_game_process_change",{type:data.enemyIsGood?3:4})
                } else if (GameRules.fastWarGameStatus != FastWarGamePhase.TempEnd) {
                    s = data.enemyIsGood?"Fw.UI.Process.Good.Tower.top.fall":"Fw.UI.Process.Bad.Tower.top.fall"
                }
                break;
            case 2:
                p[data.enemyPID.toString()].tower2 = -1
                if (GameRules.fastWarGameStatus == FastWarGamePhase.Overtime) {
                    this.controller.changePhase(FastWarGameEvent.TowerDestroyed)
                    CustomGameEventManager.Send_ServerToAllClients("go_game_process_change",{type:data.enemyIsGood?3:4})
                } else if (GameRules.fastWarGameStatus != FastWarGamePhase.TempEnd) {
                    s = data.enemyIsGood?"Fw.UI.Process.Good.Tower.top.fall":"Fw.UI.Process.Bad.Tower.top.fall"
                }
                break;
            case 3:
                p[data.enemyPID.toString()].tower3 = -1
                if (GameRules.fastWarGameStatus == FastWarGamePhase.MainTime || GameRules.fastWarGameStatus == FastWarGamePhase.Overtime) {
                    this.controller.changePhase(FastWarGameEvent.AcientDestroyed)
                    CustomGameEventManager.Send_ServerToAllClients("go_game_process_change",{type:data.enemyIsGood?3:4})
                }
                break;
        }
        if (s != "") {
            CustomGameEventManager.Send_ServerToAllClients("go_ui_sound",{
                soundNames:s,
                group:4,
                interval:0,
            }) 
        }
        CustomNetTables.SetTableValue("fast_war_game_phase","fast_war_game_tower", p)
    }
}


// 游戏主控制器
export class FastWarGamePhaseController {
    private currentTime:number;
    private currentPhase: FastWarGamePhase;
    private currentPhaseHandler: GamePhaseHandler;
    private phaseHandlers: Map<FastWarGamePhase, GamePhaseHandler>;
    private gameEventHandlers:GameEventsHandler[];
    /**
     * 用于储存当前对战的队伍对阵情况
     */
    public GameTeamGroup : {
        [keys:number]:{
            enemyPlayer:PlayerID,
            enemyTeamNumber:number,
            enemyIsGoodGuy:boolean,
            enemyTower:[number,number,number]
        }
    } = {}

    private curGameMode:[GoFastWarGameModeEnum,number] = [GoFastWarGameModeEnum.None, -1]

    private timer:string;
    private timer2:string;
    constructor() {
       
    }

    public chooseMode (group:GoFastWarGameModeEnum, mode:number) {
        let info = CustomNetTables.GetTableValue("fast_war_game_phase","fast_war_game_mode")
        info.group = group
        info.mode = mode
        CustomNetTables.SetTableValue("fast_war_game_phase","fast_war_game_mode",info);
    }

    // 启动游戏
    start(phaseHs: Map<FastWarGamePhase, GamePhaseHandler>, gameEventHs:GameEventsHandler[], currentP:FastWarGamePhase,players:PlayerID[]) {
        let info = CustomNetTables.GetTableValue("fast_war_game_phase","fast_war_game_mode")
        info.nowGameGroup = info.group
        info.nowGameMode = info.mode
        CustomNetTables.SetTableValue("fast_war_game_phase","fast_war_game_mode",info);

        this.phaseHandlers = phaseHs
        this.gameEventHandlers = gameEventHs
        this.currentPhase = currentP
        GameRules.fastWarGameStatus = currentP
        this.currentPhaseHandler = this.phaseHandlers.get(currentP)
        this.currentTime = this.currentPhaseHandler.onEnter(FastWarGamePhase.None);
        
        if (this.timer != undefined) {
            Timers.RemoveTimer(this.timer)
            Timers.RemoveTimer(this.timer2)
        }
        let goFastWarGameC = this
        this.timer = Timers.CreateTimer(0.5,()=>{
            CustomNetTables.SetTableValue("fast_war_game_phase","fast_war_game_phase", {
                time:Math.max(goFastWarGameC.currentTime-1,0),
                gameTime:GameRules.GetGameTime(),
                gamePhase:goFastWarGameC.currentPhase,
            });
            if (goFastWarGameC.currentTime == 0) {
                return 
            } else {
                if (goFastWarGameC.currentPhase == FastWarGamePhase.MainTime || goFastWarGameC.currentPhase == FastWarGamePhase.Overtime) {
                    let sound = ""
                    switch (goFastWarGameC.currentTime) {
                        case 4:
                            sound = "Fw.UI.Process.Round.ThreeSeconds"
                            break;
                        case 6:
                            sound = "Fw.UI.Process.Round.FiveSeconds"
                            break;
                        case 11:
                            sound = "Fw.UI.Process.Round.TenSeconds"
                            break;
                    }
                    CustomGameEventManager.Send_ServerToAllClients("go_ui_sound",{
                        soundNames:sound,
                        group:5,
                        interval:0,
                    }) 
                }   
            } 
            goFastWarGameC.currentPhaseHandler.phaseCheck(goFastWarGameC.currentTime)
            goFastWarGameC.currentTime--
            if (goFastWarGameC.currentTime <= 0 && !goFastWarGameC.changePhase()) {
                goFastWarGameC.currentPhase = FastWarGamePhase.None;
                goFastWarGameC.currentTime = 0
            }
            return 1
        })
        let manaInfo: Record<string, any> = {}
        for (const p of players) {
            manaInfo[p.toString()] = {
                time: GameRules.GetGameTime(),
                speed: 1,
                mana: 3,
                maxMana: 10,
                hasUp:1,
            }
        }
        CustomNetTables.SetTableValue("card_mana", "card_mana",manaInfo)
        this.timer2 = Timers.CreateTimer(0.5,()=>{
            if (GameRules.fastWarGameStatus == FastWarGamePhase.MainTime || GameRules.fastWarGameStatus == FastWarGamePhase.Overtime) {
                let m = (CustomNetTables.GetTableValue("card_mana","card_mana"));
                for (const id of players) {
                    if (m[id.toString()].mana < m[id.toString()].maxMana) {
                        if (m[id.toString()].hasUp == 0) {
                            m[id.toString()].hasUp = 1
                        } else {
                            m[id.toString()].time = GameRules.GetGameTime()
                            m[id.toString()].speed = GameRules.FastWarManaUpSpeed
                            m[id.toString()].mana = m[id.toString()].mana + 1
                            if (m[id.toString()].mana >= m[id.toString()].maxMana) {
                                m[id.toString()].hasUp = 0
                            }
                        }
                    }
                }
                CustomNetTables.SetTableValue("card_mana", "card_mana",m)
                return GameRules.FastWarManaUpSpeed
            }
            return 0.02
        })

        CustomGameEventManager.Send_ServerToAllClients("go_phase_change",{
            oldPhase:FastWarGamePhase.None,
            newPhase:currentP,
        }) 
        return true
    }

    // 切换阶段
    changePhase(eventType?:FastWarGameEvent) {
        const oldPhase = this.currentPhase;
        let newPhase = this.currentPhaseHandler.onExit(eventType);
        if (newPhase == undefined || newPhase == FastWarGamePhase.None) {
            this.emit(FastWarGameEvent.PhaseEnd,{})
            if (this.timer != undefined) {
                Timers.RemoveTimer(this.timer)
                Timers.RemoveTimer(this.timer2)
            }
            return false
        }
        this.currentPhase = newPhase;
        GameRules.fastWarGameStatus = newPhase
        let newHandler = this.phaseHandlers.get(newPhase)
        this.currentPhaseHandler = newHandler
        this.currentTime = newHandler.onEnter(oldPhase);
        this.emit(FastWarGameEvent.PhaseChange,{})
        CustomGameEventManager.Send_ServerToAllClients("go_phase_change",{
            oldPhase:oldPhase,
            newPhase:newPhase,
        })  
        return true
    }

    emit(event: FastWarGameEvent, data:any) {
        print("FastWar触发："+event+"事件！")
        for (const h of this.gameEventHandlers) {
            h.handleMessage(event,data)
        }
    }

    
    startDefaultGame () {
        let player1 = PlayerResource.GetPlayer(GameRules.PlayerIDs[0])
        let player2 = PlayerResource.GetPlayer(GameRules.PlayerIDs[1])
        this.GameTeamGroup[player1.GetTeam()] = {
            enemyPlayer:player2.GetPlayerID(),
            enemyTeamNumber:player2.GetTeam(),
            enemyIsGoodGuy:true,
            enemyTower:[1,1,1]
        }
        this.GameTeamGroup[player2.GetTeam()] = {
            enemyPlayer:player1.GetPlayerID(),
            enemyTeamNumber:player1.GetTeam(),
            enemyIsGoodGuy:false,
            enemyTower:[1,1,1]
        }
        
        GameRules.NPCUtils.clearUnits()  
        const config = {
            prepareDuration: 120,
            mainDuration: 180,
            overTimeDuration: 120
        };
        // 启动游戏
        let controller = this
        controller.start(new Map([
            [FastWarGamePhase.Preparing, new DefaultPreparePhaseHandler(controller, config, player1, player2)],
            [FastWarGamePhase.MainTime, new DefaultMainPhaseHandler(controller, config, player1, player2)],
            [FastWarGamePhase.Overtime, new DefaultOvertimePhaseHandler(controller, config, player1, player2)],
            [FastWarGamePhase.TempEnd, new DefaultEndPhaseHandler(controller, config, player1, player2)],
        ]), [
            new TowerDestroyEventHandle(controller,FastWarGameEvent.TowerDestroyed)
        ],FastWarGamePhase.Preparing, [GameRules.PlayerIDs[0],GameRules.PlayerIDs[1]]);
    
    }

    startTestGame (p1:PlayerID, p2:PlayerID){
        GameRules.FastWarPlayerHeroSpell.clearPlayerSpell()
        GameRules.FastWarCard.clearCard()
        GameRules.NPCUtils.clearUnits()
        GameRules.FastWarSpell.clearTimer()
        
        let player1 = PlayerResource.GetPlayer(p1)
        let player2 = PlayerResource.GetPlayer(p2)
        this.GameTeamGroup[player1.GetTeam()] = {
            enemyPlayer:p2,
            enemyTeamNumber:player2.GetTeam(),
            enemyIsGoodGuy:player2.GetTeam()==2,
            enemyTower:[1,1,1]
        }
        this.GameTeamGroup[player2.GetTeam()] = {
            enemyPlayer:p1,
            enemyTeamNumber:player1.GetTeam(),
            enemyIsGoodGuy:player1.GetTeam()==2,
            enemyTower:[1,1,1]
        }
        PlayerResource.SetUnitShareMaskForPlayer(p1,p2,1,true)
        PlayerResource.SetUnitShareMaskForPlayer(p1,p2,2,true)
        PlayerResource.SetUnitShareMaskForPlayer(p2,p1,1,true)
        PlayerResource.SetUnitShareMaskForPlayer(p2,p1,2,true)

        let p1Hero = GameRules.FastWarPlayerHeroSpell.getPlayerDHero(p1 as PlayerID)
        let p2Hero = GameRules.FastWarPlayerHeroSpell.getPlayerDHero(p2 as PlayerID)
        CustomGameEventManager.Send_ServerToPlayer(player1, "go_game_process_change",{type:1,p1:p1,p1Hero:p1Hero,p2:p2,p2Hero:p2Hero, isGood:player1.GetTeam()==2})
        CustomGameEventManager.Send_ServerToPlayer(player2, "go_game_process_change",{type:1,p1:p2,p1Hero:p2Hero,p2:p1,p2Hero:p1Hero, isGood:player2.GetTeam()==2})
        GameRules.FastWarCard.completionDeck([p1,p2],false,0)


        const config = {
            prepareDuration: 5,
            mainDuration: 180,
            overTimeDuration: 120
        };
        // 启动游戏
        let controller = this
        controller.start(new Map([
            [FastWarGamePhase.Preparing, new DefaultPreparePhaseHandler(controller, config, player1, player2)],
            [FastWarGamePhase.MainTime, new DefaultMainPhaseHandler(controller, config, player1, player2)],
            [FastWarGamePhase.Overtime, new DefaultOvertimePhaseHandler(controller, config, player1, player2)],
            [FastWarGamePhase.TempEnd, new DefaultEndPhaseHandler(controller, config, player1, player2)],
        ]), [
            new TowerDestroyEventHandle(controller,FastWarGameEvent.TowerDestroyed)
        ],FastWarGamePhase.Preparing, [p1,p2]);
    }
}
