declare interface CustomNetTableDeclarations {
    keys:{
        keys:{
            [key:string]: string
        }
    }
    
    /**
     *  当前玩家技能
     *  库存可替换玩家技能（用于准备阶段组建卡组）
     */
    player_hero:{
        player_hero:{
            changedPlayer:number,
            deck:{
                [key: string]: string
            },
            reserve:{
                [key: string]: {
                    [key:number]:string
                }
            },
        },
    }
    /**
     *  当前套牌（完整套牌）
     *  库存可替换卡牌（用于准备阶段组建卡组）
     */
    deck_cards:{
        deck_cards:{
            changedPlayer:number,
            deck:{
                [key: string]: {
                    [key:number]:GoFastWarCardClient
                }
            },
            reserve:{
                [key: string]: {
                    [key:number]:GoFastWarCardClient
                }
            },
        },
    }
    /**
     *  当前手牌以及下一次抽牌（用于对局，当前牌库剩余卡牌仅在后台保存）
     */
    hand_cards:{
        hand_cards:{
            changedPlayer:number,
            data:{
                [key: string]: {
                    [key:number]:GoFastWarCardClient
                }
            }
            heroSpell:{
                [key: string]: {
                    heroInfo:PlayerHeroForClient,
                    cardInfo?:GoFastWarCardClient
                }
            }
        },
    }
    /**
     *  每轮对局使用过的卡牌（用于PVP的UI提示）
     */
    using_cards:{
        using_cards:{
            [key: string]: string
        }
    } 
    /**
     *  卡牌施放所需的能量点数
     */
    card_mana:{
        card_mana:{
            [key: string]: {
                time:number,
                speed:number,
                mana:number,
                maxMana:number,
                hasUp:number,//用于解决增长停滞后恢复增长的问题
            }
        }
    }  

    game_player_info : {
        player:{
            [key:string]:{
                isPlayer:number,
                uiPID:number,
                enemyUIPID:number,
                uiPIsGood:0|1,
                uiPHero:EntityIndex,
            }
        }
    }

    fast_war_game_phase:{
        fast_war_game_mode:{
            nowGameGroup:GoFastWarGameModeEnum,
            nowGameMode:number,
            group:GoFastWarGameModeEnum,
            mode:number,
        },
        fast_war_game_phase:GoFastWarGamePhaseTime,
        fast_war_game_tower:{//取的是敌方playerID
            [key: string]: {
                tower1:number,
                tower2:number,
                tower3:number,
            }
        }
    }


}
