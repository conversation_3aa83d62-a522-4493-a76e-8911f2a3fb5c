/** @noSelfInFile */
declare function PopupHealing(target: CDOTA_BaseNPC, amount: number, player?: CDOTAPlayerController): void;
declare function PopupDamage(target: CDOTA_BaseNPC, amount: number, player?: CDOTAPlayerController): void;
declare function PopupDamageColored(target: CDOTA_BaseNPC, amount: number, color: Vector, player?: CDOTAPlayerController): void;
declare function PopupCriticalDamage(target: CDOTA_BaseNPC, amount: number, player?: CDOTAPlayerController): void;
declare function PopupCriticalDamageColored(target: CDOTA_BaseNPC, amount: number, color: Vector, player?: CDOTAPlayerController): void;
declare function PopupDamageOverTime(target: CDOTA_BaseNPC, amount: number, player?: CDOTAPlayerController): void;
declare function PopupDamageBlock(target: CDOTA_BaseNPC, amount: number, player?: CDOTAPlayerController): void;
declare function PopupGoldGain(target: CDOTA_BaseNPC, amount: number, player?: CDOTAPlayerController): void;
declare function PopupManaGain(target: CDOTA_BaseNPC, amount: number, player?: CDOTAPlayerController): void;
declare function PopupMiss(target: CDOTA_BaseNPC, player?: CDOTAPlayerController): void;
declare function PopupDamageBig(target: CDOTA_BaseNPC, amount: number, player?: CDOTAPlayerController): void;
declare function PopupAddGold(target: CDOTA_BaseNPC, amount: number, player?: CDOTAPlayerController): void;
