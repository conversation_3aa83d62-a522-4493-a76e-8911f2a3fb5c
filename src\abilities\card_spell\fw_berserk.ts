import { modifier_fw_berserk } from "../../modifiers/abilities/card_spell/modifier_fw_berserk";
import { modifier_fw_berserk_debuff } from "../../modifiers/abilities/card_spell/modifier_fw_berserk_debuff";
import { modifier_fw_freezing_field_debuff } from "../../modifiers/abilities/card_spell/modifier_fw_freezing_field_debuff";
import { BaseAbility, registerAbility } from "../../utils/dota_ts_adapter";
import { MathUtils } from "../../utils/math_utils";

export class fw_berserk extends BaseAbility {
    
    spell:SpellInfoKV;
    GetAOERadius(): number {
        return this.spell.AOERadius
    }

    OnSpellStart(): void {
        let duration = this.GetSpecialValueFor("duration")

        let hero = this.GetCaster()
        let ab = this
        let team = hero.GetTeam()
        let tarPos = this.GetCursorPosition()
        let radius = this.GetAOERadius()
        let tarTeam = this.GetAbilityTargetTeam()
        let tarFlag = this.GetAbilityTargetFlags()
        let fwTargetType = this.spell.FWTargetType
        let e = GameRules.SoundUtils.getSoundEntity(tarPos)
        
        let par = ParticleManager.CreateParticle("particles/spell/fw_berserk/berserk.vpcf", ParticleAttachment_t.PATTACH_WORLDORIGIN, undefined)
        ParticleManager.SetParticleControl(par, 0, tarPos)
        ParticleManager.SetParticleControl(par, 1, Vector(radius,duration,0))

        GameRules.FastWarSpell.startIntervalSpell(0,duration,0.3,
        ()=>{
            e.EmitSound("Fw.Cards.Spell.berserk.cast")
        },()=>{
            let tars = FindUnitsInRadius(
                team,
                tarPos,
                undefined,
                radius,
                tarTeam,
                1 + 2 + 4,
                tarFlag,
                0,
                false,
            )
            for (const unit of tars) {
                if (unit.fastWarUnitType != undefined && (fwTargetType == GoFastWarAIUnitTypeEnum.ALL || unit.fastWarUnitType == fwTargetType)) {
                    unit.AddNewModifier(hero, ab, modifier_fw_berserk.name, {duration:0.4}) 
                    if (!unit.HasModifier(modifier_fw_berserk_debuff.name)) {
                        unit.AddNewModifier(hero, ab, modifier_fw_berserk_debuff.name, {}) 
                    }
                }
            }
        },()=>{
            e.StopSound("Fw.Cards.Spell.berserk.cast")
            GameRules.SoundUtils.backSoundEntity(e)
            ParticleManager.DestroyParticle(par, false)
        })
    }

    Spawn(): void {
        this.spell = GameRules.KVUtils.getSpellInfo("fw_berserk")
    }
    
    Precache(context: CScriptPrecacheContext): void {
        PrecacheResource("particle","particles/spell/fw_berserk/berserk.vpcf",context)
        PrecacheResource("particle","particles/status_fx/status_effect_bloodrage.vpcf",context)
    }
}

