// Fast War Card Game - Simple Panorama JavaScript Version
// 不使用 React，直接使用 Panorama API

console.log('=== Fast War Simple JS Loading ===');

// 游戏状态
var gameState = {
    currentTurn: 1,
    playerMana: 10,
    playerHealth: 30,
    handCards: [
        {
            Name: 'test_unit_1',
            ChName: '测试单位',
            LayCost: 3,
            CardType: 'unit',
            CardUIType: 'creep'
        },
        {
            Name: 'test_spell_1',
            ChName: '测试法术',
            LayCost: 5,
            CardType: 'spell',
            CardUIType: 'spell'
        },
        {
            Name: 'test_hero_1',
            ChName: '测试英雄',
            LayCost: 7,
            CardType: 'unit',
            CardUIType: 'hero'
        }
    ],
    selectedCard: null
};

// 创建UI元素
function createGameUI() {
    console.log('Creating game UI...');
    
    var rootPanel = $.GetContextPanel();
    var fastWarApp = rootPanel.FindChildInLayoutFile('FastWarApp');
    
    if (!fastWarApp) {
        console.log('FastWarApp panel not found, creating...');
        fastWarApp = $.CreatePanel('Panel', rootPanel, 'FastWarApp');
    }
    
    // 清空现有内容
    fastWarApp.RemoveAndDeleteChildren();
    
    // 设置基本样式
    fastWarApp.style.width = '100%';
    fastWarApp.style.height = '100%';
    fastWarApp.style.backgroundColor = '#1a1a2e';
    
    // 创建游戏信息栏
    createGameInfo(fastWarApp);
    
    // 创建战场区域
    createBattlefield(fastWarApp);
    
    // 创建手牌区域
    createHandArea(fastWarApp);
    
    console.log('Game UI created successfully');
}

// 创建游戏信息栏
function createGameInfo(parent) {
    var gameInfo = $.CreatePanel('Panel', parent, 'GameInfo');
    gameInfo.style.width = '300px';
    gameInfo.style.height = '80px';
    gameInfo.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
    gameInfo.style.border = '2px solid #4a90e2';
    gameInfo.style.borderRadius = '10px';
    gameInfo.style.padding = '15px';
    gameInfo.style.margin = '20px';
    
    // 回合信息
    var turnLabel = $.CreatePanel('Label', gameInfo, 'TurnLabel');
    turnLabel.text = '回合: ' + gameState.currentTurn;
    turnLabel.style.color = '#ffffff';
    turnLabel.style.fontSize = '16px';
    turnLabel.style.marginBottom = '5px';
    
    // 法力值
    var manaLabel = $.CreatePanel('Label', gameInfo, 'ManaLabel');
    manaLabel.text = '法力: ' + gameState.playerMana;
    manaLabel.style.color = '#4a90e2';
    manaLabel.style.fontSize = '16px';
    manaLabel.style.marginBottom = '5px';
    
    // 生命值
    var healthLabel = $.CreatePanel('Label', gameInfo, 'HealthLabel');
    healthLabel.text = '生命: ' + gameState.playerHealth;
    healthLabel.style.color = '#e74c3c';
    healthLabel.style.fontSize = '16px';
}

// 创建战场区域
function createBattlefield(parent) {
    var battlefield = $.CreatePanel('Panel', parent, 'Battlefield');
    battlefield.style.width = '600px';
    battlefield.style.height = '300px';
    battlefield.style.backgroundColor = 'rgba(0, 0, 0, 0.3)';
    battlefield.style.border = '2px solid #666666';
    battlefield.style.borderRadius = '15px';
    battlefield.style.margin = '200px auto';
    
    var battlefieldLabel = $.CreatePanel('Label', battlefield, 'BattlefieldLabel');
    battlefieldLabel.text = '战场区域\n拖拽卡牌到此处使用';
    battlefieldLabel.style.color = '#888888';
    battlefieldLabel.style.fontSize = '18px';
    battlefieldLabel.style.textAlign = 'center';
    battlefieldLabel.style.width = '100%';
    battlefieldLabel.style.height = '100%';
    battlefieldLabel.style.verticalAlign = 'middle';
    battlefieldLabel.style.paddingTop = '120px';
}

// 创建手牌区域
function createHandArea(parent) {
    var handArea = $.CreatePanel('Panel', parent, 'HandArea');
    handArea.style.width = '800px';
    handArea.style.height = '250px';
    handArea.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
    handArea.style.border = '2px solid #4a90e2';
    handArea.style.borderRadius = '15px';
    handArea.style.padding = '15px';
    handArea.style.margin = '20px auto';
    
    var handTitle = $.CreatePanel('Label', handArea, 'HandTitle');
    handTitle.text = '手牌 (' + gameState.handCards.length + ')';
    handTitle.style.color = '#ffffff';
    handTitle.style.fontSize = '16px';
    handTitle.style.marginBottom = '10px';
    
    var handCards = $.CreatePanel('Panel', handArea, 'HandCards');
    handCards.style.width = '100%';
    handCards.style.height = '200px';
    handCards.style.flowChildren = 'right';
    handCards.style.marginTop = '10px';
    
    // 创建卡牌
    for (var i = 0; i < gameState.handCards.length; i++) {
        createCard(handCards, gameState.handCards[i], i);
    }
}

// 创建单张卡牌
function createCard(parent, cardData, index) {
    var card = $.CreatePanel('Panel', parent, 'Card_' + index);
    card.style.width = '150px';
    card.style.height = '200px';
    card.style.backgroundColor = getCardBackgroundColor(cardData.CardUIType);
    card.style.border = '2px solid #4a90e2';
    card.style.borderRadius = '10px';
    card.style.padding = '10px';
    card.style.marginRight = '10px';
    
    // 卡牌头部
    var cardHeader = $.CreatePanel('Panel', card, 'CardHeader_' + index);
    cardHeader.style.width = '100%';
    cardHeader.style.height = '30px';
    cardHeader.style.marginBottom = '10px';
    cardHeader.style.flowChildren = 'right';
    
    // 费用
    var costLabel = $.CreatePanel('Label', cardHeader, 'Cost_' + index);
    costLabel.text = cardData.LayCost.toString();
    costLabel.style.backgroundColor = '#e74c3c';
    costLabel.style.color = 'white';
    costLabel.style.borderRadius = '50%';
    costLabel.style.width = '25px';
    costLabel.style.height = '25px';
    costLabel.style.textAlign = 'center';
    costLabel.style.fontSize = '12px';
    costLabel.style.lineHeight = '25px';
    
    // 类型图标
    var typeIcon = $.CreatePanel('Label', cardHeader, 'TypeIcon_' + index);
    typeIcon.text = getCardTypeIcon(cardData.CardUIType);
    typeIcon.style.fontSize = '16px';
    typeIcon.style.marginLeft = '80px';
    typeIcon.style.lineHeight = '25px';
    
    // 卡牌图片区域
    var cardImage = $.CreatePanel('Panel', card, 'CardImage_' + index);
    cardImage.style.width = '100%';
    cardImage.style.height = '60px';
    cardImage.style.backgroundColor = 'rgba(0, 0, 0, 0.3)';
    cardImage.style.borderRadius = '5px';
    cardImage.style.marginBottom = '10px';
    cardImage.style.textAlign = 'center';
    
    var placeholderImage = $.CreatePanel('Label', cardImage, 'PlaceholderImage_' + index);
    placeholderImage.text = '🎴';
    placeholderImage.style.fontSize = '24px';
    placeholderImage.style.opacity = '0.7';
    placeholderImage.style.lineHeight = '60px';
    
    // 卡牌名称
    var cardName = $.CreatePanel('Label', card, 'CardName_' + index);
    cardName.text = cardData.ChName;
    cardName.style.fontSize = '14px';
    cardName.style.fontWeight = 'bold';
    cardName.style.marginBottom = '5px';
    cardName.style.color = '#ffffff';
    
    // 卡牌描述
    var cardDesc = $.CreatePanel('Label', card, 'CardDesc_' + index);
    cardDesc.text = '这是一张' + cardData.ChName + '卡牌';
    cardDesc.style.fontSize = '11px';
    cardDesc.style.color = '#cccccc';
    
    // 添加点击事件
    card.SetPanelEvent('onactivate', function() {
        selectCard(index);
    });
    
    // 添加悬停事件
    card.SetPanelEvent('onmouseover', function() {
        card.style.backgroundColor = '#34495e';
        card.style.borderColor = '#5aa3f0';
    });
    
    card.SetPanelEvent('onmouseout', function() {
        card.style.backgroundColor = getCardBackgroundColor(cardData.CardUIType);
        card.style.borderColor = gameState.selectedCard === index ? '#f39c12' : '#4a90e2';
    });
}

// 获取卡牌背景颜色
function getCardBackgroundColor(cardType) {
    switch (cardType) {
        case 'creep':
        case 'unit':
            return '#2c5530';
        case 'spell':
            return '#4a2c50';
        case 'hero':
            return '#5a4a2c';
        case 'building':
            return '#4a3c2c';
        default:
            return '#2c3e50';
    }
}

// 获取卡牌类型图标
function getCardTypeIcon(cardType) {
    switch (cardType) {
        case 'creep':
        case 'unit':
            return '⚔️';
        case 'spell':
            return '✨';
        case 'hero':
            return '👑';
        case 'building':
            return '🏰';
        default:
            return '❓';
    }
}

// 选择卡牌
function selectCard(index) {
    console.log('Selected card:', index, gameState.handCards[index]);
    gameState.selectedCard = index;
    
    // 更新所有卡牌的选中状态
    for (var i = 0; i < gameState.handCards.length; i++) {
        var cardPanel = $.GetContextPanel().FindChildInLayoutFile('Card_' + i);
        if (cardPanel) {
            if (i === index) {
                cardPanel.style.borderColor = '#f39c12';
                cardPanel.style.backgroundColor = '#3e2723';
            } else {
                cardPanel.style.borderColor = '#4a90e2';
                cardPanel.style.backgroundColor = getCardBackgroundColor(gameState.handCards[i].CardUIType);
            }
        }
    }
}

// 初始化函数
function initializeFastWarSimple() {
    console.log('=== Fast War Simple Initialization Started ===');
    
    // 延迟创建UI，确保面板已经加载
    $.Schedule(0.1, function() {
        createGameUI();
        console.log('=== Fast War Simple UI initialized successfully ===');
    });
}

// 当脚本加载时初始化
(function() {
    if ($.GetContextPanel()) {
        initializeFastWarSimple();
    } else {
        $.Schedule(0.1, initializeFastWarSimple);
    }
})();
