import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";
import { modifier_fw_techies_bomb_motion } from "./modifier_fw_techies_bomb_motion";
import { modifier_fw_zombie_extra_debuff } from "./modifier_fw_zombie_extra_debuff";


export class modifier_fw_techies_bomb_adsorb extends BaseModifier {
    
    IsHidden() {
        return true;
    }

    IsPurgable(): boolean {
        return false
    }

    IsPurgeException(): boolean {
        return false
    }
    
    CheckState() {
        return {
            [8]: true,
        }   
    }

    OnCreated(params: object): void {
        if (IsServer()) {
            this.StartIntervalThink(0.2)
        }
    }

    OnIntervalThink(): void {
        let hero = this.GetParent()
        let team = hero.GetTeam()
        let ab = this.GetAbility()
        let range = hero.GetAcquisitionRange()
        let tarTeam = ab.GetAbilityTargetTeam()
        let tarFlag = ab.GetAbilityTargetFlags()
        let spellInfo = GameRules.KVUtils.getSpellInfo(ab.GetAbilityName())
        let tars = FindUnitsInRadius(
            team,
            hero.GetAbsOrigin(),
            undefined,
            range,
            tarTeam,
            1 + 2 + 4,
            tarFlag,
            1,
            false,
        )
        if (tars.length > 0) {
            this.GetParent().StartGestureWithFadeAndPlaybackRate(GameActivity_t.ACT_DOTA_ATTACK, 0.2,0.2, 3)
            this.GetParent().AddNewModifier(this.GetParent(), this.GetAbility(), modifier_fw_techies_bomb_motion.name, {duration:2, tarEnt:tars[0].entindex()})
            this.Destroy()
        }
    }

    OnAttackLanded(event: ModifierAttackEvent): void {
        if (event.target.entindex() == this.GetParent().entindex()) {
            this.GetParent().StartGestureWithFadeAndPlaybackRate(GameActivity_t.ACT_DOTA_ATTACK, 0.2,0.2, 3)
            this.GetParent().AddNewModifier(this.GetParent(), this.GetAbility(), modifier_fw_techies_bomb_motion.name, {duration:2, tarEnt:event.attacker.entindex()})
            this.Destroy()
        }
    }

    OnDeath(event: ModifierInstanceEvent): void {
        if (event.unit.entindex() == this.GetParent().entindex() && !this.GetParent().HasModifier(modifier_fw_techies_bomb_motion.name)) {
            let hero = this.GetParent()
            let ab = this.GetAbility()
            let damage = ab.GetSpecialValueFor("damage")
            let range = ab.GetSpecialValueFor("range")

            let team = hero.GetTeam()
            let tarTeam = ab.GetAbilityTargetTeam()
            let tarFlag = ab.GetAbilityTargetFlags()
            let damageType = ab.GetAbilityDamageType()
            let spellInfo = GameRules.KVUtils.getSpellInfo(ab.GetAbilityName())
            let fwTargetType = spellInfo.FWTargetType
            let tars = FindUnitsInRadius(
                team,
                hero.GetAbsOrigin(),
                undefined,
                range,
                tarTeam,
                1 + 2 + 4,
                tarFlag,
                0,
                false,
            )
            if (tars.length > 0) {
                for (const unit of tars) {
                    if (unit.fastWarUnitType != undefined && (fwTargetType == GoFastWarAIUnitTypeEnum.ALL || unit.fastWarUnitType == fwTargetType)) {
                        ApplyDamage({
                            victim: unit,
                            attacker: hero,
                            damage: damage,
                            damage_type: damageType,
                            damage_flags:DOTADamageFlag_t.DOTA_DAMAGE_FLAG_NONE,
                            ability:ab,
                        });
                    }
                }
            }
            let par = ParticleManager.CreateParticle("particles/units/heroes/hero_techies/techies_remote_cart_explode.vpcf", ParticleAttachment_t.PATTACH_WORLDORIGIN, undefined)
            ParticleManager.SetParticleControl(par,0,hero.GetAbsOrigin())
            ParticleManager.SetParticleControl(par,1,Vector(0,0,range))
            hero.AddNoDraw()
        }
    }

    GetModifierIncomingPhysicalDamageConstant(event: ModifierAttackEvent): number {
        return -event.damage
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            34,
            21,
            35,
        ];
    }

    RemoveOnDeath(): boolean {
        return true
    }
    
}
