{"radiant_creep_default_melee": {"Template": "creep_default_melee", "BaseClass": "npc_dota_creature", "Model": "models/creeps/lane_creeps/creep_radiant_melee/radiant_melee.vmdl", "SoundSet": "<PERSON><PERSON><PERSON>_Bad_Melee", "ModelScale": 0.93, "Level": 2, "ArmorPhysical": 2, "AttackAnimationPoint": 0.467, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackDamageMax": 55, "AttackDamageMin": 45, "AttackRange": 100, "AttackRate": 1.2, "BaseAttackSpeed": 100, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 45, "StatusHealth": 100, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": 0, "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "dire_creep_default_melee": {"Template": "creep_default_melee", "BaseClass": "npc_dota_creature", "Model": "models/creeps/lane_creeps/creep_bad_melee/creep_bad_melee.vmdl", "SoundSet": "<PERSON><PERSON><PERSON>_Bad_Melee", "ModelScale": 0.93, "Level": 2, "ArmorPhysical": 2, "AttackAnimationPoint": 0.467, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackDamageMax": 55, "AttackDamageMin": 45, "AttackRange": 100, "AttackRate": 1.2, "BaseAttackSpeed": 100, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 45, "StatusHealth": 100, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": 0, "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "radiant_creep_default_ranged": {"Template": "creep_default_ranged", "BaseClass": "npc_dota_creature", "Model": "models/creeps/lane_creeps/creep_radiant_ranged/radiant_ranged.vmdl", "SoundSet": "Creep_Bad_Range", "ModelScale": 0.93, "Level": 2, "ArmorPhysical": 2, "AttackAnimationPoint": 0.5, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackDamageMax": 48, "AttackDamageMin": 38, "AttackRange": 500, "AttackRate": 1.2, "BaseAttackSpeed": 100, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "ProjectileModel": "particles/base_attacks/ranged_goodguy.vpcf", "ProjectileSpeed": 900, "RingRadius": 45, "StatusHealth": 80, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": 0, "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "dire_creep_default_ranged": {"Template": "creep_default_ranged", "BaseClass": "npc_dota_creature", "Model": "models/creeps/lane_creeps/creep_bad_ranged/lane_dire_ranged.vmdl", "SoundSet": "Creep_Bad_Range", "ModelScale": 0.93, "Level": 2, "ArmorPhysical": 2, "AttackAnimationPoint": 0.5, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackDamageMax": 48, "AttackDamageMin": 38, "AttackRange": 500, "AttackRate": 1.2, "BaseAttackSpeed": 100, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "ProjectileModel": "particles/base_attacks/ranged_badguy.vpcf", "ProjectileSpeed": 900, "RingRadius": 45, "StatusHealth": 80, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": 0, "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "radiant_creep_default_siege": {"Template": "creep_default_siege", "BaseClass": "npc_dota_creature", "Model": "models/creeps/lane_creeps/creep_good_siege/creep_good_siege.vmdl", "SoundSet": "Creep_Bad_Engine", "ModelScale": 0.93, "Level": 4, "ArmorPhysical": 1, "AttackAnimationPoint": 0.7, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackDamageMax": 110, "AttackDamageMin": 90, "AttackRange": 700, "AttackRate": 2, "BaseAttackSpeed": 100, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 50, "MinimapIcon": "minimap_siege", "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 230, "MovementTurnRate": 1, "ProjectileModel": "particles/base_attacks/ranged_siege_good.vpcf", "ProjectileSpeed": 1100, "RingRadius": 100, "StatusHealth": 800, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1100, "VisionNighttimeRange": 1100, "Ability1": "creep_siege", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "AttackAcquisitionRange": 1100, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "dire_creep_default_siege": {"Template": "creep_default_siege", "BaseClass": "npc_dota_creature", "Model": "models/creeps/lane_creeps/creep_bad_siege/creep_bad_siege.vmdl", "SoundSet": "Creep_Bad_Engine", "ModelScale": 0.93, "Level": 4, "ArmorPhysical": 1, "AttackAnimationPoint": 0.7, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackDamageMax": 110, "AttackDamageMin": 90, "AttackRange": 700, "AttackRate": 2, "BaseAttackSpeed": 100, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 50, "MinimapIcon": "minimap_siege", "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 230, "MovementTurnRate": 1, "ProjectileModel": "particles/base_attacks/ranged_siege_bad.vpcf", "ProjectileSpeed": 1100, "RingRadius": 100, "StatusHealth": 800, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 1100, "VisionNighttimeRange": 1100, "Ability1": "creep_siege", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "AttackAcquisitionRange": 1100, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "radiant_tower_default": {"Template": "tower_default", "BaseClass": "npc_dota_tower", "Model": "models/props_structures/tower_good.vmdl", "SoundSet": "Tower.Water", "ModelScale": 1, "Level": 5, "ArmorPhysical": 4, "AttackAnimationPoint": 0.6, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackDamageMax": 100, "AttackDamageMin": 90, "AttackRange": 800, "AttackRate": 1.4, "BaseAttackSpeed": 120, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 260, "MagicalResistance": 50, "MinimapIcon": "minimap_tower90", "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_NONE", "MovementSpeed": 100, "MovementTurnRate": 1, "ProjectileModel": "particles/base_attacks/ranged_tower_good.vpcf", "ProjectileSpeed": 1100, "RingRadius": 120, "StatusHealth": 1500, "StatusHealthRegen": 0, "StatusMana": 0, "StatusManaRegen": 0, "VisionDaytimeRange": 1500, "VisionNighttimeRange": 1500, "Ability1": "creep_siege", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "AttackAcquisitionRange": 1500, "BoundsHullName": "DOTA_HULL_SIZE_TOWER"}, "dire_tower_default": {"Template": "tower_default", "BaseClass": "npc_dota_tower", "Model": "models/props_structures/tower_bad.vmdl", "SoundSet": "Tower.Water", "ModelScale": 1, "Level": 5, "ArmorPhysical": 4, "AttackAnimationPoint": 0.6, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackDamageMax": 100, "AttackDamageMin": 90, "AttackRange": 800, "AttackRate": 1.4, "BaseAttackSpeed": 120, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 260, "MagicalResistance": 50, "MinimapIcon": "minimap_tower90", "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_NONE", "MovementSpeed": 100, "MovementTurnRate": 1, "ProjectileModel": "particles/base_attacks/ranged_tower_bad.vpcf", "ProjectileSpeed": 1100, "RingRadius": 120, "StatusHealth": 1500, "StatusHealthRegen": 0, "StatusMana": 0, "StatusManaRegen": 0, "VisionDaytimeRange": 1500, "VisionNighttimeRange": 1500, "Ability1": "creep_siege", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "AttackAcquisitionRange": 1500, "BoundsHullName": "DOTA_HULL_SIZE_TOWER"}, "radiant_tower_default_aciant": {"Template": "tower_default_aciant", "BaseClass": "npc_dota_tower", "Model": "models/props_structures/radiant_ancient001.vmdl", "SoundSet": "<PERSON><PERSON><PERSON>_<PERSON>_Melee", "ModelScale": 1, "Level": 10, "ArmorPhysical": 6, "AttackAnimationPoint": 0.6, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackDamageMax": 120, "AttackDamageMin": 100, "AttackRange": 800, "AttackRate": 1.4, "BaseAttackSpeed": 140, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 260, "MagicalResistance": 50, "MinimapIcon": "minimap_ancient", "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_NONE", "MovementSpeed": 100, "MovementTurnRate": 1, "ProjectileModel": "particles/base_attacks/ranged_tower_good.vpcf", "ProjectileSpeed": 1100, "RingRadius": 350, "StatusHealth": 2000, "StatusHealthRegen": 0, "StatusMana": 0, "StatusManaRegen": 0, "VisionDaytimeRange": 1600, "VisionNighttimeRange": 1600, "Ability1": "creep_siege", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "AttackAcquisitionRange": 1600, "BoundsHullName": "DOTA_HULL_SIZE_BUILDING"}, "dire_tower_default_aciant": {"Template": "tower_default_aciant", "BaseClass": "npc_dota_tower", "Model": "models/props_structures/bad_ancient002.vmdl", "SoundSet": "<PERSON><PERSON><PERSON>_<PERSON>_Melee", "ModelScale": 1, "Level": 10, "ArmorPhysical": 6, "AttackAnimationPoint": 0.6, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackDamageMax": 120, "AttackDamageMin": 100, "AttackRange": 800, "AttackRate": 1.4, "BaseAttackSpeed": 140, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 260, "MagicalResistance": 50, "MinimapIcon": "minimap_ancient", "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_NONE", "MovementSpeed": 100, "MovementTurnRate": 1, "ProjectileModel": "particles/base_attacks/ranged_tower_bad.vpcf", "ProjectileSpeed": 1100, "RingRadius": 350, "StatusHealth": 2000, "StatusHealthRegen": 0, "StatusMana": 0, "StatusManaRegen": 0, "VisionDaytimeRange": 1600, "VisionNighttimeRange": 1600, "Ability1": "creep_siege", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {}}, "AttackAcquisitionRange": 1600, "BoundsHullName": "DOTA_HULL_SIZE_BUILDING"}, "base_creep_default_axe": {"Template": "creep_default_axe", "BaseClass": "npc_dota_creature", "Model": "models/heroes/axe/axe.vmdl", "SoundSet": "Hero_Axe", "ModelScale": 1.1, "Level": 4, "ArmorPhysical": 4, "AttackAnimationPoint": 0.4, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackDamageMax": 70, "AttackDamageMin": 60, "AttackRange": 150, "AttackRate": 1.7, "BaseAttackSpeed": 120, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 1, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 270, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 500, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": "axe_counter_helix", "Ability2": "fw_unit_str_hero_health", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 2}, "2": {"ItemDef": 3}, "3": {"ItemDef": 4}, "4": {"ItemDef": 5}, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_axe": {"Template": "creep_default_axe", "BaseClass": "npc_dota_creature", "Model": "models/heroes/axe/axe.vmdl", "SoundSet": "Hero_Axe", "ModelScale": 1.1, "Level": 4, "ArmorPhysical": 4, "AttackAnimationPoint": 0.4, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackDamageMax": 70, "AttackDamageMin": 60, "AttackRange": 150, "AttackRate": 1.7, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 1, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 270, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 500, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": "axe_counter_helix", "Ability2": "fw_unit_str_hero_health", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 18545}, "2": {"ItemDef": 18546}, "3": {"ItemDef": 18547}, "4": {"ItemDef": 18548}, "5": {"ItemDef": 18549}, "6": {"ItemDef": 18550}, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_juggernaut": {"Template": "creep_default_juggernaut", "BaseClass": "npc_dota_creature", "Model": "models/heroes/juggernaut/juggernaut.vmdl", "SoundSet": "<PERSON><PERSON><PERSON><PERSON>naut", "ModelScale": 1.1, "Level": 3, "ArmorPhysical": 3, "AttackAnimationPoint": 0.33, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackDamageMax": 75, "AttackDamageMin": 65, "AttackRange": 150, "AttackRate": 1.4, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 1, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 250, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": "juggernaut_blade_dance", "Ability2": "fw_unit_int_hero", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 6}, "2": {"ItemDef": 7}, "3": {"ItemDef": 8}, "4": {"ItemDef": 9}, "5": {"ItemDef": 62}, "6": {"ItemDef": 811}, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "AttackSpeedActivityModifiers": "run"}, "default_creep_granite_golem": {"Template": "creep_granite_golem", "BaseClass": "npc_dota_creature", "Model": "models/creeps/neutral_creeps/n_creep_golem_a/neutral_creep_golem_a.vmdl", "SoundSet": "n_creep_<PERSON>ee", "ModelScale": 2, "Level": 5, "ArmorPhysical": 5, "AttackAnimationPoint": 0.3, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackDamageMax": 155, "AttackDamageMin": 145, "AttackRange": 150, "AttackRate": 2, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 320, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 200, "MovementTurnRate": 1, "RingRadius": 90, "StatusHealth": 1000, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": 0, "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_undyingt_tombstone": {"Template": "creep_undyingt_tombstone", "BaseClass": "npc_dota_creature", "Model": "models/heroes/undying/undying_tower.vmdl", "SoundSet": "Undying_Tombstone", "ModelScale": 0.93, "Level": 4, "ArmorPhysical": 0, "AttackAnimationPoint": 0.3, "AttackCapabilities": "DOTA_UNIT_CAP_NO_ATTACK", "AttackDamageMax": 0, "AttackDamageMin": 0, "AttackRange": 0, "AttackRate": 0, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 260, "MagicalResistance": 0, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_NONE", "MovementSpeed": 100, "MovementTurnRate": 1, "RingRadius": 45, "StatusHealth": 300, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": "fw_summon_zombie", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_undying_zombie": {"Template": "creep_default_undying_zombie", "BaseClass": "npc_dota_creature", "Model": "models/heroes/undying/undying_minion.vmdl", "SoundSet": "Undying_Zombie", "ModelScale": 0.93, "Level": 1, "ArmorPhysical": 1, "AttackAnimationPoint": 0.3, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackDamageMax": 40, "AttackDamageMin": 30, "AttackRange": 100, "AttackRate": 1.6, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 45, "StatusHealth": 30, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": 0, "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_flesh_golem": {"Template": "creep_default_flesh_golem", "BaseClass": "npc_dota_creature", "Model": "models/heroes/undying/undying_flesh_golem.vmdl", "SoundSet": "Hero_Undying", "ModelScale": 1.2, "Level": 1, "ArmorPhysical": 1.5, "AttackAnimationPoint": 0.3, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackDamageMax": 60, "AttackDamageMin": 50, "AttackRange": 100, "AttackRate": 1.7, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 260, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 300, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": "fw_zombie_extra", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_abaddon": {"Template": "creep_default_abaddon", "BaseClass": "npc_dota_creature", "Model": "models/heroes/abaddon/abaddon.vmdl", "SoundSet": "Hero_<PERSON><PERSON>", "ModelScale": 1.3, "Level": 3, "ArmorPhysical": 3.5, "AttackAnimationPoint": 0.56, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackDamageMax": 76, "AttackDamageMin": 65, "AttackRange": 150, "AttackRate": 1.5, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 1, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 400, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": "abaddon_aphotic_shield", "Ability2": "fw_unit_str_hero", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 454}, "2": {"ItemDef": 455}, "3": {"ItemDef": 456}, "4": {"ItemDef": 457}, "5": {"ItemDef": 458}, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_ursa": {"Template": "creep_default_ursa", "BaseClass": "npc_dota_creature", "Model": "models/heroes/ursa/ursa.vmdl", "SoundSet": "Hero_Ursa", "ModelScale": 1, "Level": 4, "ArmorPhysical": 3, "AttackAnimationPoint": 0.3, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackDamageMax": 70, "AttackDamageMin": 60, "AttackRange": 150, "AttackRate": 1.7, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 1, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 400, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": "ursa_fury_swipes", "Ability2": "fw_unit_int_hero", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 85}, "2": {"ItemDef": 86}, "3": {"ItemDef": 87}, "4": {"ItemDef": 88}, "5": {"ItemDef": 584}, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_weaver": {"Template": "creep_default_weaver", "BaseClass": "npc_dota_creature", "Model": "models/heroes/weaver/weaver.vmdl", "SoundSet": "<PERSON><PERSON><PERSON>", "ModelScale": 1.3, "Level": 3, "ArmorPhysical": 4, "AttackAnimationPoint": 0.55, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackDamageMax": 67, "AttackDamageMin": 60, "AttackRange": 425, "AttackRate": 1.1, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 1, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "ProjectileModel": "particles/units/heroes/hero_weaver/weaver_base_attack.vpcf", "ProjectileSpeed": 900, "RingRadius": 70, "StatusHealth": 330, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": "weaver_geminate_attack", "Ability2": "fw_unit_int_hero", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 277}, "2": {"ItemDef": 278}, "3": {"ItemDef": 279}, "4": {"ItemDef": 280}, "5": {"ItemDef": 585}, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_spirit_breaker": {"Template": "creep_default_spirit_breaker", "BaseClass": "npc_dota_creature", "Model": "models/heroes/spirit_breaker/spirit_breaker.vmdl", "SoundSet": "Hero_spirit_breaker", "ModelScale": 1.3, "Level": 3, "ArmorPhysical": 5, "AttackAnimationPoint": 0.6, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackDamageMax": 100, "AttackDamageMin": 90, "AttackRange": 150, "AttackRate": 1.9, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 1, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 350, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 500, "StatusHealthRegen": 0, "StatusMana": 500, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": "spirit_breaker_charge_of_darkness", "Ability2": "spirit_breaker_greater_bash", "Ability3": "fw_unit_str_hero_health", "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 106}, "2": {"ItemDef": 111}, "3": {"ItemDef": 112}, "4": {"ItemDef": 113}, "5": {"ItemDef": 114}, "6": {"ItemDef": 115}, "7": {"ItemDef": 116}, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_huskar": {"Template": "creep_default_huskar", "BaseClass": "npc_dota_creature", "Model": "models/heroes/huskar/huskar.vmdl", "SoundSet": "<PERSON><PERSON><PERSON><PERSON>", "ModelScale": 1, "Level": 5, "ArmorPhysical": 3, "AttackAnimationPoint": 0.3, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackDamageMax": 90, "AttackDamageMin": 80, "AttackRange": 400, "AttackRate": 1.5, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 1, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "ProjectileModel": "particles/units/heroes/hero_huskar/huskar_base_attack.vpcf", "ProjectileSpeed": 1400, "RingRadius": 70, "StatusHealth": 800, "StatusHealthRegen": 0.1, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": "huskar_burning_spear", "Ability2": "fw_huskar_berserkers_blood", "Ability3": "fw_unit_str_hero_health", "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 268}, "2": {"ItemDef": 269}, "3": {"ItemDef": 270}, "4": {"ItemDef": 271}, "5": {"ItemDef": 272}, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_earthshaker": {"Template": "creep_default_earthshaker", "BaseClass": "npc_dota_creature", "Model": "models/heroes/earthshaker/earthshaker.vmdl", "SoundSet": "Hero_Earthshaker", "ModelScale": 1, "Level": 2, "ArmorPhysical": 2, "AttackAnimationPoint": 0.467, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackDamageMax": 50, "AttackDamageMin": 40, "AttackRange": 150, "AttackRate": 1.7, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 1, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 250, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 200, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": "earthshaker_echo_slam", "Ability2": "fw_unit_str_hero_health", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 459}, "2": {"ItemDef": 460}, "3": {"ItemDef": 461}, "4": {"ItemDef": 462}, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_skywrath_mage": {"Template": "creep_default_skywrath_mage", "BaseClass": "npc_dota_creature", "Model": "models/heroes/skywrath_mage/skywrath_mage.vmdl", "SoundSet": "Hero_SkywrathMage", "ModelScale": 1, "Level": 1, "ArmorPhysical": 2, "AttackAnimationPoint": 0.4, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackDamageMax": 60, "AttackDamageMin": 50, "AttackRange": 625, "AttackRate": 1.7, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 1, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "ProjectileModel": "particles/units/heroes/hero_skywrath_mage/skywrath_mage_base_attack.vpcf", "ProjectileSpeed": 1000, "RingRadius": 70, "StatusHealth": 300, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 800, "VisionNighttimeRange": 800, "Ability1": "skywrath_mage_concussive_shot", "Ability2": "fw_unit_agi_hero", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 420}, "2": {"ItemDef": 421}, "3": {"ItemDef": 422}, "4": {"ItemDef": 423}, "5": {"ItemDef": 424}, "6": {"ItemDef": 425}, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 800, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_dark_seer": {"Template": "creep_default_dark_seer", "BaseClass": "npc_dota_creature", "Model": "models/heroes/dark_seer/dark_seer.vmdl", "SoundSet": "Hero_DarkSeer", "ModelScale": 1, "Level": 4, "ArmorPhysical": 4, "AttackAnimationPoint": 0.59, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackDamageMax": 90, "AttackDamageMin": 70, "AttackRange": 150, "AttackRate": 1.7, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 1, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 450, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": "dark_seer_ion_shell", "Ability2": "fw_unit_agi_hero", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 348}, "2": {"ItemDef": 349}, "3": {"ItemDef": 350}, "4": {"ItemDef": 351}, "5": {"ItemDef": 352}, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_primal_beast": {"Template": "creep_default_primal_beast", "BaseClass": "npc_dota_creature", "Model": "models/heroes/primal_beast/primal_beast_base.vmdl", "SoundSet": "Hero_PrimalBeast", "ModelScale": 1.3, "Level": 4, "ArmorPhysical": 5, "AttackAnimationPoint": 0.6, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackDamageMax": 90, "AttackDamageMin": 80, "AttackRange": 150, "AttackRate": 1.8, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 1, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 750, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": "primal_beast_pulverize", "Ability2": "fw_unit_str_hero_health", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 769}, "2": {"ItemDef": 770}, "3": {"ItemDef": 771}, "4": {"ItemDef": 772}, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_tiny": {"Template": "creep_default_tiny", "BaseClass": "npc_dota_creature", "Model": "models/heroes/tiny/tiny_04/tiny_04.vmdl", "SoundSet": "<PERSON>_<PERSON>", "ModelScale": 1.8, "Level": 1, "ArmorPhysical": 8, "AttackAnimationPoint": 0.4, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackDamageMax": 170, "AttackDamageMin": 150, "AttackRange": 200, "AttackRate": 1.7, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 350, "MagicalResistance": 10, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 250, "MovementTurnRate": 0.5, "RingRadius": 100, "StatusHealth": 1000, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": "fw_tiny_tree_grab", "Ability2": "fw_unit_tiny_hero", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "base_creep_default_luna": {"Template": "creep_default_luna", "BaseClass": "npc_dota_creature", "Model": "models/heroes/luna/luna.vmdl", "SoundSet": "<PERSON><PERSON><PERSON>", "ModelScale": 1, "Level": 3, "ArmorPhysical": 3, "AttackAnimationPoint": 0.35, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackDamageMax": 60, "AttackDamageMin": 50, "AttackRange": 400, "AttackRate": 1.7, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 1, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "ProjectileModel": "particles/units/heroes/hero_luna/luna_base_attack.vpcf", "ProjectileSpeed": 900, "RingRadius": 70, "StatusHealth": 400, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 800, "VisionNighttimeRange": 800, "Ability1": "fw_luna_lunar_grace", "Ability2": "fw_unit_luna_hero", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 448}, "2": {"ItemDef": 449}, "3": {"ItemDef": 450}, "4": {"ItemDef": 451}, "5": {"ItemDef": 452}, "6": {"ItemDef": 453}, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 800, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_sniper": {"Template": "creep_default_sniper", "BaseClass": "npc_dota_creature", "Model": "models/heroes/sniper/sniper.vmdl", "SoundSet": "<PERSON>_<PERSON><PERSON><PERSON>", "ModelScale": 1, "Level": 1, "ArmorPhysical": 5, "AttackAnimationPoint": 0.17, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackDamageMax": 65, "AttackDamageMin": 50, "AttackRange": 550, "AttackRate": 1.7, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 1, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "ProjectileModel": "particles/units/heroes/hero_sniper/sniper_base_attack.vpcf", "ProjectileSpeed": 3000, "RingRadius": 70, "StatusHealth": 250, "StatusHealthRegen": 0, "StatusMana": 1000, "StatusManaRegen": 0, "VisionDaytimeRange": 800, "VisionNighttimeRange": 800, "Ability1": "sniper_headshot", "Ability2": "sniper_keen_scope", "Ability3": "fw_unit_int_hero", "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 281}, "2": {"ItemDef": 282}, "3": {"ItemDef": 283}, "4": {"ItemDef": 284}, "5": {"ItemDef": 285}, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 800, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_techies": {"Template": "creep_default_techies", "BaseClass": "npc_dota_creature", "Model": "models/heroes/techies/techies.vmdl", "SoundSet": "Hero_Techies", "ModelScale": 0.7, "Level": 2, "ArmorPhysical": 5, "AttackAnimationPoint": 0.5, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackDamageMax": 70, "AttackDamageMin": 60, "AttackRange": 600, "AttackRate": 1.7, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 1, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 200, "MovementTurnRate": 1, "ProjectileModel": "particles/units/heroes/hero_techies/techies_base_attack.vpcf", "ProjectileSpeed": 900, "RingRadius": 70, "StatusHealth": 600, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 800, "VisionNighttimeRange": 800, "Ability1": "fw_techies_summon_bomb", "Ability2": "fw_unit_agi_hero", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 487}, "2": {"ItemDef": 489}, "3": {"ItemDef": 490}, "4": {"ItemDef": 491}, "5": {"ItemDef": 492}, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 800, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_jakiro": {"Template": "creep_default_jakiro", "BaseClass": "npc_dota_creature", "Model": "models/heroes/jakiro/jakiro.vmdl", "SoundSet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ModelScale": 1, "Level": 1, "ArmorPhysical": 5, "AttackAnimationPoint": 0.4, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackDamageMax": 40, "AttackDamageMin": 35, "AttackRange": 400, "AttackRate": 1.7, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 1, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "ProjectileModel": "particles/units/heroes/hero_jakiro/jakiro_base_attack.vpcf", "ProjectileSpeed": 1100, "RingRadius": 70, "StatusHealth": 300, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 800, "VisionNighttimeRange": 800, "Ability1": "jakiro_liquid_fire", "Ability2": "jakiro_liquid_ice", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 631}, "2": {"ItemDef": 633}, "3": {"ItemDef": 634}, "4": {"ItemDef": 635}, "5": {"ItemDef": 636}, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}, "UsesGestureBasedAttackAnimation": 1}, "AttackAcquisitionRange": 800, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_visage": {"Template": "creep_default_visage", "BaseClass": "npc_dota_creature", "Model": "models/heroes/visage/visage.vmdl", "SoundSet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ModelScale": 1, "Level": 1, "ArmorPhysical": 5, "AttackAnimationPoint": 0.4, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackDamageMax": 55, "AttackDamageMin": 45, "AttackRange": 400, "AttackRate": 1.7, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 1, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "ProjectileModel": "particles/units/heroes/hero_jakiro/jakiro_base_attack.vpcf", "ProjectileSpeed": 1100, "RingRadius": 70, "StatusHealth": 500, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 800, "VisionNighttimeRange": 800, "Ability1": "jaki<PERSON>_dual_breath", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}, "UsesGestureBasedAttackAnimation": 1}, "AttackAcquisitionRange": 800, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_luna": {"Template": "creep_default_luna", "BaseClass": "npc_dota_creature", "Model": "models/heroes/luna/luna.vmdl", "SoundSet": "<PERSON><PERSON><PERSON>", "ModelScale": 1, "Level": 3, "ArmorPhysical": 3, "AttackAnimationPoint": 0.35, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackDamageMax": 60, "AttackDamageMin": 50, "AttackRange": 400, "AttackRate": 1.7, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 1, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "ProjectileModel": "particles/units/heroes/hero_luna/luna_base_attack.vpcf", "ProjectileSpeed": 900, "RingRadius": 70, "StatusHealth": 400, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 800, "VisionNighttimeRange": 800, "Ability1": "fw_luna_lunar_grace", "Ability2": "fw_unit_luna_hero", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 448}, "2": {"ItemDef": 449}, "3": {"ItemDef": 450}, "4": {"ItemDef": 451}, "5": {"ItemDef": 452}, "6": {"ItemDef": 453}, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 800, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_alpha_wolf": {"Template": "creep_default_alpha_wolf", "BaseClass": "npc_dota_creature", "Model": "models/creeps/neutral_creeps/n_creep_worg_large/n_creep_worg_large.vmdl", "SoundSet": "n_creep_<PERSON>ee", "ModelScale": 0.93, "Level": 3, "ArmorPhysical": 3, "AttackAnimationPoint": 0.33, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackDamageMax": 55, "AttackDamageMin": 45, "AttackRange": 100, "AttackRate": 2, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 45, "StatusHealth": 250, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": "alpha_wolf_command_aura", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_giant_wolf": {"Template": "creep_default_giant_wolf", "BaseClass": "npc_dota_creature", "Model": "models/creeps/neutral_creeps/n_creep_worg_small/n_creep_worg_small.vmdl", "SoundSet": "n_creep_<PERSON>ee", "ModelScale": 0.93, "Level": 3, "ArmorPhysical": 2, "AttackAnimationPoint": 0.33, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackDamageMax": 35, "AttackDamageMin": 30, "AttackRange": 100, "AttackRate": 2, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 45, "StatusHealth": 150, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": 0, "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_polar_furbolg_ursa_warrior": {"Template": "creep_default_polar_furbolg_ursa_warrior", "BaseClass": "npc_dota_creature", "Model": "models/creeps/neutral_creeps/n_creep_furbolg/n_creep_furbolg_disrupter.vmdl", "SoundSet": "n_creep_<PERSON>ee", "ModelScale": 1.1, "Level": 3, "ArmorPhysical": 3, "AttackAnimationPoint": 0.3, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackDamageMax": 60, "AttackDamageMin": 50, "AttackRange": 100, "AttackRate": 2, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 50, "StatusHealth": 500, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": "polar_furbolg_ursa_warrior_thunder_clap", "Ability2": "furbolg_enrage_damage", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_polar_furbolg_champion": {"Template": "creep_default_polar_furbolg_champion", "BaseClass": "npc_dota_creature", "Model": "models/creeps/neutral_creeps/n_creep_beast/n_creep_beast.vmdl", "SoundSet": "n_creep_<PERSON>ee", "ModelScale": 1.1, "Level": 3, "ArmorPhysical": 3, "AttackAnimationPoint": 0.3, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackDamageMax": 70, "AttackDamageMin": 60, "AttackRange": 100, "AttackRate": 2, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 50, "StatusHealth": 500, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": "centaur_khan_endurance_aura", "Ability2": "furbolg_enrage_attack_speed", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_ogre_magi": {"Template": "creep_default_ogre_magi", "BaseClass": "npc_dota_creature", "Model": "models/creeps/neutral_creeps/n_creep_ogre_lrg/n_creep_ogre_lrg.vmdl", "SoundSet": "n_creep_<PERSON>ee", "ModelScale": 1, "Level": 2, "ArmorPhysical": 2, "AttackAnimationPoint": 0.3, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackDamageMax": 50, "AttackDamageMin": 40, "AttackRange": 100, "AttackRate": 2, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 50, "StatusHealth": 400, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": "ogre_magi_frost_armor", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_ogre_mauler": {"Template": "creep_default_ogre_mauler", "BaseClass": "npc_dota_creature", "Model": "models/creeps/neutral_creeps/n_creep_ogre_med/n_creep_ogre_med.vmdl", "SoundSet": "n_creep_<PERSON>ee", "ModelScale": 1, "Level": 2, "ArmorPhysical": 2, "AttackAnimationPoint": 0.3, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackDamageMax": 60, "AttackDamageMin": 50, "AttackRange": 100, "AttackRate": 2, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 50, "StatusHealth": 450, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": "ogre_bruiser_ogre_smash", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_elder_jungle_stalker": {"Template": "creep_default_elder_jungle_stalker", "BaseClass": "npc_dota_creature", "Model": "models/creeps/neutral_creeps/n_creep_gargoyle/n_creep_gargoyle.vmdl", "SoundSet": "n_creep_<PERSON>ee", "ModelScale": 1, "Level": 1, "ArmorPhysical": 1, "AttackAnimationPoint": 0.3, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackDamageMax": 35, "AttackDamageMin": 30, "AttackRange": 100, "AttackRate": 2, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 45, "StatusHealth": 300, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": 0, "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_jungle_stalker": {"Template": "creep_default_jungle_stalker", "BaseClass": "npc_dota_creature", "Model": "models/creeps/neutral_creeps/n_creep_gargoyle/n_creep_gargoyle.vmdl", "SoundSet": "n_creep_<PERSON>ee", "ModelScale": 1, "Level": 1, "ArmorPhysical": 1, "AttackAnimationPoint": 0.3, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackDamageMax": 35, "AttackDamageMin": 30, "AttackRange": 128, "AttackRate": 2, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 45, "StatusHealth": 300, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": 0, "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_prowler_acolyte": {"Template": "creep_default_prowler_acolyte", "BaseClass": "npc_dota_creature", "Model": "models/creeps/neutral_creeps/n_creep_satyr_spawn_a/n_creep_satyr_spawn_b.vmdl", "SoundSet": "n_creep_<PERSON>ee", "ModelScale": 1, "Level": 1, "ArmorPhysical": 5, "AttackAnimationPoint": 0.83, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackDamageMax": 60, "AttackDamageMin": 50, "AttackRange": 100, "AttackRate": 2, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 350, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": 0, "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_prowler_shaman": {"Template": "creep_default_prowler_shaman", "BaseClass": "npc_dota_creature", "Model": "models/creeps/neutral_creeps/n_creep_satyr_spawn_a/n_creep_satyr_spawn_a.vmdl", "SoundSet": "n_creep_<PERSON>ee", "ModelScale": 1, "Level": 1, "ArmorPhysical": 2, "AttackAnimationPoint": 0.83, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackDamageMax": 85, "AttackDamageMin": 75, "AttackRange": 100, "AttackRate": 2, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 500, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": "spawnlord_master_freeze", "Ability2": "spawnlord_master_stomp", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_big_thunder_lizard": {"Template": "creep_default_big_thunder_lizard", "BaseClass": "npc_dota_creature", "Model": "models/creeps/neutral_creeps/n_creep_thunder_lizard/n_creep_thunder_lizard_big.vmdl", "SoundSet": "n_creep_Ranged", "ModelScale": 1, "Level": 5, "ArmorPhysical": 4, "AttackAnimationPoint": 0.3, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackDamageMax": 75, "AttackDamageMin": 65, "AttackRange": 350, "AttackRate": 2, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "ProjectileModel": "particles/neutral_fx/thunderlizard_base_attack.vpcf", "ProjectileSpeed": 1500, "RingRadius": 70, "StatusHealth": 450, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 800, "VisionNighttimeRange": 800, "Ability1": "big_thunder_lizard_slam", "Ability2": "big_thunder_lizard_frenzy", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 800, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_small_thunder_lizard": {"Template": "creep_default_small_thunder_lizard", "BaseClass": "npc_dota_creature", "Model": "models/creeps/neutral_creeps/n_creep_thunder_lizard/n_creep_thunder_lizard_small.vmdl", "SoundSet": "n_creep_Ranged", "ModelScale": 1, "Level": 3, "ArmorPhysical": 2, "AttackAnimationPoint": 0.5, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackDamageMax": 65, "AttackDamageMin": 55, "AttackRange": 350, "AttackRate": 2, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "ProjectileModel": "particles/neutral_fx/thunderlizard_base_attack.vpcf", "ProjectileSpeed": 1500, "RingRadius": 70, "StatusHealth": 400, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 800, "VisionNighttimeRange": 800, "Ability1": "big_thunder_lizard_wardrums_aura", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 800, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_black_dragon": {"Template": "creep_default_black_dragon", "BaseClass": "npc_dota_creature", "Model": "models/creeps/neutral_creeps/n_creep_black_dragon/n_creep_black_dragon.vmdl", "SoundSet": "n_creep_Ranged", "ModelScale": 1, "Level": 5, "ArmorPhysical": 5, "AttackAnimationPoint": 0.5, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackDamageMax": 120, "AttackDamageMin": 100, "AttackRange": 450, "AttackRate": 2, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 300, "MagicalResistance": 40, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "ProjectileModel": "particles/neutral_fx/black_dragon_attack.vpcf", "ProjectileSpeed": 1500, "RingRadius": 70, "StatusHealth": 400, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 800, "VisionNighttimeRange": 800, "Ability1": "black_dragon_splash_attack", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 800, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_ice_shaman": {"Template": "creep_default_ice_shaman", "BaseClass": "npc_dota_creature", "Model": "models/creeps/ice_biome/giant/ice_giant01.vmdl", "SoundSet": "n_creep_Ranged", "ModelScale": 1.2, "Level": 5, "ArmorPhysical": 3, "AttackAnimationPoint": 0.7, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackDamageMax": 80, "AttackDamageMin": 70, "AttackRange": 500, "AttackRate": 1.8, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 300, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "ProjectileModel": "particles/units/heroes/hero_witchdoctor/witchdoctor_base_attack.vpcf", "ProjectileSpeed": 1500, "RingRadius": 70, "StatusHealth": 400, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 800, "VisionNighttimeRange": 800, "Ability1": "ice_shaman_incendiary_bomb", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 800, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_forest_troll_high_priest": {"Template": "creep_default_forest_troll_high_priest", "BaseClass": "npc_dota_creature", "Model": "models/creeps/neutral_creeps/n_creep_forest_trolls/n_creep_forest_troll_high_priest.vmdl", "SoundSet": "n_creep_Ranged", "ModelScale": 0.93, "Level": 1, "ArmorPhysical": 1, "AttackAnimationPoint": 0.3, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackDamageMax": 40, "AttackDamageMin": 30, "AttackRange": 600, "AttackRate": 2, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "ProjectileModel": "particles/units/heroes/hero_witchdoctor/witchdoctor_base_attack.vpcf", "ProjectileSpeed": 900, "RingRadius": 45, "StatusHealth": 200, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 800, "VisionNighttimeRange": 800, "Ability1": "fw_forest_troll_high_priest_heal", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 800, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_forest_troll_berserker": {"Template": "creep_default_forest_troll_berserker", "BaseClass": "npc_dota_creature", "Model": "models/creeps/neutral_creeps/n_creep_forest_trolls/n_creep_forest_troll_berserker.vmdl", "SoundSet": "n_creep_Ranged", "ModelScale": 0.93, "Level": 1, "ArmorPhysical": 1, "AttackAnimationPoint": 0.3, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackDamageMax": 45, "AttackDamageMin": 35, "AttackRange": 500, "AttackRate": 2, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "ProjectileModel": "particles/units/heroes/hero_witchdoctor/witchdoctor_base_attack.vpcf", "ProjectileSpeed": 1200, "RingRadius": 45, "StatusHealth": 200, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 800, "VisionNighttimeRange": 800, "Ability1": "berserker_troll_break", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 800, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_roshan": {"Template": "creep_default_roshan", "BaseClass": "npc_dota_creature", "Model": "models/creeps/roshan/roshan.vmdl", "SoundSet": "<PERSON><PERSON><PERSON>", "ModelScale": 2, "Level": 10, "ArmorPhysical": 1, "AttackAnimationPoint": 0.6, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackDamageMax": 100, "AttackDamageMin": 100, "AttackRange": 150, "AttackRate": 2, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "ProjectileModel": "particles/units/heroes/hero_witchdoctor/witchdoctor_base_attack.vpcf", "ProjectileSpeed": 1000, "RingRadius": 45, "StatusHealth": 2000, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": "roshan_spell_block", "Ability2": "roshan_slam", "Ability3": "roshan_bash", "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR", "AttackSpeedActivityModifiers": "frostivus"}, "default_creep_default_viper": {"Template": "creep_default_viper", "BaseClass": "npc_dota_creature", "Model": "models/heroes/viper/viper.vmdl", "SoundSet": "Hero_Viper", "ModelScale": 1, "Level": 1, "ArmorPhysical": 5, "AttackAnimationPoint": 0.17, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackDamageMax": 55, "AttackDamageMin": 45, "AttackRange": 550, "AttackRate": 1.7, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 1, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 500, "StatusHealthRegen": 0, "StatusMana": 1000, "StatusManaRegen": 0, "VisionDaytimeRange": 800, "VisionNighttimeRange": 800, "Ability1": 0, "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 519}, "2": {"ItemDef": 611}, "3": {"ItemDef": 623}, "4": {"ItemDef": 654}, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 800, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_gyrocopter": {"Template": "creep_default_gyrocopter", "BaseClass": "npc_dota_creature", "Model": "models/heroes/gyro/gyro.vmdl", "SoundSet": "Hero_Gyrocopter", "ModelScale": 1, "Level": 1, "ArmorPhysical": 0, "AttackAnimationPoint": 0.17, "AttackCapabilities": "DOTA_UNIT_CAP_NO_ATTACK", "AttackDamageMax": 55, "AttackDamageMin": 45, "AttackRange": 100, "AttackRate": 1.7, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_NONE", "MovementSpeed": 300, "MovementTurnRate": 1, "RingRadius": 70, "StatusHealth": 100, "StatusHealthRegen": 0, "StatusMana": 1000, "StatusManaRegen": 0, "VisionDaytimeRange": 800, "VisionNighttimeRange": 800, "Ability1": "gyrocopter_call_down", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 176}, "2": {"ItemDef": 177}, "3": {"ItemDef": 178}, "4": {"ItemDef": 179}, "5": {"ItemDef": 131}, "6": {"ItemDef": 527}, "7": {"ItemDef": 126}, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 800, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_ghost": {"Template": "creep_default_ghost", "BaseClass": "npc_dota_creature", "Model": "models/creeps/neutral_creeps/n_creep_ghost_a/n_creep_ghost_a.vmdl", "SoundSet": "n_creep_Ranged", "ModelScale": 0.93, "Level": 1, "ArmorPhysical": 1, "AttackAnimationPoint": 0.3, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackDamageMax": 35, "AttackDamageMin": 30, "AttackRange": 400, "AttackRate": 2, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "ProjectileModel": "particles/neutral_fx/ghost_base_attack.vpcf", "ProjectileSpeed": 900, "RingRadius": 50, "StatusHealth": 200, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 800, "VisionNighttimeRange": 800, "Ability1": "ghost_frost_attack", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 800, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_dark_troll": {"Template": "creep_default_dark_troll", "BaseClass": "npc_dota_creature", "Model": "models/creeps/neutral_creeps/n_creep_troll_dark_a/n_creep_troll_dark_a.vmdl", "SoundSet": "n_creep_Ranged", "ModelScale": 0.7, "Level": 1, "ArmorPhysical": 1, "AttackAnimationPoint": 0.3, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackDamageMax": 40, "AttackDamageMin": 35, "AttackRange": 400, "AttackRate": 2, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "ProjectileModel": "particles/base_attacks/ranged_badguy.vpcf", "ProjectileSpeed": 1200, "RingRadius": 45, "StatusHealth": 300, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": 0, "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_dark_troll_warlord": {"Template": "creep_default_dark_troll_warlord", "BaseClass": "npc_dota_creature", "Model": "models/creeps/neutral_creeps/n_creep_troll_dark_b/n_creep_troll_dark_b.vmdl", "SoundSet": "n_creep_Ranged", "ModelScale": 0.93, "Level": 1, "ArmorPhysical": 2, "AttackAnimationPoint": 0.3, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackDamageMax": 55, "AttackDamageMin": 45, "AttackRange": 500, "AttackRate": 2, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 150, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "ProjectileModel": "particles/base_attacks/ranged_badguy.vpcf", "ProjectileSpeed": 1200, "RingRadius": 45, "StatusHealth": 400, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 700, "VisionNighttimeRange": 700, "Ability1": "fw_summon_zombie_b", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 700, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_black_drake": {"Template": "creep_default_black_drake", "BaseClass": "npc_dota_creature", "Model": "models/creeps/neutral_creeps/n_creep_black_drake/n_creep_black_drake.vmdl", "SoundSet": "n_creep_Ranged", "ModelScale": 0.65, "Level": 5, "ArmorPhysical": 3, "AttackAnimationPoint": 0.5, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackDamageMax": 55, "AttackDamageMin": 45, "AttackRange": 400, "AttackRate": 2, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 300, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "ProjectileModel": "particles/neutral_fx/black_drake_attack.vpcf", "ProjectileSpeed": 1500, "RingRadius": 60, "StatusHealth": 300, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 800, "VisionNighttimeRange": 800, "Ability1": "black_dragon_dragonhide_aura", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 800, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_techies_bomb": {"Template": "creep_default_techies_bomb", "BaseClass": "npc_dota_creature", "Model": "models/heroes/techies/fx_techies_remote_cart.vmdl", "SoundSet": "n_creep_<PERSON>ee", "ModelScale": 1, "Level": 1, "ArmorPhysical": 0, "AttackAnimationPoint": 0.5, "AttackCapabilities": "DOTA_UNIT_CAP_MELEE_ATTACK", "AttackDamageMax": 0, "AttackDamageMin": 0, "AttackRange": 100, "AttackRate": 1, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 80, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 350, "MovementTurnRate": 1, "RingRadius": 50, "StatusHealth": 100, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 500, "VisionNighttimeRange": 500, "Ability1": "fw_techies_bomb_adsorb", "Ability2": 0, "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 500, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_shaman": {"Template": "creep_default_shaman", "BaseClass": "npc_dota_creature", "Model": "models/heroes/shadowshaman/shadowshaman.vmdl", "SoundSet": "<PERSON>_<PERSON>Shaman", "ModelScale": 0.9, "Level": 1, "ArmorPhysical": 1, "AttackAnimationPoint": 0.3, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackDamageMax": 50, "AttackDamageMin": 40, "AttackRange": 400, "AttackRate": 1.7, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 80, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 250, "MovementTurnRate": 1, "ProjectileModel": "particles/units/heroes/hero_shadowshaman/shadowshaman_base_attack.vpcf", "ProjectileSpeed": 900, "RingRadius": 50, "StatusHealth": 30, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 800, "VisionNighttimeRange": 800, "Ability1": "fw_shadow_shaman_shackles", "Ability2": "fw_unit_shaman_hero", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 246}, "2": {"ItemDef": 247}, "3": {"ItemDef": 248}, "4": {"ItemDef": 249}, "5": {"ItemDef": 250}, "6": {"ItemDef": 251}, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 800, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}, "default_creep_default_crystal_maiden": {"Template": "creep_default_crystal_maiden", "BaseClass": "npc_dota_creature", "Model": "models/heroes/crystal_maiden/crystal_maiden.vmdl", "SoundSet": "hero_<PERSON>", "ModelScale": 0.78, "Level": 1, "ArmorPhysical": 1, "AttackAnimationPoint": 0.3, "AttackCapabilities": "DOTA_UNIT_CAP_RANGED_ATTACK", "AttackDamageMax": 30, "AttackDamageMin": 20, "AttackRange": 300, "AttackRate": 1.7, "BountyGoldMax": 0, "BountyGoldMin": 0, "BountyXP": 0, "ConsideredHero": 0, "DisableWearables": 0, "HasInventory": 1, "HealthBarOffset": 80, "MagicalResistance": 25, "MinimapIconSize": 320, "MovementCapabilities": "DOTA_UNIT_CAP_MOVE_GROUND", "MovementSpeed": 300, "MovementTurnRate": 1, "ProjectileModel": "particles/units/heroes/hero_crystalmaiden/maiden_base_attack.vpcf", "ProjectileSpeed": 900, "RingRadius": 50, "StatusHealth": 30, "StatusHealthRegen": 0, "StatusMana": 100, "StatusManaRegen": 0, "VisionDaytimeRange": 800, "VisionNighttimeRange": 800, "Ability1": "fw_crystal_maiden_frostbite", "Ability2": "fw_unit_delay_hero", "Ability3": 0, "Ability4": 0, "Ability5": 0, "Ability6": 0, "Ability7": 0, "Ability8": 0, "Ability9": 0, "Ability10": 0, "Ability11": 0, "Ability12": 0, "Ability13": 0, "Ability14": 0, "Ability15": 0, "Ability16": 0, "Ability17": 0, "Ability18": 0, "Creature": {"AttachWearables": {"1": {"ItemDef": 38}, "2": {"ItemDef": 39}, "3": {"ItemDef": 40}, "4": {"ItemDef": 41}, "5": {"ItemDef": 311}, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "AttackAcquisitionRange": 800, "BoundsHullName": "DOTA_HULL_SIZE_REGULAR"}}