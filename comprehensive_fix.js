const fs = require('fs');
const path = require('path');

// 定义需要修复的模式和替换
const fixes = [
    // 修复导入大小写问题
    { pattern: /from "\.\/NPCUtils"/g, replacement: 'from "./npcutils"' },
    { pattern: /from '\.\/NPCUtils'/g, replacement: 'from "./npcutils"' },
    
    // 修复枚举和常量
    { pattern: /UnitTargetType\.CREEP/g, replacement: 'DOTA_UNIT_TARGET_CREEP' },
    { pattern: /UnitTargetType\.BUILDING/g, replacement: 'DOTA_UNIT_TARGET_BUILDING' },
    { pattern: /UnitTargetType\.HERO/g, replacement: 'DOTA_UNIT_TARGET_HERO' },
    { pattern: /UnitTargetTeam\.ENEMY/g, replacement: 'DOTA_UNIT_TARGET_TEAM_ENEMY' },
    { pattern: /UnitTargetFlags\.NONE/g, replacement: 'DOTA_UNIT_TARGET_FLAG_NONE' },
    { pattern: /UnitAttackCapability\.NO_ATTACK/g, replacement: 'DOTA_UNIT_CAP_NO_ATTACK' },
    
    { pattern: /FindOrder\.ANY/g, replacement: 'FIND_ANY_ORDER' },
    { pattern: /FindOrder\.CLOSEST/g, replacement: 'FIND_CLOSEST' },
    
    { pattern: /DamageFlag\.NONE/g, replacement: 'DOTA_DAMAGE_FLAG_NONE' },
    { pattern: /DamageTypes\.PURE/g, replacement: 'DAMAGE_TYPE_PURE' },
    
    { pattern: /GameActivity\.DOTA_CAST_ABILITY_1/g, replacement: 'ACT_DOTA_CAST_ABILITY_1' },
    { pattern: /GameActivity\.DOTA_CAST_ABILITY_3/g, replacement: 'ACT_DOTA_CAST_ABILITY_3' },
    { pattern: /GameActivity\.DOTA_SPAWN/g, replacement: 'ACT_DOTA_SPAWN' },
    
    { pattern: /ModifierFunction\.INCOMING_DAMAGE_PERCENTAGE/g, replacement: 'MODIFIER_PROPERTY_INCOMING_DAMAGE_PERCENTAGE' },
    { pattern: /ModifierFunction\.ATTACKSPEED_BONUS_CONSTANT/g, replacement: 'MODIFIER_PROPERTY_ATTACKSPEED_BONUS_CONSTANT' },
    { pattern: /ModifierFunction\.MOVESPEED_BONUS_CONSTANT/g, replacement: 'MODIFIER_PROPERTY_MOVESPEED_BONUS_CONSTANT' },
    { pattern: /ModifierFunction\.MODEL_SCALE/g, replacement: 'MODIFIER_PROPERTY_MODEL_SCALE' },
    { pattern: /ModifierFunction\.MODEL_SCALE_ANIMATE_TIME/g, replacement: 'MODIFIER_PROPERTY_MODEL_SCALE_ANIMATE_TIME' },
    { pattern: /ModifierFunction\.MODEL_SCALE_USE_IN_OUT_EASE/g, replacement: 'MODIFIER_PROPERTY_MODEL_SCALE_USE_IN_OUT_EASE' },
    { pattern: /ModifierFunction\.INVISIBILITY_LEVEL/g, replacement: 'MODIFIER_PROPERTY_INVISIBILITY_LEVEL' },
    { pattern: /ModifierFunction\.PREATTACK_BONUS_DAMAGE/g, replacement: 'MODIFIER_PROPERTY_PREATTACK_BONUS_DAMAGE' },
    { pattern: /ModifierFunction\.MAGICAL_RESISTANCE_BONUS/g, replacement: 'MODIFIER_PROPERTY_MAGICAL_RESISTANCE_BONUS' },
    { pattern: /ModifierFunction\.OVERRIDE_ANIMATION/g, replacement: 'MODIFIER_PROPERTY_OVERRIDE_ANIMATION' },
    { pattern: /ModifierFunction\.TRANSLATE_ACTIVITY_MODIFIERS/g, replacement: 'MODIFIER_PROPERTY_TRANSLATE_ACTIVITY_MODIFIERS' },
    { pattern: /ModifierFunction\.ON_DAMAGE_CALCULATED/g, replacement: 'MODIFIER_EVENT_ON_DAMAGE_CALCULATED' },
    
    { pattern: /ModifierState\.DISARMED/g, replacement: 'MODIFIER_STATE_DISARMED' },
    { pattern: /ModifierState\.ATTACK_IMMUNE/g, replacement: 'MODIFIER_STATE_ATTACK_IMMUNE' },
    { pattern: /ModifierState\.NO_HEALTH_BAR/g, replacement: 'MODIFIER_STATE_NO_HEALTH_BAR' },
    { pattern: /ModifierState\.DEBUFF_IMMUNE/g, replacement: 'MODIFIER_STATE_DEBUFF_IMMUNE' },
    { pattern: /ModifierState\.ROOTED/g, replacement: 'MODIFIER_STATE_ROOTED' },
    { pattern: /ModifierState\.FROZEN/g, replacement: 'MODIFIER_STATE_FROZEN' },
    { pattern: /ModifierState\.CANNOT_BE_MOTION_CONTROLLED/g, replacement: 'MODIFIER_STATE_CANNOT_BE_MOTION_CONTROLLED' },
    { pattern: /ModifierState\.STUNNED/g, replacement: 'MODIFIER_STATE_STUNNED' },
    { pattern: /ModifierState\.PASSIVES_DISABLED/g, replacement: 'MODIFIER_STATE_PASSIVES_DISABLED' },
    
    { pattern: /ModifierAttribute\.PERMANENT/g, replacement: 'MODIFIER_ATTRIBUTE_PERMANENT' },
    { pattern: /ModifierAttribute\.IGNORE_INVULNERABLE/g, replacement: 'MODIFIER_ATTRIBUTE_IGNORE_INVULNERABLE' },
    { pattern: /ModifierAttribute\.MULTIPLE/g, replacement: 'MODIFIER_ATTRIBUTE_MULTIPLE' },
    
    { pattern: /ConVarFlags\.NONE/g, replacement: 'FCVAR_NONE' },
    
    // 修复游戏状态
    { pattern: /GameState\.PRE_GAME/g, replacement: 'DOTA_GAMERULES_STATE_PRE_GAME' },
    { pattern: /GameState\.HERO_SELECTION/g, replacement: 'DOTA_GAMERULES_STATE_HERO_SELECTION' },
    { pattern: /GameState\.CUSTOM_GAME_SETUP/g, replacement: 'DOTA_GAMERULES_STATE_CUSTOM_GAME_SETUP' },
    
    // 修复金币修改原因
    { pattern: /ModifyGoldReason\.BUILDING/g, replacement: 'DOTA_ModifyGold_Building' },
    
    // 修复修饰符运动类型
    { pattern: /LuaModifierMotionType\.NONE/g, replacement: 'LUA_MODIFIER_MOTION_NONE' },
    { pattern: /LuaModifierMotionType\.BOTH/g, replacement: 'LUA_MODIFIER_MOTION_BOTH' },
    { pattern: /LuaModifierMotionType\.HORIZONTAL/g, replacement: 'LUA_MODIFIER_MOTION_HORIZONTAL' },
    { pattern: /LuaModifierMotionType\.VERTICAL/g, replacement: 'LUA_MODIFIER_MOTION_VERTICAL' },
    
    // 修复粒子附着
    { pattern: /ParticleAttachment\.WORLDORIGIN/g, replacement: 'ParticleAttachment_t.PATTACH_WORLDORIGIN' },
    { pattern: /ParticleAttachment\.OVERHEAD_FOLLOW/g, replacement: 'ParticleAttachment_t.PATTACH_OVERHEAD_FOLLOW' },
    { pattern: /ParticleAttachment\.ABSORIGIN/g, replacement: 'ParticleAttachment_t.PATTACH_ABSORIGIN' },
    { pattern: /ParticleAttachment\.POINT_FOLLOW/g, replacement: 'ParticleAttachment_t.PATTACH_POINT_FOLLOW' },
    
    // 修复单位命令
    { pattern: /dotaunitorder_t\.dotaunitorder_t\.DOTA_UNIT_ORDER_CAST_TARGET_TREE/g, replacement: 'dotaunitorder_t.DOTA_UNIT_ORDER_CAST_TARGET_TREE' },
    
    // 修复常量名称 - 使用正确的枚举类型
    { pattern: /PATTACH_OVERHEAD_FOLLOW/g, replacement: 'ParticleAttachment_t.PATTACH_OVERHEAD_FOLLOW' },
    { pattern: /ACT_DOTA_SPAWN/g, replacement: 'GameActivity_t.ACT_DOTA_SPAWN' },
    { pattern: /MODIFIER_STATE_STUNNED/g, replacement: 'modifierstate.MODIFIER_STATE_STUNNED' },
    { pattern: /MODIFIER_STATE_PASSIVES_DISABLED/g, replacement: 'modifierstate.MODIFIER_STATE_PASSIVES_DISABLED' },

    // 修复修饰符属性常量
    { pattern: /MODIFIER_ATTRIBUTE_PERMANENT/g, replacement: 'MODIFIER_ATTRIBUTE.MODIFIER_ATTRIBUTE_PERMANENT' },
    { pattern: /MODIFIER_ATTRIBUTE_IGNORE_INVULNERABLE/g, replacement: 'MODIFIER_ATTRIBUTE.MODIFIER_ATTRIBUTE_IGNORE_INVULNERABLE' },
    { pattern: /MODIFIER_ATTRIBUTE_MULTIPLE/g, replacement: 'MODIFIER_ATTRIBUTE.MODIFIER_ATTRIBUTE_MULTIPLE' },

    // 修复修饰符属性常量
    { pattern: /MODIFIER_PROPERTY_ATTACKSPEED_BONUS_CONSTANT/g, replacement: 'MODIFIER_PROPERTY.MODIFIER_PROPERTY_ATTACKSPEED_BONUS_CONSTANT' },
    { pattern: /MODIFIER_PROPERTY_MOVESPEED_BONUS_CONSTANT/g, replacement: 'MODIFIER_PROPERTY.MODIFIER_PROPERTY_MOVESPEED_BONUS_CONSTANT' },
    { pattern: /MODIFIER_PROPERTY_PREATTACK_BONUS_DAMAGE/g, replacement: 'MODIFIER_PROPERTY.MODIFIER_PROPERTY_PREATTACK_BONUS_DAMAGE' },
    { pattern: /MODIFIER_PROPERTY_MAGICAL_RESISTANCE_BONUS/g, replacement: 'MODIFIER_PROPERTY.MODIFIER_PROPERTY_MAGICAL_RESISTANCE_BONUS' },
    { pattern: /MODIFIER_PROPERTY_OVERRIDE_ANIMATION/g, replacement: 'MODIFIER_PROPERTY.MODIFIER_PROPERTY_OVERRIDE_ANIMATION' },
    { pattern: /MODIFIER_PROPERTY_TRANSLATE_ACTIVITY_MODIFIERS/g, replacement: 'MODIFIER_PROPERTY.MODIFIER_PROPERTY_TRANSLATE_ACTIVITY_MODIFIERS' },

    // 修复修饰符状态常量
    { pattern: /MODIFIER_STATE_DISARMED/g, replacement: 'MODIFIER_STATE.MODIFIER_STATE_DISARMED' },
    { pattern: /MODIFIER_STATE_ATTACK_IMMUNE/g, replacement: 'MODIFIER_STATE.MODIFIER_STATE_ATTACK_IMMUNE' },
    { pattern: /MODIFIER_STATE_NO_HEALTH_BAR/g, replacement: 'MODIFIER_STATE.MODIFIER_STATE_NO_HEALTH_BAR' },
    { pattern: /MODIFIER_STATE_ROOTED/g, replacement: 'MODIFIER_STATE.MODIFIER_STATE_ROOTED' },
    { pattern: /MODIFIER_STATE_CANNOT_BE_MOTION_CONTROLLED/g, replacement: 'MODIFIER_STATE.MODIFIER_STATE_CANNOT_BE_MOTION_CONTROLLED' },

    // 修复单位目标常量
    { pattern: /DOTA_UNIT_TARGET_HERO/g, replacement: 'DOTA_UNIT_TARGET.DOTA_UNIT_TARGET_HERO' },
    { pattern: /DOTA_UNIT_TARGET_CREEP/g, replacement: 'DOTA_UNIT_TARGET.DOTA_UNIT_TARGET_CREEP' },
    { pattern: /DOTA_UNIT_TARGET_BUILDING/g, replacement: 'DOTA_UNIT_TARGET.DOTA_UNIT_TARGET_BUILDING' },
    { pattern: /DOTA_UNIT_TARGET_HEROES_AND_CREEPS/g, replacement: 'DOTA_UNIT_TARGET.DOTA_UNIT_TARGET_HEROES_AND_CREEPS' },
    { pattern: /DOTA_UNIT_TARGET_TEAM_ENEMY/g, replacement: 'DOTA_UNIT_TARGET_TEAM.DOTA_UNIT_TARGET_TEAM_ENEMY' },
    { pattern: /DOTA_UNIT_TARGET_FLAG_NONE/g, replacement: 'DOTA_UNIT_TARGET_FLAGS.DOTA_UNIT_TARGET_FLAG_NONE' },

    // 修复伤害类型常量
    { pattern: /DAMAGE_TYPE_PURE/g, replacement: 'DAMAGE_TYPES.DAMAGE_TYPE_PURE' },
    { pattern: /DOTA_DAMAGE_FLAG_NONE/g, replacement: 'DOTADamageFlag_t.DOTA_DAMAGE_FLAG_NONE' },

    // 修复其他常量
    { pattern: /FIND_CLOSEST/g, replacement: 'FIND_ORDER_TYPE.FIND_CLOSEST' },
    { pattern: /FIND_ANY_ORDER/g, replacement: 'FIND_ORDER_TYPE.FIND_ANY_ORDER' },
    { pattern: /FCVAR_NONE/g, replacement: 'FCVAR.FCVAR_NONE' },
    { pattern: /DOTA_UNIT_CAP_NO_ATTACK/g, replacement: 'DOTAUnitAttackCapability_t.DOTA_UNIT_CAP_NO_ATTACK' },

    // 修复游戏状态常量
    { pattern: /DOTA_GAMERULES_STATE_PRE_GAME/g, replacement: 'DOTA_GameState.DOTA_GAMERULES_STATE_PRE_GAME' },
    { pattern: /DOTA_GAMERULES_STATE_HERO_SELECTION/g, replacement: 'DOTA_GameState.DOTA_GAMERULES_STATE_HERO_SELECTION' },
    { pattern: /DOTA_GAMERULES_STATE_CUSTOM_GAME_SETUP/g, replacement: 'DOTA_GameState.DOTA_GAMERULES_STATE_CUSTOM_GAME_SETUP' },

    // 修复金币修改原因
    { pattern: /DOTA_ModifyGold_Building/g, replacement: 'EDOTA_ModifyGold_Reason.DOTA_ModifyGold_Building' },
    
    // 修复类型注解
    { pattern: /let (\w+) = \{\}/g, replacement: 'let $1: Record<string, any> = {}' },
    { pattern: /let (\w+) = \[\]/g, replacement: 'let $1: any[] = []' },
];

// 递归遍历目录
function walkDir(dir, callback) {
    const files = fs.readdirSync(dir);
    files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        if (stat.isDirectory()) {
            walkDir(filePath, callback);
        } else if (stat.isFile() && (file.endsWith('.ts') || file.endsWith('.tsx'))) {
            callback(filePath);
        }
    });
}

// 修复文件
function fixFile(filePath) {
    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;
    
    fixes.forEach(fix => {
        if (fix.pattern.test(content)) {
            content = content.replace(fix.pattern, fix.replacement);
            changed = true;
        }
    });
    
    if (changed) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`Fixed comprehensive errors in: ${filePath}`);
    }
}

// 主函数
function main() {
    const srcDir = path.join(__dirname, 'src');
    console.log('Starting comprehensive error fixes...');
    
    walkDir(srcDir, fixFile);
    
    console.log('Comprehensive error fixes completed!');
}

main();
