const fs = require('fs');
const path = require('path');

// 定义需要修复的模式和替换
const fixes = [
    // 枚举类型修复
    { pattern: /DotaTeam\.GOODGUYS/g, replacement: 'DOTATeam_t.DOTA_TEAM_GOODGUYS' },
    { pattern: /DotaTeam\.BADGUYS/g, replacement: 'DOTATeam_t.DOTA_TEAM_BADGUYS' },
    { pattern: /DotaTeam\.NEUTRALS/g, replacement: 'DOTATeam_t.DOTA_TEAM_NEUTRALS' },
    { pattern: /DOTA_TEAM_GOODGUYS/g, replacement: 'DOTATeam_t.DOTA_TEAM_GOODGUYS' },
    { pattern: /DOTA_TEAM_BADGUYS/g, replacement: 'DOTATeam_t.DOTA_TEAM_BADGUYS' },
    { pattern: /DOTA_TEAM_NEUTRALS/g, replacement: 'DOTATeam_t.DOTA_TEAM_NEUTRALS' },
    { pattern: /ModifierState\.STUNNED/g, replacement: 'MODIFIER_STATE_STUNNED' },
    { pattern: /ModifierState\.PASSIVES_DISABLED/g, replacement: 'MODIFIER_STATE_PASSIVES_DISABLED' },
    { pattern: /ParticleAttachment\.WORLDORIGIN/g, replacement: 'ParticleAttachment_t.PATTACH_WORLDORIGIN' },
    { pattern: /ParticleAttachment\.OVERHEAD_FOLLOW/g, replacement: 'ParticleAttachment_t.PATTACH_OVERHEAD_FOLLOW' },
    { pattern: /ParticleAttachment\.ABSORIGIN/g, replacement: 'ParticleAttachment_t.PATTACH_ABSORIGIN' },
    { pattern: /ParticleAttachment\.POINT_FOLLOW/g, replacement: 'ParticleAttachment_t.PATTACH_POINT_FOLLOW' },
    { pattern: /UnitOrder\.CAST_POSITION/g, replacement: 'dotaunitorder_t.DOTA_UNIT_ORDER_CAST_POSITION' },
    { pattern: /UnitOrder\.CAST_TARGET/g, replacement: 'dotaunitorder_t.DOTA_UNIT_ORDER_CAST_TARGET' },
    { pattern: /UnitOrder\.CAST_TARGET_TREE/g, replacement: 'dotaunitorder_t.DOTA_UNIT_ORDER_CAST_TARGET_TREE' },
    { pattern: /UnitOrder\.ATTACK_MOVE/g, replacement: 'dotaunitorder_t.DOTA_UNIT_ORDER_ATTACK_MOVE' },
    { pattern: /UnitOrder\.ATTACK_TARGET/g, replacement: 'dotaunitorder_t.DOTA_UNIT_ORDER_ATTACK_TARGET' },
    { pattern: /UnitOrder\.HOLD_POSITION/g, replacement: 'dotaunitorder_t.DOTA_UNIT_ORDER_HOLD_POSITION' },
    { pattern: /DOTA_UNIT_ORDER_CAST_POSITION/g, replacement: 'dotaunitorder_t.DOTA_UNIT_ORDER_CAST_POSITION' },
    { pattern: /DOTA_UNIT_ORDER_CAST_TARGET/g, replacement: 'dotaunitorder_t.DOTA_UNIT_ORDER_CAST_TARGET' },
    { pattern: /DOTA_UNIT_ORDER_CAST_TARGET_TREE/g, replacement: 'dotaunitorder_t.DOTA_UNIT_ORDER_CAST_TARGET_TREE' },
    { pattern: /DOTA_UNIT_ORDER_ATTACK_MOVE/g, replacement: 'dotaunitorder_t.DOTA_UNIT_ORDER_ATTACK_MOVE' },
    { pattern: /DOTA_UNIT_ORDER_ATTACK_TARGET/g, replacement: 'dotaunitorder_t.DOTA_UNIT_ORDER_ATTACK_TARGET' },
    { pattern: /DOTA_UNIT_ORDER_HOLD_POSITION/g, replacement: 'dotaunitorder_t.DOTA_UNIT_ORDER_HOLD_POSITION' },
    { pattern: /GameState\.PRE_GAME/g, replacement: 'DOTA_GAMERULES_STATE_PRE_GAME' },
    { pattern: /GameState\.HERO_SELECTION/g, replacement: 'DOTA_GAMERULES_STATE_HERO_SELECTION' },
    { pattern: /GameState\.CUSTOM_GAME_SETUP/g, replacement: 'DOTA_GAMERULES_STATE_CUSTOM_GAME_SETUP' },
    { pattern: /ModifyGoldReason\.BUILDING/g, replacement: 'DOTA_ModifyGold_Building' },
    { pattern: /UnitTargetTeam\.ENEMY/g, replacement: 'DOTA_UNIT_TARGET_TEAM_ENEMY' },
    { pattern: /UnitTargetType\.HERO/g, replacement: 'DOTA_UNIT_TARGET_HERO' },
    { pattern: /UnitTargetFlags\.NONE/g, replacement: 'DOTA_UNIT_TARGET_FLAG_NONE' },
    { pattern: /UnitAttackCapability\.NO_ATTACK/g, replacement: 'DOTA_UNIT_CAP_NO_ATTACK' },
    { pattern: /GameActivity\.DOTA_SPAWN/g, replacement: 'ACT_DOTA_SPAWN' },
    { pattern: /LuaModifierMotionType\.NONE/g, replacement: 'LUA_MODIFIER_MOTION_NONE' },
    { pattern: /LuaModifierMotionType\.BOTH/g, replacement: 'LUA_MODIFIER_MOTION_BOTH' },
    { pattern: /LuaModifierMotionType\.HORIZONTAL/g, replacement: 'LUA_MODIFIER_MOTION_HORIZONTAL' },
    { pattern: /LuaModifierMotionType\.VERTICAL/g, replacement: 'LUA_MODIFIER_MOTION_VERTICAL' },
    { pattern: /ConVarFlags\.NONE/g, replacement: 'FCVAR_NONE' },
    
    // API 名称修复
    { pattern: /CustomNetTables\.SetTableValue/g, replacement: 'CustomNetTables.SetTableValue' },
    { pattern: /FindOrder\.CLOSEST/g, replacement: 'FIND_CLOSEST' },
];

// 递归遍历目录
function walkDir(dir, callback) {
    const files = fs.readdirSync(dir);
    files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        if (stat.isDirectory()) {
            walkDir(filePath, callback);
        } else if (stat.isFile() && (file.endsWith('.ts') || file.endsWith('.tsx'))) {
            callback(filePath);
        }
    });
}

// 修复文件
function fixFile(filePath) {
    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;
    
    fixes.forEach(fix => {
        if (fix.pattern.test(content)) {
            content = content.replace(fix.pattern, fix.replacement);
            changed = true;
        }
    });
    
    if (changed) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`Fixed: ${filePath}`);
    }
}

// 主函数
function main() {
    const srcDir = path.join(__dirname, 'src');
    console.log('Starting TypeScript error fixes...');
    
    walkDir(srcDir, fixFile);
    
    console.log('TypeScript error fixes completed!');
}

main();
