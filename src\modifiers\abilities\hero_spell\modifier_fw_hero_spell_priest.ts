import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";


export class modifier_fw_hero_spell_priest extends BaseModifier {
    
    par:ParticleID;
    scale:number;
    scaleInt:number;
    scaleTar:number;

    extra_health:number;
    movespeed:number;
    attack:number;
    attackspeed:number;
    OnCreated(keys:any): void {
        let ab = this.GetAbility()
        this.extra_health = ab.GetSpecialValueFor("extra_health")
        this.movespeed = ab.GetSpecialValueFor("movespeed")
        this.attack = ab.GetSpecialValueFor("attack")
        this.attackspeed = ab.GetSpecialValueFor("attackspeed")
        if (IsServer()) {
            let unit = this.GetParent()
            unit.EmitSound("Fw.Hero.Spell.priest.passive")
            this.par = ParticleManager.CreateParticle("particles/units/heroes/hero_ogre_magi/ogre_magi_bloodlust_buff.vpcf", ParticleAttachment_t.PATTACH_ABSORIGIN_FOLLOW, unit)
            ParticleManager.SetParticleControlEnt(this.par, 0, unit, ParticleAttachment_t.PATTACH_ABSORIGIN_FOLLOW, "attach_hitloc", Vector(0,0,0), true)
        }
    } 

    OnDestroy(){
        if (IsServer()) {
            ParticleManager.DestroyParticle(this.par, false);
        }
    }

    IsDebuff(): boolean {
        return false;
    }

    IsHidden() {
        return false;
    }

    IsPurgable(): boolean {
        return true;
    }

    GetTexture () {
        return "spell_priest"
    }

    AllowIllusionDuplicate () {
        return true;
    }

    RemoveOnDeath(): boolean {
        return true
    }

    GetModifierExtraHealthBonus(): number {
        return this.extra_health
    }

    GetModifierAttackSpeedBonus_Constant(): number {
        return this.attackspeed
    }

    GetModifierMoveSpeedBonus_Constant(): number {
        return this.movespeed
    }

    GetModifierPreAttack_BonusDamage(): number {
        return this.attack
    }

    GetModifierModelScale(): number {
        return 30
    }

    GetModifierModelScaleAnimateTime(): number {
        return 1
    }

    GetModifierModelScaleUseInOutEase(): number {
        return 1
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            24,
            1,
            2,
            7,
            3,
            4,
            5,
            ];
    }


}
