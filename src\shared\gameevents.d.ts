declare interface CustomGameEventDeclarations {
    go_used_card:GoUsedCard,
    go_change_deck_card:GoChangeDeckCard,
    go_change_player_hero:GoChangePlayerHero,
    go_test:GoTest,
    go_debug:GoDebug,
    go_phase_change:GoPhaseChange,
    go_game_process_change:GoGameProcessChange,
    go_game_start:GoGameStart,
    go_random_deck_card:GoRandomDeckCard,
    go_ui_sound:GoUISound,
    go_ui_button_disable:GoUIButtonDisable,
    go_ui_choose_mode:GoUIChooseMode,
    go_server_choose_mode:GoServerChooseMode,
}

interface GoUIChooseMode {
    groupIndex:number,
    modeIndex:number,
}
interface GoServerChooseMode {
    groupIndex:number,
    modeIndex:number,
}

interface GoUIButtonDisable {
    /**
     * 1. 禁用tab切换队伍
     */
    type:0|1|2|3|4,
    beAble:0|1,
}

interface GoTest {
    pid:number,
    str:string,
}
interface GoChangePlayerHero {
    pid:number;
    heroName:string
}
interface GoUISound {
    soundNames:string,
    group:number,//1，2，3代表防御塔受击事件，4防御塔受击，5倒计时
    interval:number,//该组别播放音效时间间隔
}
interface GoRandomDeckCard {
    pid:number;
    deckOrHero:boolean;
}
interface GoDebug {
    pid:number;
    flag:boolean
}
/**
 * 1. 开始比赛请求
 * 2. 接受比赛
 * 3. 暂缓开始
 */
interface GoGameStart{
    pid:number,
    status:1|2|3,
}
/**
 * 1. 对战开局
 * 2. 回合开始
 * 3. goodGuy获胜
 * 4. bad获胜
 * 5. 加时赛
 */
interface GoGameProcessChange{
    type:1|2|3|4|5,
    // startTime:number,
    p1?:number,
    p1Hero?:string,
    p2?:number
    p2Hero?:string,
    isGood?:boolean,
}

interface GoPhaseChange {
    oldPhase:FastWarGamePhase;
    newPhase:FastWarGamePhase;
}

interface GoChangeDeckCard {
    cardId:number;
    deckToReserve:boolean;
    pid:number;
}

interface GoUsedCard {
    aim : {x:number,y:number,z:number};
    cardId:number;
    pid:number;
    isHeroSpell:boolean;
}

declare interface GoFastWarGamePhaseTime {
    time:number,
    gameTime:number,
    gamePhase:number,
}


declare interface GoFastWarCardClient {
    card_id:number,//全局游戏唯一id
    card_index:number,//卡牌类型
    entity_indexs:string,
    card_unit_name:string,
    card_subsidiary_unit_name:string,
    next_using_time:number,
    cooldown:number,
    cost:number,
    usesNum:number,
}

declare interface SpecialUnitInfoKV {
    name:string,
    EntranceSound:string,
    DeathSound:string,
    ExtraModel:string,
    Entrance:string,
}

declare interface CardInfoKV {
    Index:number,
    name:string,
    Enable:boolean,
    EnableForDeckCards:boolean,
    chName?:string,
    layCost:number,
    CardUIType:string,
    cardType:string,
    UnitCardInfo?:CardUnitTInfo,
    SpellCardInfo?:CardSpellInfo,
    BotCardAI:BotCardAI,
    ExtraData:{
        HandCardCooldown:number,
        HandCardCooldown_UseAdd:number,
        HandCardCost_UsesCheckNum:number,
        HandCardCost:number,
        HandCardCost_MaxNum:number,
    }
}
declare interface CardUnitTInfo {
    LayType:LayType,
    Delay:number,
    LayGroupNum:number[],
    Template:{
        Template:string,
        Num:number,
    }[]
}
declare interface CardSpellInfo {
    SpellTemplate:string,
    UICardBack:string,
}
declare interface BotCardAI {
    CardTag:number,
    TarAttack:number[],
    TarDefense:number[],
}
declare interface UnitAIInfoKV {
    Name:string,
    SelfFilterType:boolean,
    TarFilterType:boolean,
    CastType:UnitOrder,
    FindTarType:number,
    FindRadiusP:{
        Radius?:number,
        TargetTeam?:UnitTargetTeam, 
        UnitTargetType?:UnitTargetType,
        UnitTargetFlags?:UnitTargetFlags,
        FindOrder?:FindOrder,
    },
    TarFilter:{
        Health?:number,
        FindSelf?:number
    },		
    SelfFilter:{
        PosY?:number,
        Health?:number,
    },
    AfterFilter:{
        RePos:boolean
        HealthDeal:number
        Num:number,
        OnlySelf:boolean,
        Delay:number,
        ModifierFilter:string[],
    }			
}

declare type LayType = "lay_ring"|"lay_row"
declare const enum GoFastSpellEffectTypeEnum {
    Damage = 1,
    CreateUnit = 2,
}

declare interface UnitTemplateInfoKV {
    name:string,
    unitType:GoFastWarAIUnitTypeEnum,
    AttackType:number,
    modelScale:number,
    isHealthBarPips:boolean,
    attackHealthPips:number,
    statusHealth:number,
    unitDuration:number,
    unitAI:{
        [keys:number]:{
            autoNum:number,
            spellAI:UnitAIInfoKV[],
        },
    },
    UIHandSpell:string,
    BaseAttackSpeed:number,
    ParEntUnit:string,
    Abilities:string[],
    RingRadius:number,
    SpawnActivityModifiers:string[],
}
declare interface SpellInfoKV {
    abName:string,
    EffectPar:string,
    UnitTemplate:string[],
    CoolDown:number,
    AOERadius:number,
    FWTargetType:GoFastWarAIUnitTypeEnum,
    Precache:{},
}

declare interface CardComboStrategy {
    Index:number,
    RelatedCards:number[],
    Priority:number,
    AttackLevel:number,
    TarAttack:number[],
    TarDefense:number[],
    minCost:number,
}

declare interface PlayerHeroForKV {
    HeroName:string,
    HeroImage:string,
    SpellName:string,
    CardIndex:number[],
}

declare interface PlayerHeroForClient {
    HeroName:string,
    HeroImage:string,
    SpellCardIndex:number,
    SpellName:string,
    cost:number;
    nextUsingTime:number,
    cooldown:number,
    UsingPar?:string,
    num:string,
    usesNum:number,
}

declare const enum PlayerHeroSpellFunction {
    NONE = 0,
    AFTER_SPELL_USING = 1,
    AFTER_ENEMY_SPELL_USING = 2,
    CARD_INIT = 3,
    AFTER_CARD_USING = 4,
    AFTER_ENEMY_CARD_USING = 5,

    CARD_BUFF = 11,
    ENEMY_CARD_DEBUFF = 12,
    TOWER_BUFF = 13,
    ENEMY_TOWER_DEBUFF = 14,
}

declare interface ModifierData {
    ab:CDOTABaseAbility,
    caster:CDOTA_BaseNPC,
    modifierName:string,
    data:any
}

declare interface PlayerHeroForDeck {
    HeroName:string,
    HeroImage:string,
    SpellName:string,
    UsingPar:string,
    cost:number,
}


declare const enum GoFastWarAIMessageTypeEnum {
    START_POS = 1,
    SET_UNIT_TAR = 2,
    USE_SPELL = 3,
}

declare const enum GoFastWarAIUnitTypeEnum {
    ALL = 0,
    GROUND = 1,
    BUILDING = 2,
}
declare const enum GoFastWarAttackTypeEnum {
    ALL = 0,
    BUILDING = 1,
    REVERSE = 2,
}
declare const enum GoFastWarCardExtraDataDealTypeEnum {
    HandCardCooldown = 1,
}
declare const enum GoFastWarGameModeEnum {
    None = 0,  
    Sand_Table_Mode=1,//沙盘模式
    Classic_Mode=2,//经典模式
    Ban_Pick_Mode=3,//ban选模式
    Challenge_Mode=4,//挑战模式
    Fun_Mode=5,//娱乐模式
}

// 游戏阶段枚举
declare const enum FastWarGamePhase {
    None = 0,       // 空阶段
    Preparing = 1,  // 准备阶段
    MainTime = 2,   // 主计时阶段（3分钟）
    Overtime = 3,   // 加时赛阶段
    TempEnd = 4,    // 小回合结束
}

// 游戏事件类型枚举
declare const enum FastWarGameEvent {
    TowerDestroyed = 1,  // 防御塔被毁
    PhaseEnd = 2, //某规则下游戏主流程全部结束
    PhaseChange = 3, //主流程阶段切换
    AcientDestroyed = 4, //基地倒塌
}
