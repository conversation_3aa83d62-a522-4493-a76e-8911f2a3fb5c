export abstract class PlayerHeroSpellHandle {

    protected nowSpellInfo:PlayerHeroForClient = {
        HeroName: "",
        HeroImage: "",
        SpellCardIndex: -1,
        SpellName: "",
        cost: -1,
        nextUsingTime:-1,
        cooldown: 0,
        num:"",
        usesNum:0,
    };
    protected hero:CDOTA_BaseNPC;
    constructor (
        protected playerId:PlayerID,
        protected spellInfo:PlayerHeroForKV
    ) {
        this.hero = PlayerResource.GetPlayer(playerId).GetAssignedHero()
       
    }

    public ClearSpell () {
        if (this.hero.FindAbilityByName(this.spellInfo.SpellName)) {
            this.hero.RemoveAbility(this.spellInfo.SpellName)
        }
    }

    GetNowHeroSpellInfo (type:PlayerHeroSpellFunction, data:any):PlayerHeroForClient {
        switch (type) {
            case PlayerHeroSpellFunction.AFTER_SPELL_USING :
                // print("触发玩家"+this.playerId+":"+"AFTER_SPELL_USING")
                this.nowSpellInfo.nextUsingTime = GameRules.GetGameTime() + this.nowSpellInfo.cooldown + 0.1
                this.nowSpellInfo.usesNum += 1 
                this.AfterSpellUsing(data)
                break;
            case PlayerHeroSpellFunction.AFTER_ENEMY_SPELL_USING :
                // print("触发玩家"+this.playerId+":"+"AFTER_ENEMY_SPELL_USING")
                return this.AfterEnemySpellUsing(data)
            case PlayerHeroSpellFunction.CARD_INIT :
                // print("触发玩家"+this.playerId+":"+"CARD_INIT")
                this.OnCardInit(data)
                break;
            case PlayerHeroSpellFunction.AFTER_CARD_USING :
                // print("触发玩家"+this.playerId+":"+"AFTER_CARD_USING")
                this.AfterCardUsing(data)
                break;
            case PlayerHeroSpellFunction.AFTER_ENEMY_CARD_USING :
                // print("触发玩家"+this.playerId+":"+"AFTER_ENEMY_CARD_USING")
                return this.AfterEnemyCardUsing(data)
        }
        return this.nowSpellInfo
    }

    GetBuff (type:PlayerHeroSpellFunction, data:any):ModifierData[] {
        switch (type) {
            case PlayerHeroSpellFunction.CARD_BUFF :
                return this.GetCardBuff(data)
            case PlayerHeroSpellFunction.ENEMY_CARD_DEBUFF :
                return this.GetEnemyCardBuff(data)
            case PlayerHeroSpellFunction.TOWER_BUFF :
                return this.GetTowerBuff(data)
            case PlayerHeroSpellFunction.ENEMY_TOWER_DEBUFF :
                return this.GetEnemyTowerBuff(data)
        }
        return []
    }

    checkCooldown () : boolean {
        return GameRules.GetGameTime() > this.nowSpellInfo.nextUsingTime
    }

    getAllInvolvedCardInfo():number[] {
        return this.spellInfo.CardIndex
    }
    
    AfterSpellUsing(data: any): void {};
    AfterEnemySpellUsing(data: any): PlayerHeroForClient { return data.info};
    AfterCardUsing(data: any): void {};
    AfterEnemyCardUsing(data: any): PlayerHeroForClient { return data.info};
    OnCardInit(data: any): void {};

    GetCardBuff(data: any):ModifierData[] { return []}
    GetEnemyCardBuff(data: any):ModifierData[] { return []}
    GetTowerBuff(data: any):ModifierData[] { return []}
    GetEnemyTowerBuff(data: any):ModifierData[] { return []}

}