import { modifier_fw_luna_lunar_grace_aura } from "../../modifiers/abilities/unit_spell/modifier_fw_luna_lunar_grace_aura";
import { modifier_fw_shadow_shaman_shackles } from "../../modifiers/abilities/unit_spell/modifier_fw_shadow_shaman_shackles";
import { BaseAbility, registerAbility } from "../../utils/dota_ts_adapter";

export class fw_shadow_shaman_shackles extends BaseAbility {

    tar:CDOTA_BaseNPC;
    OnSpellStart(): void {
        let unit = this.GetCaster()
        unit.EmitSound("Hero_ShadowShaman.Shackles.Cast")
        this.tar = this.GetCursorTarget()
        let duration = this.GetSpecialValueFor("duration")
        this.tar.AddNewModifier(unit, this, modifier_fw_shadow_shaman_shackles.name, {duration:duration})
    }

    GetChannelTime(): number {
        return this.GetSpecialValueFor("duration")
    }

    OnChannelFinish(interrupted: boolean): void {
        this.tar.RemoveModifierByNameAndCaster(modifier_fw_shadow_shaman_shackles.name, this.GetCaster())
    }

    Precache(context: CScriptPrecacheContext): void {
       PrecacheResource("particle","particles/status_fx/status_effect_shaman_shackle.vpcf",context)
       PrecacheResource("particle","particles/units/heroes/hero_shadowshaman/shadowshaman_shackle.vpcf",context)
    }
}

