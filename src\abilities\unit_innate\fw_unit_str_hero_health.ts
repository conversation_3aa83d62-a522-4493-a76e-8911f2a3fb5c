import { modifier_fw_unit_str_hero_health } from "../../modifiers/abilities/unit_innate/modifier_fw_unit_str_hero_health";
import { BaseAbility, registerAbility } from "../../utils/dota_ts_adapter";

export class fw_unit_str_hero_health extends BaseAbility {
    
    GetIntrinsicModifierName(): string {
        return modifier_fw_unit_str_hero_health.name
    }

    Precache(context: CScriptPrecacheContext): void {
       
    }
}

