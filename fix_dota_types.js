const fs = require('fs');
const path = require('path');

// 修复 Dota 类型文件中的接口问题
const apiFilePath = path.join(__dirname, 'node_modules/@moddota/dota-lua-types/types/api.generated.d.ts');

if (fs.existsSync(apiFilePath)) {
    let content = fs.readFileSync(apiFilePath, 'utf8');
    
    // 在有问题的接口声明前添加 @ts-ignore
    content = content.replace(
        /declare interface CDOTA_Ability_Lua extends CDOTABaseAbility \{/g,
        '// @ts-ignore\ndeclare interface CDOTA_Ability_Lua extends CDOTABaseAbility {'
    );
    
    content = content.replace(
        /declare interface CDOTA_Item_Lua extends CDOTA_Item \{/g,
        '// @ts-ignore\ndeclare interface CDOTA_Item_Lua extends CDOTA_Item {'
    );
    
    fs.writeFileSync(apiFilePath, content, 'utf8');
    console.log('Fixed Dota API types');
} else {
    console.log('Dota API types file not found');
}
