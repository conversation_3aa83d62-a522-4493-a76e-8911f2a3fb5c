"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("react");
var react_panorama_x_1 = require("react-panorama-x");
var GameStore = (function () {
    function GameStore() {
        this.state = {
            currentTurn: 1,
            playerMana: 10,
            playerHealth: 30,
            handCards: [],
            selectedCard: undefined,
            hoveredCard: undefined,
            draggedCard: undefined,
        };
        this.listeners = [];
    }
    GameStore.prototype.getState = function () {
        return __assign({}, this.state);
    };
    GameStore.prototype.setState = function (updater) {
        updater(this.state);
        this.notifyListeners();
    };
    GameStore.prototype.subscribe = function (listener) {
        var _this = this;
        this.listeners.push(listener);
        return function () {
            var index = _this.listeners.indexOf(listener);
            if (index > -1) {
                _this.listeners.splice(index, 1);
            }
        };
    };
    GameStore.prototype.notifyListeners = function () {
        this.listeners.forEach(function (listener) { return listener(); });
    };
    GameStore.prototype.setPlayerMana = function (mana) {
        this.setState(function (state) {
            state.playerMana = mana;
        });
    };
    GameStore.prototype.setPlayerHealth = function (health) {
        this.setState(function (state) {
            state.playerHealth = health;
        });
    };
    GameStore.prototype.addCardToHand = function (card) {
        this.setState(function (state) {
            state.handCards.push(card);
        });
    };
    GameStore.prototype.removeCardFromHand = function (cardIndex) {
        this.setState(function (state) {
            state.handCards.splice(cardIndex, 1);
        });
    };
    GameStore.prototype.playCard = function (cardIndex) {
        this.setState(function (state) {
            var card = state.handCards[cardIndex];
            if (card && state.playerMana >= card.LayCost) {
                state.playerMana -= card.LayCost;
                state.handCards.splice(cardIndex, 1);
                GameEvents.SendCustomGameEventToServer('play_card', {
                    cardName: card.Name,
                    cardIndex: cardIndex
                });
            }
        });
    };
    GameStore.prototype.selectCard = function (card) {
        this.setState(function (state) {
            state.selectedCard = card;
        });
    };
    GameStore.prototype.hoverCard = function (card) {
        this.setState(function (state) {
            state.hoveredCard = card;
        });
    };
    GameStore.prototype.startDragCard = function (card) {
        this.setState(function (state) {
            state.draggedCard = card;
        });
    };
    GameStore.prototype.endDragCard = function () {
        this.setState(function (state) {
            state.draggedCard = undefined;
        });
    };
    GameStore.prototype.initializeGame = function () {
        this.setState(function (state) {
            state.currentTurn = 1;
            state.playerMana = 10;
            state.playerHealth = 30;
            state.handCards = [
                {
                    Name: 'test_unit_1',
                    ChName: '测试单位',
                    LayCost: 3,
                    CardType: 'unit',
                    CardUIType: 'creep',
                    Enable: 1,
                    EnableForDeckCards: 1,
                    UsingAreaLimit: 0,
                    UnitCardInfo: {
                        Delay: 2,
                        LayType: 'lay_row',
                        CoverTemplate: 0,
                        LayGroupNum: 1,
                        Template1: 'npc_dota_creep_goodguys_melee',
                        Num1: 2,
                    },
                    SpellCardInfo: {},
                    ExtraData: {},
                    BotCardAI: { CardTag: 'test' },
                },
                {
                    Name: 'test_spell_1',
                    ChName: '测试法术',
                    LayCost: 5,
                    CardType: 'spell',
                    CardUIType: 'spell',
                    Enable: 1,
                    EnableForDeckCards: 1,
                    UsingAreaLimit: 0,
                    UnitCardInfo: {
                        Delay: 0,
                        LayType: 'lay_row',
                        CoverTemplate: 0,
                        LayGroupNum: 1,
                        Template1: '',
                        Num1: 1,
                    },
                    SpellCardInfo: {
                        SpellTemplate: 'fireball_spell',
                    },
                    ExtraData: {},
                    BotCardAI: { CardTag: 'spell' },
                },
                {
                    Name: 'test_hero_1',
                    ChName: '测试英雄',
                    LayCost: 7,
                    CardType: 'unit',
                    CardUIType: 'hero',
                    Enable: 1,
                    EnableForDeckCards: 1,
                    UsingAreaLimit: 0,
                    UnitCardInfo: {
                        Delay: 3,
                        LayType: 'lay_row',
                        CoverTemplate: 0,
                        LayGroupNum: 1,
                        Template1: 'npc_dota_hero_pudge',
                        Num1: 1,
                    },
                    SpellCardInfo: {},
                    ExtraData: {},
                    BotCardAI: { CardTag: 'hero' },
                },
            ];
            state.selectedCard = undefined;
            state.hoveredCard = undefined;
            state.draggedCard = undefined;
        });
    };
    return GameStore;
}());
var gameStore = new GameStore();
function useGameStore() {
    var _a = (0, react_1.useState)(gameStore.getState()), state = _a[0], setState = _a[1];
    (0, react_1.useEffect)(function () {
        var unsubscribe = gameStore.subscribe(function () {
            setState(gameStore.getState());
        });
        return unsubscribe;
    }, []);
    return __assign(__assign({}, state), { setPlayerMana: gameStore.setPlayerMana.bind(gameStore), setPlayerHealth: gameStore.setPlayerHealth.bind(gameStore), addCardToHand: gameStore.addCardToHand.bind(gameStore), removeCardFromHand: gameStore.removeCardFromHand.bind(gameStore), playCard: gameStore.playCard.bind(gameStore), selectCard: gameStore.selectCard.bind(gameStore), hoverCard: gameStore.hoverCard.bind(gameStore), startDragCard: gameStore.startDragCard.bind(gameStore), endDragCard: gameStore.endDragCard.bind(gameStore), initializeGame: gameStore.initializeGame.bind(gameStore) });
}
var CardComponent = function (_a) {
    var card = _a.card, index = _a.index, isPlayable = _a.isPlayable, isSelected = _a.isSelected, isHovered = _a.isHovered, onPlay = _a.onPlay, onSelect = _a.onSelect, onHover = _a.onHover, onDragStart = _a.onDragStart, onDragEnd = _a.onDragEnd;
    var handleClick = function () {
        onSelect(card);
        if (isPlayable) {
            onPlay(index);
        }
    };
    var handleMouseEnter = function () {
        onHover(card);
    };
    var handleMouseLeave = function () {
        onHover(undefined);
    };
    var handleDragStart = function () {
        onDragStart(card);
    };
    var handleDragEnd = function () {
        onDragEnd();
    };
    var getCardTypeIcon = function () {
        switch (card.CardUIType) {
            case 'hero': return '👤';
            case 'creep': return '🐾';
            case 'building': return '🏢';
            case 'spell': return '✨';
            default: return '❓';
        }
    };
    var cardClasses = [
        'card',
        "card--".concat(card.CardType),
        "card--".concat(card.CardUIType),
        isSelected && 'card--selected',
        isHovered && 'card--hovered',
        isPlayable && 'card--playable',
        !isPlayable && 'card--unplayable'
    ].filter(Boolean).join(' ');
    return (react_1.default.createElement(Panel, { className: cardClasses, onactivate: handleClick, onmouseover: handleMouseEnter, onmouseout: handleMouseLeave, draggable: isPlayable },
        react_1.default.createElement(Panel, { className: "card__header" },
            react_1.default.createElement(Label, { className: "card__cost", text: card.LayCost.toString() }),
            react_1.default.createElement(Label, { className: "card__type-icon", text: getCardTypeIcon() })),
        react_1.default.createElement(Panel, { className: "card__body" },
            react_1.default.createElement(Panel, { className: "card__image" },
                react_1.default.createElement(Panel, { className: "card__placeholder-image" },
                    react_1.default.createElement(Label, { text: getCardTypeIcon() }))),
            react_1.default.createElement(Panel, { className: "card__info" },
                react_1.default.createElement(Label, { className: "card__name", text: card.ChName }),
                react_1.default.createElement(Label, { className: "card__description", text: card.CardType === 'unit'
                        ? card.UnitCardInfo.Template1
                        : card.SpellCardInfo.SpellTemplate || '' }))),
        card.CardType === 'unit' && (react_1.default.createElement(Panel, { className: "card__stats" },
            card.UnitCardInfo.Num1 > 1 && (react_1.default.createElement(Label, { className: "card__count", text: "\u00D7".concat(card.UnitCardInfo.Num1) })),
            card.UnitCardInfo.Delay && (react_1.default.createElement(Label, { className: "card__delay", text: "\u23F1".concat(card.UnitCardInfo.Delay, "s") })))),
        react_1.default.createElement(Panel, { className: "card__footer" },
            react_1.default.createElement(Label, { className: "card__rarity", text: card.CardUIType }))));
};
var FastWarApp = function () {
    var _a = useGameStore(), currentTurn = _a.currentTurn, playerMana = _a.playerMana, playerHealth = _a.playerHealth, handCards = _a.handCards, selectedCard = _a.selectedCard, hoveredCard = _a.hoveredCard, setPlayerMana = _a.setPlayerMana, setPlayerHealth = _a.setPlayerHealth, addCardToHand = _a.addCardToHand, playCard = _a.playCard, selectCard = _a.selectCard, hoverCard = _a.hoverCard, startDragCard = _a.startDragCard, endDragCard = _a.endDragCard, initializeGame = _a.initializeGame;
    (0, react_1.useEffect)(function () {
        initializeGame();
        var gameEventListeners = [
            GameEvents.Subscribe('player_mana_changed', function (event) {
                setPlayerMana(event.mana);
            }),
            GameEvents.Subscribe('player_health_changed', function (event) {
                setPlayerHealth(event.health);
            }),
            GameEvents.Subscribe('card_added_to_hand', function (event) {
                addCardToHand(event.card);
            }),
        ];
        var netTableListener = CustomNetTables.SubscribeNetTableListener('game_state', function (tableName, key, value) {
            if (key === 'player_mana') {
                setPlayerMana(value);
            }
            else if (key === 'player_health') {
                setPlayerHealth(value);
            }
        });
        return function () {
            gameEventListeners.forEach(function (id) { return GameEvents.Unsubscribe(id); });
            CustomNetTables.UnsubscribeNetTableListener(netTableListener);
        };
    }, []);
    var handleCardPlay = function (cardIndex) {
        playCard(cardIndex);
    };
    var handleCardSelect = function (card) {
        selectCard((selectedCard === null || selectedCard === void 0 ? void 0 : selectedCard.Name) === card.Name ? undefined : card);
    };
    var handleCardHover = function (card) {
        hoverCard(card);
    };
    var handleCardDragStart = function (card) {
        startDragCard(card);
    };
    var handleCardDragEnd = function () {
        endDragCard();
    };
    return (react_1.default.createElement(Panel, { className: "fast-war-app" },
        react_1.default.createElement(Panel, { className: "game-info" },
            react_1.default.createElement(Panel, { className: "player-stats" },
                react_1.default.createElement(Panel, { className: "stat" },
                    react_1.default.createElement(Label, { className: "stat__label", text: "\u56DE\u5408" }),
                    react_1.default.createElement(Label, { className: "stat__value", text: currentTurn.toString() })),
                react_1.default.createElement(Panel, { className: "stat" },
                    react_1.default.createElement(Label, { className: "stat__label", text: "\u6CD5\u529B" }),
                    react_1.default.createElement(Label, { className: "stat__value", text: playerMana.toString() })),
                react_1.default.createElement(Panel, { className: "stat" },
                    react_1.default.createElement(Label, { className: "stat__label", text: "\u751F\u547D" }),
                    react_1.default.createElement(Label, { className: "stat__value", text: playerHealth.toString() })))),
        react_1.default.createElement(Panel, { className: "battlefield" },
            react_1.default.createElement(Panel, { className: "battlefield__content" },
                react_1.default.createElement(Label, { text: "\u6218\u573A" }),
                react_1.default.createElement(Label, { text: "\u62D6\u62FD\u5361\u724C\u5230\u6B64\u5904\u4F7F\u7528" }))),
        react_1.default.createElement(Panel, { className: "hand-area" },
            react_1.default.createElement(Label, { text: "\u624B\u724C (".concat(handCards.length, ")") }),
            react_1.default.createElement(Panel, { className: "hand-cards" }, handCards.map(function (card, index) { return (react_1.default.createElement(CardComponent, { key: "".concat(card.Name, "-").concat(index), card: card, index: index, isPlayable: playerMana >= card.LayCost, isSelected: (selectedCard === null || selectedCard === void 0 ? void 0 : selectedCard.Name) === card.Name, isHovered: (hoveredCard === null || hoveredCard === void 0 ? void 0 : hoveredCard.Name) === card.Name, onPlay: handleCardPlay, onSelect: handleCardSelect, onHover: handleCardHover, onDragStart: handleCardDragStart, onDragEnd: handleCardDragEnd })); }))),
        selectedCard && (react_1.default.createElement(Panel, { className: "card-detail" },
            react_1.default.createElement(Label, { text: "\u5361\u724C\u8BE6\u60C5" }),
            react_1.default.createElement(Panel, { className: "card-detail__content" },
                react_1.default.createElement(Label, { text: selectedCard.ChName }),
                react_1.default.createElement(Label, { text: "\u8D39\u7528: ".concat(selectedCard.LayCost) }),
                react_1.default.createElement(Label, { text: "\u7C7B\u578B: ".concat(selectedCard.CardUIType) }),
                react_1.default.createElement(Label, { text: "\u5185\u90E8\u540D\u79F0: ".concat(selectedCard.Name) }),
                selectedCard.CardType === 'unit' && (react_1.default.createElement(react_1.default.Fragment, null,
                    react_1.default.createElement(Label, { text: "\u6A21\u677F: ".concat(selectedCard.UnitCardInfo.Template1) }),
                    react_1.default.createElement(Label, { text: "\u6570\u91CF: ".concat(selectedCard.UnitCardInfo.Num1) }),
                    react_1.default.createElement(Label, { text: "\u5EF6\u8FDF: ".concat(selectedCard.UnitCardInfo.Delay, "s") }))),
                selectedCard.CardType === 'spell' && (react_1.default.createElement(Label, { text: "\u6CD5\u672F: ".concat(selectedCard.SpellCardInfo.SpellTemplate || '') })))))));
};
function initializeFastWar() {
    var rootPanel = $.GetContextPanel();
    var targetPanel = rootPanel.FindChildInLayoutFile('FastWarApp');
    if (!targetPanel) {
        targetPanel = $.CreatePanel('Panel', rootPanel, 'FastWarApp');
        targetPanel.AddClass('fast-war-app');
    }
    (0, react_panorama_x_1.render)(react_1.default.createElement(FastWarApp, null), targetPanel);
    targetPanel.SetHasClass('hidden', false);
    console.log('Fast War UI initialized successfully');
}
(function () {
    if ($.GetContextPanel()) {
        initializeFastWar();
    }
    else {
        $.Schedule(0.1, initializeFastWar);
    }
})();
