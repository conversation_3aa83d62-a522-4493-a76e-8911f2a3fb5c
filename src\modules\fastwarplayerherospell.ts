import { Player<PERSON><PERSON>Assassin } from "../playerhero/sp_assassin";
import { PlayerHeroPriest } from "../playerhero/sp_priest";
import { PlayerHeroDefault} from "../playerhero/sp_default";
import { MathUtils } from "../utils/math_utils";
import { PlayerHeroSaint } from "../playerhero/sp_saint";
import { PlayerHeroSpellHandle } from "../playerhero/playerherospellhandle";
import { PlayerHeroPassive } from "../playerhero/sp_passive";


export class FastWarPlayerHeroSpell {

    /**
     * 玩家当前配置出战英雄牌
     */
    private PlayerDeckHero:{
        [key:string]:{
            heroName:string,
            spellHandle?:PlayerHeroSpellHandle,
        }
    } = {}
    /**
     * 玩家当前库存英雄牌
     */
    private PlayerReserveHeros:{
        [key:string]:string[]
    } = {}

    public initPlayerHeroNetTable (playerId:PlayerID) {
        if (playerId == -1) {
            return;
        }
        let p = playerId.toString();
        let player_hero = CustomNetTables.GetTableValue("player_hero","player_hero");
        if (player_hero == undefined) {
            player_hero = {changedPlayer:-1,deck:{},reserve:{}};
        }
        let dp = GameRules.KVUtils.getFixDPlayerHero(playerId)
        let rp = GameRules.KVUtils.getFixRPlayerHero(playerId)
        this.PlayerDeckHero[p] = {
            heroName:dp
        }
        this.PlayerReserveHeros[p] = [dp,...rp]
        player_hero.changedPlayer = playerId
        player_hero.deck[p] = dp
        player_hero.reserve[p] = this.PlayerReserveHeros[p]
        CustomNetTables.SetTableValue("player_hero","player_hero", player_hero);
    }
     
    public changePlayerHero (playerId:PlayerID, heroName:string) {
        if (playerId == -1) {
            return;
        }
        let p = playerId.toString();
        let player_hero = CustomNetTables.GetTableValue("player_hero","player_hero");
        if (player_hero == undefined) {
            player_hero = {changedPlayer:-1,deck:{},reserve:{}};
        }
        this.PlayerDeckHero[p] = {
            heroName:heroName,
        }
        player_hero.changedPlayer = playerId
        player_hero.deck[p] = heroName
        CustomNetTables.SetTableValue("player_hero","player_hero", player_hero);
    }
        
    public randomPlayerHero (playerId:PlayerID) {
        if (playerId == -1) {
            return;
        }
        let p = playerId.toString();
        if (this.PlayerReserveHeros[p].length < 2) {
            return 
        }
        let heroName = MathUtils.getRandomElements(this.PlayerReserveHeros[p],1)[0]
        this.changePlayerHero(playerId, heroName)
    }

    public getPlayerDHero (playerId:PlayerID) {
        if (playerId == -1) {
            return;
        }
        let p = playerId.toString();
        return this.PlayerDeckHero[p].heroName
    }

    constructor() {
        
    }

    public registerAllPlayerSpell () {
        for (const id of GameRules.PlayerIDs) {
            this.registerPlayerSpell(id)
        }
    }

    public registerPlayerSpell (playerId:PlayerID) {
        let p = playerId.toString();
        let info = GameRules.KVUtils.getPlayerHeroInfo(this.PlayerDeckHero[p].heroName)
        switch (info.SpellName) {
            case "fw_hero_spell_assassin"://技能槽位为最左边的手牌
                this.PlayerDeckHero[p].spellHandle = new PlayerHeroAssassin(playerId,info)
                break;
            case "fw_hero_spell_priest"://每用三张牌，下一张获得buff
                this.PlayerDeckHero[p].spellHandle = new PlayerHeroPriest(playerId,info)
                break;
            case "fw_hero_spell_saint"://三个技能切换
                this.PlayerDeckHero[p].spellHandle = new PlayerHeroSaint(playerId,info)
                break;
            case "fw_hero_spell_blacksmith"://纯被动无cd
                this.PlayerDeckHero[p].spellHandle = new PlayerHeroPassive(playerId,info)
                break;
            default:
                this.PlayerDeckHero[p].spellHandle = new PlayerHeroDefault(playerId,info)
                break;
        }
        // if (info.CardIndex.length == 0) {
            let hero = PlayerResource.GetPlayer(playerId).GetAssignedHero()
            if (hero.FindAbilityByName(info.SpellName) == undefined) {
                let ab = hero.AddAbility(info.SpellName)
                ab.SetLevel(1)
                ab.SetActivated(true)
                GameRules.preCacheAbs.push(ab)
            }
        // }
    }

    public clearPlayerSpell () {
        for (const pid of GameRules.PlayerIDs) {
            let p = pid.toString()
            if (this.PlayerDeckHero[p].spellHandle != undefined) {
                this.PlayerDeckHero[p].spellHandle.ClearSpell()
            }
        }
    }

    public getAllInvolvedCardInfoForPrecache (playerId:PlayerID):number[] {
        let p = playerId.toString();
        if (this.PlayerDeckHero[p] != undefined && this.PlayerDeckHero[p].spellHandle != undefined) {
            return this.PlayerDeckHero[p].spellHandle.getAllInvolvedCardInfo()
        }   
        return []
    }
    
    public getSpellHandle (playerId:PlayerID):PlayerHeroSpellHandle {
        return this.PlayerDeckHero[playerId.toString()].spellHandle
    }

    public checkCooldown (playerId:PlayerID):boolean {
        let p = playerId.toString();
        if (this.PlayerDeckHero[p] != undefined && this.PlayerDeckHero[p].spellHandle != undefined) {
            return this.PlayerDeckHero[p].spellHandle.checkCooldown()
        } 
        return false
    }

    public getSpellNowInfo (playerId:PlayerID,type:PlayerHeroSpellFunction, data:any) {
        return this.PlayerDeckHero[playerId.toString()].spellHandle.GetNowHeroSpellInfo(type,data)
    }

    public getCardUsingBuff (playerId:PlayerID,type:PlayerHeroSpellFunction, data:any):ModifierData[] {
        return this.PlayerDeckHero[playerId.toString()].spellHandle.GetBuff(type,data)
    }

}


