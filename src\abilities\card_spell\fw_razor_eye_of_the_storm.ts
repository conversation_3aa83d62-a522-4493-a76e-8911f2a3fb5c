import { BaseAbility, registerAbility } from "../../utils/dota_ts_adapter";

export class fw_razor_eye_of_the_storm extends BaseAbility {
    
    spell:SpellInfoKV;
    GetAOERadius(): number {
        return this.spell.AOERadius
    }

    OnSpellStart(): void {
        let duration = this.GetSpecialValueFor("duration")
        let damage_interval = this.GetSpecialValueFor("damage_interval")
        let damage = this.GetSpecialValueFor("damage")
        let move_speed = this.GetSpecialValueFor("move_speed")

        let hero = this.GetCaster()
        let ab = this
        let team = hero.GetTeam()
        let tarPos = this.GetCursorPosition()
        let moveV = Vector(0,-move_speed*0.03,0).__mul(hero.fastWarIsGood?-1:1)
        let radius = this.GetAOERadius()
        let tarTeam = this.GetAbilityTargetTeam()
        let tarFlag = this.GetAbilityTargetFlags()
        let damageType = this.GetAbilityDamageType()
        let fwTargetType = this.spell.FWTargetType
        let e = GameRules.SoundUtils.getSoundEntity(tarPos)
        
        let par = ParticleManager.CreateParticle("particles/spell/razor_eye_of_the_storm/razor_rain_storm.vpcf", ParticleAttachment_t.PATTACH_WORLDORIGIN, undefined)
        ParticleManager.SetParticleControl(par, 0, tarPos)
        ParticleManager.SetParticleControl(par, 1, Vector(radius,0,0))
        
        let timer = Timers.CreateTimer(0.03,()=>{
            tarPos = GetGroundPosition(tarPos.__add(moveV), undefined)
            ParticleManager.SetParticleControl(par, 0, tarPos)
            e.SetAbsOrigin(tarPos.__sub(Vector(0,0,70)))
            return 0.03
        })

        GameRules.FastWarSpell.startIntervalSpell(0,duration,damage_interval,
        ()=>{
            e.EmitSound("Fw.Cards.Spell.eye_of_the_storm.cast")
            e.EmitSound("Fw.Cards.Spell.eye_of_the_storm.loop")
        },()=>{
            let tars = FindUnitsInRadius(
                team,
                tarPos,
                undefined,
                radius,
                tarTeam,
                1 + 2 + 4,
                tarFlag,
                0,
                false,
            )
            if (tars.length > 0) {
                let h = 9999;
                let tar = undefined;
                for (const unit of tars) {
                    if (unit.fastWarUnitType != undefined && (fwTargetType == GoFastWarAIUnitTypeEnum.ALL || unit.fastWarUnitType == fwTargetType) 
                        && unit.GetHealth() < h) {
                       tar = unit
                       h = unit.GetHealth()
                    }
                }
                let par1 = ParticleManager.CreateParticle("particles/units/heroes/hero_razor/razor_storm_lightning_strike.vpcf", ParticleAttachment_t.PATTACH_WORLDORIGIN, undefined)
                ParticleManager.SetParticleControl(par1, 0, tar.GetAbsOrigin())
                ParticleManager.SetParticleControl(par1, 1, tarPos.__add(Vector(0,0,500)))
                ApplyDamage({
                    victim: tar,
                    attacker: hero,
                    damage: damage,
                    damage_type: damageType,
                    damage_flags:DOTADamageFlag_t.DOTA_DAMAGE_FLAG_NONE,
                    ability:ab,
                });
                e.EmitSound("Fw.Cards.Spell.eye_of_the_storm.impact")
            }
        },()=>{
            e.StopSound("Fw.Cards.Spell.eye_of_the_storm.cast")
            e.StopSound("Fw.Cards.Spell.eye_of_the_storm.loop")
            GameRules.SoundUtils.backSoundEntity(e)
            ParticleManager.DestroyParticle(par, false)
            Timers.RemoveTimer(timer)
        })
    }

    Spawn(): void {
        this.spell = GameRules.KVUtils.getSpellInfo("fw_razor_eye_of_the_storm")
    }
    
    Precache(context: CScriptPrecacheContext): void {
        PrecacheResource("particle","particles/units/heroes/hero_razor/razor_storm_lightning_strike.vpcf",context)
        PrecacheResource("particle","particles/spell/razor_eye_of_the_storm/razor_rain_storm.vpcf",context)
    }
}

