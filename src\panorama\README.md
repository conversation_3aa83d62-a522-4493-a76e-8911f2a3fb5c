# Fast War Panorama UI - TypeScript Version

这个目录包含了 Fast War 游戏的 Panorama UI 的 TypeScript 源代码。

## 文件结构

- `fast_war.tsx` - 主要的 React 组件和游戏逻辑
- `tsconfig.json` - TypeScript 编译配置

## 功能特性

- **React + TypeScript**: 使用现代的 React 和 TypeScript 开发
- **React Panorama**: 使用 react-panorama-x 库将 React 组件渲染到 Panorama
- **状态管理**: 自定义的游戏状态管理系统
- **卡牌系统**: 完整的卡牌显示和交互功能
- **游戏事件**: 与服务器端的游戏事件通信
- **NetTable 集成**: 支持 Dota 2 的 NetTable 数据同步

## 主要组件

### GameStore
游戏状态管理类，负责：
- 管理游戏状态（回合、法力、生命值等）
- 管理手牌和选中的卡牌
- 处理卡牌操作（播放、选择、悬停等）
- 与服务器端通信

### CardComponent
卡牌显示组件，支持：
- 显示卡牌信息（名称、费用、类型等）
- 交互功能（点击、悬停、拖拽）
- 视觉状态（选中、悬停、可播放等）

### FastWarApp
主应用组件，包含：
- 游戏信息栏（回合、法力、生命值）
- 战场区域
- 手牌区域
- 卡牌详情面板

## 构建和开发

### 开发模式
```bash
npm run dev:panorama
```

### 生产构建
```bash
npm run build:panorama
```

编译后的 JavaScript 文件会输出到 `content/panorama/layout/custom_game/hud/` 目录。

## 与服务器端通信

### 游戏事件
- `play_card`: 播放卡牌时发送给服务器
- `player_mana_changed`: 监听法力值变化
- `player_health_changed`: 监听生命值变化
- `card_added_to_hand`: 监听手牌添加

### NetTable
- `game_state`: 游戏状态数据表
  - `player_mana`: 玩家法力值
  - `player_health`: 玩家生命值

## 类型定义

所有的游戏数据类型都在文件顶部定义，包括：
- `Card`: 卡牌数据结构
- `GameState`: 游戏状态结构
- `CardProps`: 卡牌组件属性

## 注意事项

1. 确保安装了所需的依赖包：`react` 和 `react-panorama-x`
2. 编译前需要确保 TypeScript 配置正确
3. 与服务器端的事件名称和数据结构需要保持一致
4. Panorama 环境下的 React 使用有一些限制，需要使用 Panorama 特定的组件（如 `Panel`、`Label`）
