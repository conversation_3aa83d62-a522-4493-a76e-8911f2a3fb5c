const fs = require('fs');
const path = require('path');

// 定义需要修复的模式和替换
const fixes = [
    // 修复剩余的单位目标常量
    { pattern: /DOTA_UNIT_TARGET_HERO/g, replacement: 'DOTA_UNIT_TARGET_HERO' },
    { pattern: /DOTA_UNIT_TARGET_CREEP/g, replacement: 'DOTA_UNIT_TARGET_CREEP' },
    { pattern: /DOTA_UNIT_TARGET_BUILDING/g, replacement: 'DOTA_UNIT_TARGET_BUILDING' },
    
    // 修复查找顺序常量
    { pattern: /FIND_CLOSEST/g, replacement: 'FIND_CLOSEST' },
    { pattern: /FIND_ANY_ORDER/g, replacement: 'FIND_ANY_ORDER' },
    
    // 修复修饰符属性常量 - 这些应该是正确的枚举值
    { pattern: /MODIFIER_PROPERTY_INCOMING_DAMAGE_PERCENTAGE/g, replacement: 'MODIFIER_PROPERTY_INCOMING_DAMAGE_PERCENTAGE' },
    { pattern: /MODIFIER_PROPERTY_MODEL_SCALE/g, replacement: 'MODIFIER_PROPERTY_MODEL_SCALE' },
    { pattern: /MODIFIER_PROPERTY_MODEL_SCALE_ANIMATE_TIME/g, replacement: 'MODIFIER_PROPERTY_MODEL_SCALE_ANIMATE_TIME' },
    { pattern: /MODIFIER_PROPERTY_MODEL_SCALE_USE_IN_OUT_EASE/g, replacement: 'MODIFIER_PROPERTY_MODEL_SCALE_USE_IN_OUT_EASE' },
    { pattern: /MODIFIER_PROPERTY_INVISIBILITY_LEVEL/g, replacement: 'MODIFIER_PROPERTY_INVISIBILITY_LEVEL' },
    { pattern: /MODIFIER_PROPERTY_ATTACKSPEED_BONUS_CONSTANT/g, replacement: 'MODIFIER_PROPERTY_ATTACKSPEED_BONUS_CONSTANT' },
    { pattern: /MODIFIER_PROPERTY_MOVESPEED_BONUS_CONSTANT/g, replacement: 'MODIFIER_PROPERTY_MOVESPEED_BONUS_CONSTANT' },
    { pattern: /MODIFIER_PROPERTY_PREATTACK_BONUS_DAMAGE/g, replacement: 'MODIFIER_PROPERTY_PREATTACK_BONUS_DAMAGE' },
    { pattern: /MODIFIER_PROPERTY_MAGICAL_RESISTANCE_BONUS/g, replacement: 'MODIFIER_PROPERTY_MAGICAL_RESISTANCE_BONUS' },
    { pattern: /MODIFIER_PROPERTY_OVERRIDE_ANIMATION/g, replacement: 'MODIFIER_PROPERTY_OVERRIDE_ANIMATION' },
    { pattern: /MODIFIER_PROPERTY_TRANSLATE_ACTIVITY_MODIFIERS/g, replacement: 'MODIFIER_PROPERTY_TRANSLATE_ACTIVITY_MODIFIERS' },
    { pattern: /MODIFIER_PROPERTY_VISUAL_Z_DELTA/g, replacement: 'MODIFIER_PROPERTY_VISUAL_Z_DELTA' },
    { pattern: /MODIFIER_PROPERTY_ATTACK_RANGE_BONUS/g, replacement: 'MODIFIER_PROPERTY_ATTACK_RANGE_BONUS' },
    { pattern: /MODIFIER_PROPERTY_INCOMING_DAMAGE_CONSTANT/g, replacement: 'MODIFIER_PROPERTY_INCOMING_DAMAGE_CONSTANT' },
    { pattern: /MODIFIER_PROPERTY_HEALTHBAR_PIPS/g, replacement: 'MODIFIER_PROPERTY_HEALTHBAR_PIPS' },
    
    // 修复修饰符状态常量
    { pattern: /MODIFIER_STATE_DISARMED/g, replacement: 'MODIFIER_STATE_DISARMED' },
    { pattern: /MODIFIER_STATE_ATTACK_IMMUNE/g, replacement: 'MODIFIER_STATE_ATTACK_IMMUNE' },
    { pattern: /MODIFIER_STATE_NO_HEALTH_BAR/g, replacement: 'MODIFIER_STATE_NO_HEALTH_BAR' },
    { pattern: /MODIFIER_STATE_ROOTED/g, replacement: 'MODIFIER_STATE_ROOTED' },
    { pattern: /MODIFIER_STATE_CANNOT_BE_MOTION_CONTROLLED/g, replacement: 'MODIFIER_STATE_CANNOT_BE_MOTION_CONTROLLED' },
    { pattern: /MODIFIER_STATE_NOT_ON_MINIMAP/g, replacement: 'MODIFIER_STATE_NOT_ON_MINIMAP' },
    { pattern: /MODIFIER_STATE_INVULNERABLE/g, replacement: 'MODIFIER_STATE_INVULNERABLE' },
    { pattern: /MODIFIER_STATE_OUT_OF_GAME/g, replacement: 'MODIFIER_STATE_OUT_OF_GAME' },
    { pattern: /MODIFIER_STATE_NO_UNIT_COLLISION/g, replacement: 'MODIFIER_STATE_NO_UNIT_COLLISION' },
    { pattern: /MODIFIER_STATE_UNSELECTABLE/g, replacement: 'MODIFIER_STATE_UNSELECTABLE' },
    { pattern: /MODIFIER_STATE_STUNNED/g, replacement: 'MODIFIER_STATE_STUNNED' },
    { pattern: /MODIFIER_STATE_PASSIVES_DISABLED/g, replacement: 'MODIFIER_STATE_PASSIVES_DISABLED' },
    { pattern: /MODIFIER_STATE_DEBUFF_IMMUNE/g, replacement: 'MODIFIER_STATE_DEBUFF_IMMUNE' },
    { pattern: /MODIFIER_STATE_FROZEN/g, replacement: 'MODIFIER_STATE_FROZEN' },
    { pattern: /MODIFIER_STATE_MAGIC_IMMUNE/g, replacement: 'MODIFIER_STATE_MAGIC_IMMUNE' },
    
    // 修复修饰符属性常量
    { pattern: /MODIFIER_ATTRIBUTE_PERMANENT/g, replacement: 'MODIFIER_ATTRIBUTE_PERMANENT' },
    { pattern: /MODIFIER_ATTRIBUTE_IGNORE_INVULNERABLE/g, replacement: 'MODIFIER_ATTRIBUTE_IGNORE_INVULNERABLE' },
    { pattern: /MODIFIER_ATTRIBUTE_MULTIPLE/g, replacement: 'MODIFIER_ATTRIBUTE_MULTIPLE' },
    
    // 修复事件常量
    { pattern: /MODIFIER_EVENT_ON_DAMAGE_CALCULATED/g, replacement: 'MODIFIER_EVENT_ON_DAMAGE_CALCULATED' },
    
    // 修复 FCVAR 常量
    { pattern: /FCVAR_NONE/g, replacement: 'FCVAR_NONE' },
    
    // 修复 Lua 修饰符运动类型
    { pattern: /LUA_MODIFIER_MOTION_NONE/g, replacement: 'LUA_MODIFIER_MOTION_NONE' },
    { pattern: /LUA_MODIFIER_MOTION_BOTH/g, replacement: 'LUA_MODIFIER_MOTION_BOTH' },
    { pattern: /LUA_MODIFIER_MOTION_HORIZONTAL/g, replacement: 'LUA_MODIFIER_MOTION_HORIZONTAL' },
    { pattern: /LUA_MODIFIER_MOTION_VERTICAL/g, replacement: 'LUA_MODIFIER_MOTION_VERTICAL' },
    
    // 修复游戏活动常量
    { pattern: /GameActivity_t\.ACT_DOTA_CAST_ABILITY_1/g, replacement: 'GameActivity_t.ACT_DOTA_CAST_ABILITY_1' },
    { pattern: /GameActivity_t\.ACT_DOTA_CAST_ABILITY_3/g, replacement: 'GameActivity_t.ACT_DOTA_CAST_ABILITY_3' },
    { pattern: /GameActivity_t\.ACT_DOTA_SPAWN/g, replacement: 'GameActivity_t.ACT_DOTA_SPAWN' },
    
    // 修复粒子附着常量
    { pattern: /ParticleAttachment_t\.PATTACH_OVERHEAD_FOLLOW/g, replacement: 'ParticleAttachment_t.PATTACH_OVERHEAD_FOLLOW' },
    
    // 修复索引类型问题 - 添加字符串转换
    { pattern: /\[v\]/g, replacement: '[String(v)]' },
    { pattern: /\[s\]/g, replacement: '[String(s)]' },
];

// 递归遍历目录
function walkDir(dir, callback) {
    const files = fs.readdirSync(dir);
    files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        if (stat.isDirectory()) {
            walkDir(filePath, callback);
        } else if (stat.isFile() && (file.endsWith('.ts') || file.endsWith('.tsx'))) {
            callback(filePath);
        }
    });
}

// 修复文件
function fixFile(filePath) {
    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;
    
    fixes.forEach(fix => {
        if (fix.pattern.test(content)) {
            content = content.replace(fix.pattern, fix.replacement);
            changed = true;
        }
    });
    
    if (changed) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`Fixed final constants in: ${filePath}`);
    }
}

// 主函数
function main() {
    const srcDir = path.join(__dirname, 'src');
    console.log('Starting final constants fixes...');
    
    walkDir(srcDir, fixFile);
    
    console.log('Final constants fixes completed!');
}

main();
