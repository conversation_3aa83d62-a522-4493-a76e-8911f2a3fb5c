import { modifier_fw_tiny_tree_grab } from "../../modifiers/abilities/unit_spell/modifier_fw_tiny_tree_grab";
import { modifier_fw_tiny_tree_grab_hide } from "../../modifiers/abilities/unit_spell/modifier_fw_tiny_tree_grab_hide";
import { BaseAbility, registerAbility } from "../../utils/dota_ts_adapter";
import { MathUtils } from "../../utils/math_utils";

export class fw_tiny_tree_grab extends BaseAbility {
    

    GetCastPoint(): number {
        return 0.5
    }

    OnAbilityPhaseStart(): boolean {
        let hero = this.GetCaster()
        hero.EmitSound("Hero_Tiny.Tree.Grab")
        hero.StartGestureWithFade(GameActivity_t.ACT_DOTA_CAST_ABILITY_3, 0,0.1)
        //用于让小小做完完整动作
        hero.AddNewModifier(hero, this, modifier_fw_tiny_tree_grab_hide.name, {duration:1.3})
        return true
    }

    OnSpellStart(): void {
        let hero = this.GetCaster()
        hero.AddNewModifier(hero, this, modifier_fw_tiny_tree_grab.name, {})
    }

    spell:SpellInfoKV;
    Spawn(): void {
        this.spell = GameRules.KVUtils.getSpellInfo("fw_tiny_tree_grab")
        if (IsServer()) {
            GameRules.preCacheInfos.push(GameRules.KVUtils.getSpecialUnitInfoTreated(this.GetCaster().GetUnitName()).ExtraModel)
            PrecacheItemByNameAsync("item_for_precache", ()=>{
                // print("预载单位完成")
            })
        }
    }
    
    Precache(context: CScriptPrecacheContext): void {
        PrecacheResource('soundfile', "soundevents/game_sounds_heroes/game_sounds_tiny.vsndevts" , context);
        PrecacheResource("particle","particles/units/heroes/hero_tiny/tiny_craggy_cleave.vpcf",context)
    }
}

