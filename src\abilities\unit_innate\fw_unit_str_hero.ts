import { modifier_fw_unit_str_hero } from "../../modifiers/abilities/unit_innate/modifier_fw_unit_str_hero";
import { BaseAbility, registerAbility } from "../../utils/dota_ts_adapter";

export class fw_unit_str_hero extends BaseAbility {
    
    GetIntrinsicModifierName(): string {
        return modifier_fw_unit_str_hero.name
    }

    Precache(context: CScriptPrecacheContext): void {
       
    }
}

