{"version": 3, "file": "script.js", "sourceRoot": "", "sources": ["../../../../../src/panorama/hud/script.tsx"], "names": [], "mappings": "AAAA,OAAO,iCAAiC,CAAC;AACzC,OAAO,gCAAgC,CAAC;AAkExC,SAAS;AACT,IAAI,cAAc,GAAY,EAAE,CAAC;AACjC,IAAI,WAAW,GAAgB,EAAE,CAAC;AAClC,IAAI,KAAK,GAAG,CAAC,CAAC;AAEd,MAAM,CAAC,aAAa,GAAG,UAAU,SAAiB,EAAE,QAA0C;IAC5F,6BAA6B;IAC7B,IAAI,cAAc,CAAC,SAAS,CAAC,KAAK,SAAS,EAAE;QAC3C,cAAc,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;KAChC;IACD,cAAc,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC3C,CAAC,CAAC;AAEF,MAAM,CAAC,eAAe,GAAG,UAAU,SAAiB,EAAE,GAAW,EAAE,IAAS;IAC1E,IAAI,CAAC,GAAG,GAAG,CAAC;IACZ,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;QACZ,CAAC,GAAG,KAAK,EAAE,CAAC;KACb;IAED,IAAI,WAAW,CAAC,CAAC,CAAC,KAAK,SAAS,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;QAC5E,IAAI,WAAW,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;YAChC,WAAW,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;SACrB;QACD,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAE/B,MAAM,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC;QAC5C,6BAA6B;QAC7B,IAAI,SAAS,KAAK,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YACnD,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;gBAChC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;aACnB;SACF;KACF;IAED,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;QACd,WAAW,CAAC,CAAC,CAAC,GAAG,SAAgB,CAAC;KACnC;AACH,CAAC,CAAC;AAEF,MAAM,CAAC,SAAS,GAAG,UAAU,GAAW,EAAE,YAAyB,EAAE,UAAuB;IAC1F,MAAM,OAAO,GAAG,YAAY,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;IAC/C,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,IAAI,OAAO,EAAE,CAAC,CAAC;IAE7C,IAAI,YAAY,KAAK,SAAS,EAAE;QAC9B,IAAI,CAAC,UAAU,CAAC,IAAI,OAAO,EAAE,EAAE,GAAG,EAAE;YAClC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE;gBACvB,YAAY,EAAE,CAAC;aAChB;QACH,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;KACjB;IAED,IAAI,UAAU,KAAK,SAAS,EAAE;QAC5B,IAAI,CAAC,UAAU,CAAC,IAAI,OAAO,EAAE,EAAE,GAAG,EAAE;YAClC,UAAU,EAAE,CAAC;QACf,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;KACjB;AACH,CAAC,CAAC;AAEF,MAAM,CAAC,UAAU,GAAG,UAAU,OAAe;IAC3C,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAC9B,IAAI,GAAG,GAAG,EAAE,CAAC;IAEb,IAAI,OAAO,IAAI,EAAE,EAAE;QACjB,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC;QACvC,IAAI,OAAO,GAAG,EAAE,GAAG,OAAO,EAAE;YAC1B,OAAO,IAAI,CAAC,CAAC;SACd;QACD,OAAO,IAAI,OAAO,GAAG,EAAE,CAAC;QACxB,GAAG,IAAI,OAAO,GAAG,GAAG,CAAC;KACtB;SAAM;QACL,GAAG,IAAI,IAAI,CAAC;KACb;IAED,IAAI,OAAO,GAAG,CAAC,IAAI,OAAO,GAAG,EAAE,EAAE;QAC/B,GAAG,IAAI,GAAG,GAAG,OAAO,CAAC;KACtB;SAAM,IAAI,OAAO,KAAK,CAAC,EAAE;QACxB,GAAG,IAAI,IAAI,CAAC;KACb;SAAM;QACL,GAAG,IAAI,OAAO,CAAC;KAChB;IAED,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AAEF,UAAU;AACV,MAAM,CAAC,eAAe,GAAG,CAAC,CAAC;AAC3B,MAAM,CAAC,iBAAiB,GAAG,CAAC,CAAC;AAC7B,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;AACjB,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC;AACtB,MAAM,CAAC,SAAS,GAAG;IACjB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5B,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;CAClC,CAAC;AACF,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;AACxB,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC;AAC1B,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC,wBAAwB,CAAC,MAAM,CAAC,KAAiB,CAAC,CAAC;AAC5E,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC;AAC7B,MAAM,CAAC,SAAS,GAAG,EAAE,CAAC;AACtB,MAAM,CAAC,YAAY,GAAG,EAAE,CAAC;AACzB,MAAM,CAAC,iBAAiB,GAAG,IAAI,CAAC;AAChC,MAAM,CAAC,QAAQ,GAAG;IAChB,OAAO,EAAE,EAAE;IACX,IAAI,EAAE,CAAC;IACP,KAAK,EAAE,CAAC;IACR,IAAI,EAAE,CAAC;CACR,CAAC;AACF,MAAM,CAAC,iBAAiB,GAAG,EAAE,CAAC;AAC9B,MAAM,CAAC,UAAU,GAAG;IAClB,QAAQ,EAAE,EAAE;IACZ,SAAS,EAAE,GAAG;IACd,cAAc,EAAE,CAAC,CAAC;IAClB,SAAS,EAAE,EAAE;IACb,IAAI,EAAE,CAAC;IACP,aAAa,EAAE,CAAC,CAAC;IACjB,QAAQ,EAAE,CAAC;IACX,GAAG,EAAE,EAAE;IACP,OAAO,EAAE,CAAC;CACX,CAAC;AACF,MAAM,CAAC,eAAe,GAAG,CAAC,CAAC;AAC3B,MAAM,CAAC,QAAQ,GAAG,KAAK,CAAC;AACxB,MAAM,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;AAE1B,aAAa;AACb,MAAM,mBAAmB,GAAG;IAC1B,sBAAsB,CAAC,6BAA6B;IACpD,sBAAsB,CAAC,0BAA0B;IACjD,sBAAsB,CAAC,gCAAgC;IACvD,sBAAsB,CAAC,kCAAkC;IACzD,sBAAsB,CAAC,oCAAoC;IAC3D,sBAAsB,CAAC,iCAAiC;IACxD,sBAAsB,CAAC,6BAA6B;IACpD,sBAAsB,CAAC,uBAAuB;IAC9C,sBAAsB,CAAC,8BAA8B;IACrD,sBAAsB,CAAC,+BAA+B;IACtD,sBAAsB,CAAC,8BAA8B;IACrD,sBAAsB,CAAC,+BAA+B;IACtD,sBAAsB,CAAC,kCAAkC;IACzD,sBAAsB,CAAC,iCAAiC;IACxD,sBAAsB,CAAC,iCAAiC;IACxD,sBAAsB,CAAC,8BAA8B;IACrD,sBAAsB,CAAC,mCAAmC;IAC1D,sBAAsB,CAAC,gCAAgC;IACvD,sBAAsB,CAAC,iCAAiC;IACxD,sBAAsB,CAAC,2BAA2B;IAClD,sBAAsB,CAAC,uBAAuB;IAC9C,sBAAsB,CAAC,2BAA2B;IAClD,sBAAsB,CAAC,+BAA+B;CACvD,CAAC;AAEF,aAAa;AACb,mBAAmB,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;IACpC,MAAM,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAC7C,CAAC,CAAC,CAAC"}