import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";


export class modifier_fw_unit_delay_hero extends BaseModifier {
    
    IsHidden() {
        return false;
    }
    
    OnCreated(params: object): void {
        if (IsServer()) {
            Timers.CreateTimer(0.3,()=>{
                this.SetStackCount(this.GetParent().cardUsesNum)
            })
        }
    }

    IsDebuff(): boolean {
        return false;
    }

    IsPurgable(): boolean {
        return false;
    }

    IsPurgeException(): boolean {
        return false;
    }

    RemoveOnDeath(): boolean {
        return true
    }
    
}
