const fs = require('fs');
const path = require('path');

// 定义需要修复的模式和替换
const fixes = [
    // 修复剩余的修饰符状态
    { pattern: /ModifierState\.INVULNERABLE/g, replacement: 'MODIFIER_STATE.MODIFIER_STATE_INVULNERABLE' },
    { pattern: /ModifierState\.OUT_OF_GAME/g, replacement: 'MODIFIER_STATE.MODIFIER_STATE_OUT_OF_GAME' },
    { pattern: /ModifierState\.NO_UNIT_COLLISION/g, replacement: 'MODIFIER_STATE.MODIFIER_STATE_NO_UNIT_COLLISION' },
    { pattern: /ModifierState\.NOT_ON_MINIMAP/g, replacement: 'MODIFIER_STATE.MODIFIER_STATE_NOT_ON_MINIMAP' },
    { pattern: /ModifierState\.UNSELECTABLE/g, replacement: 'MODIFIER_STATE.MODIFIER_STATE_UNSELECTABLE' },
    { pattern: /ModifierState\.DEBUFF_IMMUNE/g, replacement: 'MODIFIER_STATE.MODIFIER_STATE_DEBUFF_IMMUNE' },
    { pattern: /ModifierState\.FROZEN/g, replacement: 'MODIFIER_STATE.MODIFIER_STATE_FROZEN' },
    
    // 修复剩余的修饰符函数
    { pattern: /ModifierFunction\.MIN_HEALTH/g, replacement: 'MODIFIER_PROPERTY.MODIFIER_PROPERTY_MIN_HEALTH' },
    { pattern: /ModifierFunction\.HEALTH_REGEN_CONSTANT/g, replacement: 'MODIFIER_PROPERTY.MODIFIER_PROPERTY_HEALTH_REGEN_CONSTANT' },
    { pattern: /ModifierFunction\.ON_DEATH/g, replacement: 'MODIFIER_EVENT.MODIFIER_EVENT_ON_DEATH' },
    { pattern: /ModifierFunction\.INCOMING_PHYSICAL_DAMAGE_CONSTANT/g, replacement: 'MODIFIER_PROPERTY.MODIFIER_PROPERTY_INCOMING_PHYSICAL_DAMAGE_CONSTANT' },
    { pattern: /ModifierFunction\.ON_ATTACK_LANDED/g, replacement: 'MODIFIER_EVENT.MODIFIER_EVENT_ON_ATTACK_LANDED' },
    { pattern: /ModifierFunction\.VISUAL_Z_DELTA/g, replacement: 'MODIFIER_PROPERTY.MODIFIER_PROPERTY_VISUAL_Z_DELTA' },
    { pattern: /ModifierFunction\.ATTACK_RANGE_BONUS/g, replacement: 'MODIFIER_PROPERTY.MODIFIER_PROPERTY_ATTACK_RANGE_BONUS' },
    { pattern: /ModifierFunction\.ON_ATTACK_START/g, replacement: 'MODIFIER_EVENT.MODIFIER_EVENT_ON_ATTACK_START' },
    { pattern: /ModifierFunction\.TRANSLATE_ATTACK_SOUND/g, replacement: 'MODIFIER_PROPERTY.MODIFIER_PROPERTY_TRANSLATE_ATTACK_SOUND' },
    { pattern: /ModifierFunction\.TOTALDAMAGEOUTGOING_PERCENTAGE/g, replacement: 'MODIFIER_PROPERTY.MODIFIER_PROPERTY_TOTALDAMAGEOUTGOING_PERCENTAGE' },
    { pattern: /ModifierFunction\.STATUS_RESISTANCE_STACKING/g, replacement: 'MODIFIER_PROPERTY.MODIFIER_PROPERTY_STATUS_RESISTANCE_STACKING' },
    { pattern: /ModifierFunction\.MOVESPEED_LIMIT/g, replacement: 'MODIFIER_PROPERTY.MODIFIER_PROPERTY_MOVESPEED_LIMIT' },
    { pattern: /ModifierFunction\.ON_ATTACK/g, replacement: 'MODIFIER_EVENT.MODIFIER_EVENT_ON_ATTACK' },
    { pattern: /ModifierFunction\.HEALTHBAR_PIPS/g, replacement: 'MODIFIER_PROPERTY.MODIFIER_PROPERTY_HEALTHBAR_PIPS' },
    { pattern: /ModifierFunction\.INCOMING_DAMAGE_CONSTANT/g, replacement: 'MODIFIER_PROPERTY.MODIFIER_PROPERTY_INCOMING_DAMAGE_CONSTANT' },
    
    // 修复修饰符优先级和游戏活动
    { pattern: /ModifierPriority\.HIGH/g, replacement: 'MODIFIER_PRIORITY.MODIFIER_PRIORITY_HIGH' },
    { pattern: /ModifierPriority\.NORMAL/g, replacement: 'MODIFIER_PRIORITY.MODIFIER_PRIORITY_NORMAL' },
    { pattern: /GameActivity\.DOTA_DISABLED/g, replacement: 'GameActivity_t.ACT_DOTA_DISABLED' },
    { pattern: /GameActivity\.DOTA_ATTACK/g, replacement: 'GameActivity_t.ACT_DOTA_ATTACK' },
    { pattern: /GameActivity\.DOTA_CAST_ABILITY_1/g, replacement: 'GameActivity_t.ACT_DOTA_CAST_ABILITY_1' },
    { pattern: /GameActivity\.DOTA_CAST_ABILITY_3/g, replacement: 'GameActivity_t.ACT_DOTA_CAST_ABILITY_3' },
    
    // 修复单位目标团队
    { pattern: /UnitTargetTeam\.FRIENDLY/g, replacement: 'DOTA_UNIT_TARGET_TEAM.DOTA_UNIT_TARGET_TEAM_FRIENDLY' },
    
    // 修复伤害类型
    { pattern: /DamageTypes\.PHYSICAL/g, replacement: 'DAMAGE_TYPES.DAMAGE_TYPE_PHYSICAL' },
    
    // 修复修饰符运动类型
    { pattern: /LUA_MODIFIER_MOTION_NONE/g, replacement: 'LuaModifierMotionType.LUA_MODIFIER_MOTION_NONE' },
    { pattern: /LUA_MODIFIER_MOTION_BOTH/g, replacement: 'LuaModifierMotionType.LUA_MODIFIER_MOTION_BOTH' },
    { pattern: /LUA_MODIFIER_MOTION_HORIZONTAL/g, replacement: 'LuaModifierMotionType.LUA_MODIFIER_MOTION_HORIZONTAL' },
    { pattern: /LUA_MODIFIER_MOTION_VERTICAL/g, replacement: 'LuaModifierMotionType.LUA_MODIFIER_MOTION_VERTICAL' },
    
    // 修复索引类型问题
    { pattern: /this\.towers\[s\]/g, replacement: '(this.towers as Record<string, any>)[s]' },
    { pattern: /this\.GoUnitTemplateInfos\[s\]/g, replacement: '(this.GoUnitTemplateInfos as Record<string, any>)[s]' },
    { pattern: /this\.GoFixDeckCards\[s\]/g, replacement: '(this.GoFixDeckCards as Record<string, any>)[s]' },
    { pattern: /this\.GoSpells\[s\]/g, replacement: '(this.GoSpells as Record<string, any>)[s]' },
    { pattern: /this\.GoBotStrategy\[s\]/g, replacement: '(this.GoBotStrategy as Record<string, any>)[s]' },
    { pattern: /this\.GoSpecialUnitInfos\[s\]/g, replacement: '(this.GoSpecialUnitInfos as Record<string, any>)[s]' },
    { pattern: /this\.GoPlayerSpell\[s\]/g, replacement: '(this.GoPlayerSpell as Record<string, any>)[s]' },
    { pattern: /this\.GoSpecialUnitInfos\[unitName\]/g, replacement: '(this.GoSpecialUnitInfos as Record<string, any>)[unitName]' },
    { pattern: /this\.GoUnitTemplateInfos\[template\]/g, replacement: '(this.GoUnitTemplateInfos as Record<string, any>)[template]' },
    { pattern: /GameRules\.FastWarPhaseController\.GameTeamGroup\[v\]/g, replacement: '(GameRules.FastWarPhaseController.GameTeamGroup as Record<string, any>)[v]' },
    { pattern: /unitAI\[v\]/g, replacement: '(unitAI as Record<string, any>)[v]' },
    
    // 修复类型注解
    { pattern: /let modifiers = \[\]/g, replacement: 'let modifiers: any[] = []' },
    { pattern: /let abs = \[\]/g, replacement: 'let abs: any[] = []' },
    { pattern: /let dCards = \[\]/g, replacement: 'let dCards: any[] = []' },
    { pattern: /let rCards= \[\]/g, replacement: 'let rCards: any[] = []' },
    { pattern: /let CardIndex = \[\]/g, replacement: 'let CardIndex: any[] = []' },
    
    // 修复函数参数类型
    { pattern: /\(stateValue\) => value === stateValue/g, replacement: '(stateValue: any) => value === stateValue' },
    
    // 修复 this 类型问题
    { pattern: /OnHorizontalMotionInterrupted = function \(\) \{/g, replacement: 'OnHorizontalMotionInterrupted = () => {' },
    { pattern: /OnVerticalMotionInterrupted = function \(\) \{/g, replacement: 'OnVerticalMotionInterrupted = () => {' },
    
    // 修复 GetAttackSpeed 参数问题
    { pattern: /\.GetAttackSpeed\(true\)/g, replacement: '.GetAttackSpeed()' },
];

// 递归遍历目录
function walkDir(dir, callback) {
    const files = fs.readdirSync(dir);
    files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        if (stat.isDirectory()) {
            walkDir(filePath, callback);
        } else if (stat.isFile() && (file.endsWith('.ts') || file.endsWith('.tsx'))) {
            callback(filePath);
        }
    });
}

// 修复文件
function fixFile(filePath) {
    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;
    
    fixes.forEach(fix => {
        if (fix.pattern.test(content)) {
            content = content.replace(fix.pattern, fix.replacement);
            changed = true;
        }
    });
    
    if (changed) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`Fixed final errors in: ${filePath}`);
    }
}

// 主函数
function main() {
    const srcDir = path.join(__dirname, 'src');
    console.log('Starting final error fixes...');
    
    walkDir(srcDir, fixFile);
    
    console.log('Final error fixes completed!');
}

main();
