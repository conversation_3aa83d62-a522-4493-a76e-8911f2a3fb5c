{"compilerOptions": {"target": "ES2017", "lib": ["ES2017"], "moduleResolution": "node", "rootDir": "..", "baseUrl": "..", "types": ["@moddota/dota-lua-types"], "strict": false, "declaration": false, "outDir": "../../game/scripts/vscripts"}, "tstl": {"luaTarget": "5.1", "noHeader": true, "luaBundle": "../../game/scripts/vscripts/addon_game_mode.lua", "luaBundleEntry": "src/vscripts/addon_game_mode.ts", "lua51AllowTryCatchInAsyncAwait": true}, "include": ["../**/*.ts"], "exclude": ["node_modules", "../panorama/**/*"]}