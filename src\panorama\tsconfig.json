{"extends": "../tsconfig.json", "compilerOptions": {"target": "ES5", "module": "None", "moduleResolution": "node", "lib": ["ES2017", "DOM"], "outDir": "../../content/panorama/layout/custom_game", "rootDir": ".", "strict": false, "esModuleInterop": false, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "jsx": "react", "declaration": false, "sourceMap": false, "removeComments": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "allowSyntheticDefaultImports": false, "resolveJsonModule": false, "baseUrl": ".", "paths": {"@/*": ["./*"], "@utils/*": ["./utils/*"], "@components/*": ["./components/*"]}}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules", "**/*.d.ts"], "types": ["@moddota/panorama-types", "node"]}