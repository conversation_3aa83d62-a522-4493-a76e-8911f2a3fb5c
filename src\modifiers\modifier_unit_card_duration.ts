
import { BaseModifier, registerModifier } from "../utils/dota_ts_adapter";


export class modifier_unit_card_duration extends BaseModifier {


    GetAttributes(): ModifierAttribute {
        return 1 + 2 //+ 4
    }

    IsHidden():boolean {
        return true;
    }

    RemoveOnDeath(): boolean {
        return true
    }

    CheckState():Partial<Record<modifierstate, boolean>> {
        let state = {
            // [10]: true,
          }
        return state
    }

    // GetStatusEffectName(): string {
        // return "particles/status_fx/status_effect_faceless_chronosphere.vpcf";
    // }
    
    dNum:number;
    OnCreated(keys: any): void {
        if (IsServer()) {
            let duration = keys.fastWarDuration
            this.dNum = this.GetCaster().GetMaxHealth() / (duration/0.1)
            // print("创建持续扣血:"+ this.dNum)
            this.StartIntervalThink(0.1);
        }
    } 
    OnIntervalThink(): void {
        if (IsServer()) {
            if (!this.GetCaster().IsAlive()) {
                return 
            }
            ApplyDamage({
                victim: this.GetCaster(),
                attacker: this.GetCaster(),
                damage: this.dNum,
                damage_type: DAMAGE_TYPES.DAMAGE_TYPE_PURE,
                damage_flags:DOTADamageFlag_t.DOTA_DAMAGE_FLAG_NONE,
            });
            // print("扣血:"+ this.dNum)
        }
    }

    OnDestroy(){
        if (IsServer()) {
            
        }
    }

}
