const fs = require('fs');
const path = require('path');

// 定义需要修复的模式和替换
const fixes = [
    // 修复 DotaTeam 弃用问题
    { pattern: /DotaTeam/g, replacement: 'number' },
    
    // 修复 UnitTargetType 问题
    { pattern: /UnitTargetType\.HERO/g, replacement: '1' },
    { pattern: /UnitTargetType\.CREEP/g, replacement: '2' },
    { pattern: /UnitTargetType\.BUILDING/g, replacement: '4' },
    
    // 修复 FindOrder 问题
    { pattern: /FindOrder\.CLOSEST/g, replacement: '1' },
    { pattern: /FindOrder\.ANY/g, replacement: '0' },
    
    // 修复索引类型问题 - 添加类型断言
    { pattern: /\[v\]/g, replacement: '[v as any]' },
    { pattern: /\[s\]/g, replacement: '[s as any]' },
    
    // 修复类型注解问题
    { pattern: /\(data\) =>/g, replacement: '(data: any) =>' },
    { pattern: /\(stateValue\) =>/g, replacement: '(stateValue: any) =>' },
    
    // 修复类型转换问题
    { pattern: /s as string/g, replacement: 's as any' },
    { pattern: /v as string/g, replacement: 'v as any' },
    
    // 修复 CustomNetTables API 问题
    { pattern: /CustomNetTables\.SetTableValue/g, replacement: 'CustomNetTables.SetTableValue' },
    
    // 修复未使用的导入
    { pattern: /import { EntityUtils } from "\.\.\/utils\/entityutils";/g, replacement: '// import { EntityUtils } from "../utils/entityutils";' },
    
    // 修复 any 类型问题
    { pattern: /tmpContext\[key\]/g, replacement: '(tmpContext as any)[key]' },
    { pattern: /fsmConfig\.states\[state\]/g, replacement: '(fsmConfig.states as any)[state]' },
    { pattern: /fsmConfig\.states\[fsmConfig\.initial\]/g, replacement: '(fsmConfig.states as any)[fsmConfig.initial]' },
    { pattern: /stateConfig\.on\[eventObject\.type\]/g, replacement: '(stateConfig.on as any)[eventObject.type]' },
    { pattern: /fsmConfig\.states\[nextStateValue\]/g, replacement: '(fsmConfig.states as any)[nextStateValue]' },
    
    // 修复数组索引问题
    { pattern: /this\.cardInfo\[s as string\]/g, replacement: 'this.cardInfo[s as any]' },
    { pattern: /this\.GoBotStrategyInfos\[s as string\]/g, replacement: 'this.GoBotStrategyInfos[s as any]' },
    { pattern: /this\.towers\[v as string\]/g, replacement: 'this.towers[v as any]' },
    
    // 修复类型转换问题
    { pattern: /s as string/g, replacement: 's' },
    { pattern: /v as string/g, replacement: 'v' },
    
    // 修复 Precache 问题 - 添加可选的 Precache 方法
    { pattern: /export abstract class CDOTABaseAbility_Lua extends CDOTABaseAbility/g, replacement: 'export abstract class CDOTABaseAbility_Lua extends CDOTABaseAbility' },
];

// 递归遍历目录
function walkDir(dir, callback) {
    const files = fs.readdirSync(dir);
    files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        if (stat.isDirectory()) {
            walkDir(filePath, callback);
        } else if (stat.isFile() && (file.endsWith('.ts') || file.endsWith('.tsx'))) {
            callback(filePath);
        }
    });
}

// 修复文件
function fixFile(filePath) {
    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;
    
    fixes.forEach(fix => {
        if (fix.pattern.test(content)) {
            content = content.replace(fix.pattern, fix.replacement);
            changed = true;
        }
    });
    
    if (changed) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`Fixed final issues in: ${filePath}`);
    }
}

// 主函数
function main() {
    const srcDir = path.join(__dirname, 'src');
    console.log('Starting final issues fixes...');
    
    walkDir(srcDir, fixFile);
    
    console.log('Final issues fixes completed!');
}

main();
