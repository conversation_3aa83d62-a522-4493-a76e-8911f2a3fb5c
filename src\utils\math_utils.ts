

export class MathUtils {

  public static getNavLength (e1:CBaseEntity,e2:CBaseEntity) {
      return GridNav.FindPathLength(e1.GetAbsOrigin(), e2.GetAbsOrigin())
  }

  public static getLOrR (isGood:boolean, unit:CDOTA_BaseNPC) {
    return isGood?(unit.GetAbsOrigin().x<0?0:1):(unit.GetAbsOrigin().x<0?1:0)
  }

  /**
   * 随机排列数组
   */
  public static shuffle(a:any[]) {
    let x:any;
    for (let i = a.length; i > 0; i--) {
      let j = RandomInt(0,i);
      x = a[i-1]
      a[i-1] = a[j]
      a[j] = x
    }
    return a;
  }

  public static getRandomElements<T>(arr: T[], n: number): T[] {
    const validN = Math.floor(n);
    if (arr.length === 0 || validN <= 0) return [];

    const len = arr.length;
    const copy = [...arr];
    const count = Math.min(validN, len);

    // 部分洗牌算法，仅交换前 count 个元素
    for (let i = 0; i < count; i++) {
      const randomIndex = i + RandomInt(0,len - i - 1);
      [copy[i], copy[randomIndex]] = [copy[randomIndex], copy[i]];
    }

    return copy.slice(0, count);
  }

  /**
   * 生命值校验
   */
  public static healthCheck (checkNum:number, health:number, maxHealth:number) {
    if (checkNum == 0) {
      return true
    }
    let h = health
    if (Math.abs(checkNum) < 1) {
      h = health / maxHealth
    }
    if (checkNum < 0) {
      //负数代表当前数值应小于指定值
      return Math.abs(checkNum) > h
    } else {
      return Math.abs(checkNum) <= h
    }
  }

  /**
   * 数值校验
   */
  public static numCheck (checkNum:number, beCheckNum:number) {
    if (checkNum == 0) {
      return true
    }
    if (checkNum < 0) {
      //负数代表当前数值应小于指定值
      return Math.abs(checkNum) > beCheckNum
    } else {
      return Math.abs(checkNum) <= beCheckNum
    }
  }

  public static computeCentroid(vectors: Vector[]): Vector {
    const sum = vectors.reduce(
        (acc, v) => ({ x: acc.x + v.x, y: acc.y + v.y }),
        { x: 0, y: 0 }
    );
    return Vector(sum.x / vectors.length, sum.y / vectors.length, 0);
  }

  /**
   * 区间取数计算
   */
  public static numInRange (x:number, minX:number, maxX:number, minY:number, maxY:number) {
      if (x < minX) {
          return minY
      } else if (x < maxX) {
          return (x - minX)/(maxX - minX) * (maxY - minY) + minY
      } else {
          return maxY
      }
  }


  /**
   * 获取attach加偏移后的坐标
   * 
   */
  public static getAttachPosWithOffset (unit:CDOTA_BaseNPC, attach:string, offsetAngle:QAngle, offsetPos:Vector, modelScale:number):{angle:QAngle,pos:Vector} {
      let n = unit.ScriptLookupAttachment(attach)
      let angle = unit.GetAttachmentAngles(n)
      let angles = QAngle(angle.x, angle.y, angle.z)
      if (offsetAngle != undefined && offsetAngle.x + offsetAngle.y + offsetAngle.z > 0) {
          angles = RotateOrientation(angles,RotationDelta(offsetAngle, QAngle(0,0,0)))
      }
      let attach_pos = unit.GetAttachmentOrigin(n)
      if (offsetPos != undefined && offsetPos.Length() > 0) {
          attach_pos = attach_pos.__add(RotatePosition(Vector(0,0,0), angles, offsetPos.__mul(modelScale)))
      }
      return {
          angle:angles,
          pos:attach_pos
      }
  }

  /**
     * 计算放置位置
     * @param method 放置方式：lay_row
     * @param layGroupNum 放置分组数量
     * @param unitRings 单位占地情况
     */
  public static calculateLayV(layType:LayType,layGroupNum:number[], unitRings:number[], isGood:boolean): Vector[] {
    layGroupNum.push(unitRings.length)
    let res:Vector[] = []

    let unitRingsAfterDeal:number[][] = []
    let index = 0
    for (const n of layGroupNum) {
      unitRingsAfterDeal.push(unitRings.slice(index,index+n))
      index = index+n
    }
    if (layType == "lay_row") {
      let maxRowYs = unitRingsAfterDeal.map((vs)=>vs.reduce((max,x)=>max>x?max:x,0)*2)//行高
      let halfSumRowY = maxRowYs.reduce((sum,x)=>sum+x,0)//当前行高
      for (let i = 0 ; i < unitRingsAfterDeal.length ; i++) {
        let rs = unitRingsAfterDeal[i]//第i行
        halfSumRowY -= maxRowYs[i]
        let halfSumRowX = rs.reduce((sum,x)=>sum+x,0)*2
        for (let j = 0 ; j < rs.length ; j++) {//从左往右第j个
          halfSumRowX -= rs[j]*2
          res.push(Vector(halfSumRowX,halfSumRowY,0))
          halfSumRowX -= rs[j]*2
        }
        halfSumRowY -= maxRowYs[i]
      }
    }
     return res.map((v)=>isGood?v:v.__mul(-1))
  }

  public static div (x:number, y:number) {
        return parseInt(((x - x % y) / y).toString())
    }
  
  /**
   * 要在总时间内完成总行程，传入当前时间比值，返回总行程位置
   * @param allLength 总行程
   * @param offsetV 起始时的速度偏差比例，速度将从平均速度+offsetV均匀变换至平均速度-offsetV。
   * @param time 当前时间
   * @param allTime 总时间 
   */
  public static getVLengthByRate(allLength:number, offsetV:number,time:number, allTime:number) {
      if (time > allTime) {
          time = allTime
      } else if (time < 0) {
          time = 0
      }
      let res = ((allLength / allTime) * (2 * (1 + offsetV) - (time/allTime * (offsetV * 2)))) * time / 2
      if (offsetV > 1 && res > allLength) {
          res = allLength * 2 - res
      }
      return res
  }
  
  /**
   * 用于需要精确起点，落点，时间的位移，并提供恒定加速度
   * @param startPos 起点
   * @param xyV 总位移V，无高度
   * @param xyOffsetV 加速度
   * @param zH 高度变化
   * @param zOffsetV 高度加速度
   */
  public static moveUnit (startPos:Vector, xyV:Vector, xyOffsetV:number, zH:number, zOffsetV:number, timeIndex:number, allTimeIndex:number) {
      let flag = 1;
      xyV.z = 0
      if (zH < 0) {
          zH = -zH
          flag = -1
      }
      return startPos
          .__add(xyV.Normalized().__mul(MathUtils.getVLengthByRate(xyV.Length2D(), xyOffsetV, timeIndex, allTimeIndex)))
          .__add(Vector(0,0,flag*MathUtils.getVLengthByRate(zH, zOffsetV, timeIndex, allTimeIndex)))
  }


}
 