import { BaseItem, registerAbility } from "../utils/dota_ts_adapter";


export class item_for_precache extends BaseItem {

    Precache(context: CScriptPrecacheContext): void {
        if (GameRules.preCacheInfos != undefined && GameRules.preCacheInfos.length > 0) {
            GameRules.preCacheInfos.forEach((res)=>{
                // print("预载资源："+res)
                if (res.endsWith('.vpcf')) {
                    PrecacheResource('particle', res, context);
                } else if (res.endsWith('.vsndevts')) {
                    PrecacheResource('soundfile', res, context);
                } else if (res.endsWith('.vmdl')) {
                    PrecacheResource('model', res, context);
                } else {
                    PrecacheUnitByNameSync(res, context, undefined)
                }
            })
        }
        if (GameRules.preCacheAbs != undefined && GameRules.preCacheAbs.length > 0) {
            GameRules.preCacheAbs.forEach((res)=>{
                if (res.Precache != undefined) { 
                    res.Precache(context)
                }
            })
        }
        GameRules.preCacheInfos = []
        GameRules.preCacheAbs = []
    }
    
}
