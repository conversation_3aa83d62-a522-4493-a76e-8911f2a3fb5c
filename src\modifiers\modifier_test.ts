import { BaseModifier, registerModifier } from "../utils/dota_ts_adapter";



export class modifier_test extends BaseModifier {

    num:number;
    OnCreated(): void {
        this.num = 1
        print("格子血条："+IsClient())
    } 

    OnDestroy(){

    }

    IsHidden() {
        return false;
    }

    AllowIllusionDuplicate () {
        return true;
    }
    
    CheckState() {
        return {
            // [6]: true,
        }   
    }

    GetModifierHealthBarPips(event: ModifierAttackEvent): number {
        return this.num
    } 

    GetModifierIncomingDamageConstant(event: ModifierAttackEvent): number {
        // print("触发GetModifierIncomingDamageConstant:"+IsClient())
        return -(event.damage-1)
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [14,
            // 21,
            13];
    }

   
    
}
