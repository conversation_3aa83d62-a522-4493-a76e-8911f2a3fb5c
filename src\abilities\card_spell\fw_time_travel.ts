import { modifier_fw_time_travel } from "../../modifiers/abilities/card_spell/modifier_fw_time_travel";
import { BaseAbility, registerAbility } from "../../utils/dota_ts_adapter";

export class fw_time_travel extends BaseAbility {
    
    spell:SpellInfoKV;
    GetAOERadius(): number {
        return this.spell.AOERadius
    }

    OnSpellStart(): void {
        let delay = this.GetSpecialValueFor("delay")
        let time = this.GetSpecialValueFor("time")
        let remainTime = time

        let hero = this.GetCaster()
        let ab = this
        let team = hero.GetTeam()
        let tarPos = this.GetCursorPosition()
        let radius = this.GetAOERadius()
        let tarTeam = this.GetAbilityTargetTeam()
        let tarFlag = this.GetAbilityTargetFlags()
        let fwTargetType = this.spell.FWTargetType
        let e = GameRules.SoundUtils.getSoundEntity(tarPos)
        e.EmitSound("Fw.Cards.Spell.time_travel.cast")
        
        let par = ParticleManager.CreateParticle("particles/spell/time_travel/travel_start.vpcf", ParticleAttachment_t.PATTACH_WORLDORIGIN, undefined)
        ParticleManager.SetParticleControl(par, 0, tarPos.__add(Vector(0,0,20)))
        ParticleManager.SetParticleControl(par, 1, Vector(radius,delay,0))

        let tarUnits:CDOTA_BaseNPC[] = []
        let par2:ParticleID;
        let parTime:ParticleID;
        GameRules.FastWarSpell.startIntervalSpell(delay,time,1,
        ()=>{
            e.StopSound("Fw.Cards.Spell.time_travel.cast")
            let tars = FindUnitsInRadius(
                team,
                tarPos,
                undefined,
                radius,
                tarTeam,
                1 + 2 + 4,
                tarFlag,
                0,
                false,
            )
            for (const unit of tars) {
                if (unit.fastWarUnitType != undefined && (fwTargetType == GoFastWarAIUnitTypeEnum.ALL || unit.fastWarUnitType == fwTargetType)) {
                    unit.AddNewModifier(hero, ab, modifier_fw_time_travel.name, {posX:tarPos.x, posY:tarPos.y}) 
                    tarUnits.push(unit)
                }
            }
            ParticleManager.DestroyParticle(par, false)

            par2 = ParticleManager.CreateParticle("particles/spell/time_travel/abyssal_underlord_darkrift_target.vpcf", ParticleAttachment_t.PATTACH_WORLDORIGIN, undefined)
            ParticleManager.SetParticleControl(par2, 0, tarPos.__add(Vector(0,0,60)))
            ParticleManager.SetParticleControl(par2, 1, Vector(radius,remainTime,0))
            ParticleManager.SetParticleControl(par2, 6, tarPos.__add(Vector(0,0,60)))
            parTime = ParticleManager.CreateParticle("particles/spell/time_travel/abyssal_underlord_portal_timer.vpcf", ParticleAttachment_t.PATTACH_WORLDORIGIN, undefined)
            ParticleManager.SetParticleControl(parTime, 0, tarPos.__add(Vector(0,0,250)))
            ParticleManager.SetParticleControl(parTime, 1, Vector(0,remainTime,0))
            
        },()=>{
            remainTime--
            ParticleManager.SetParticleControl(parTime, 1, Vector(0,remainTime,0))
        },()=>{
            for (const tar of tarUnits) {
                tar.RemoveModifierByName(modifier_fw_time_travel.name)
            }
            ParticleManager.DestroyParticle(par2, false)
            ParticleManager.DestroyParticle(parTime, false)
            e.EmitSound("Fw.Cards.Spell.time_travel.end")
            Timers.CreateTimer(3,()=>{
                e.StopSound("Fw.Cards.Spell.time_travel.end")
                GameRules.SoundUtils.backSoundEntity(e)
            })
        })
        
    }

    Spawn(): void {
        this.spell = GameRules.KVUtils.getSpellInfo("fw_time_travel")
    }
    
    Precache(context: CScriptPrecacheContext): void {
        PrecacheResource("particle","particles/spell/time_travel/travel_start.vpcf",context)
        PrecacheResource("particle","particles/spell/time_travel/travel_start_alter.vpcf",context)
        PrecacheResource("particle","particles/spell/time_travel/abyssal_underlord_darkrift_target.vpcf",context)
        PrecacheResource("particle","particles/spell/time_travel/abyssal_underlord_portal_timer.vpcf",context)
        
    }
}

