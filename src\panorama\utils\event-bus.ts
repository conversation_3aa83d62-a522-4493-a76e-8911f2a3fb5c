import 'panorama-polyfill-x/lib/console';

// 类型定义
interface EventData {
  [key: string]: any;
}

interface LocalEventListener {
  (data: EventData): void;
}

interface GameEventListener {
  (data: EventData): void;
}

// 简单的事件总线实现
class EventBus {
  private localEventListeners: Map<string, LocalEventListener[]> = new Map();
  private gameEventListeners: Map<string, number[]> = new Map();

  constructor() {
    // 初始化
  }

  // 发射本地事件
  emitLocalEvent(eventName: string, data: EventData): void {
    const listeners = this.localEventListeners.get(eventName);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          console.error(`Error in local event listener for ${eventName}:`, error);
        }
      });
    }
  }

  // 监听本地事件
  onLocalEvent(eventName: string, listener: LocalEventListener): () => void {
    if (!this.localEventListeners.has(eventName)) {
      this.localEventListeners.set(eventName, []);
    }
    this.localEventListeners.get(eventName)!.push(listener);

    // 返回取消监听的函数
    return () => {
      const listeners = this.localEventListeners.get(eventName);
      if (listeners) {
        const index = listeners.indexOf(listener);
        if (index > -1) {
          listeners.splice(index, 1);
        }
      }
    };
  }

  // 发射游戏事件到服务器
  emitGameEvent(eventName: string, data: EventData): void {
    (GameEvents as any).SendCustomGameEventToServer(eventName, data);
  }

  // 监听游戏事件
  onGameEvent(eventName: string, listener: GameEventListener): () => void {
    const listenerId = (GameEvents as any).Subscribe(eventName, listener);
    
    // 存储监听器 ID
    if (!this.gameEventListeners.has(eventName)) {
      this.gameEventListeners.set(eventName, []);
    }
    this.gameEventListeners.get(eventName)!.push(listenerId);

    // 返回取消监听的函数
    return () => {
      (GameEvents as any).Unsubscribe(listenerId);
      const listeners = this.gameEventListeners.get(eventName);
      if (listeners) {
        const index = listeners.indexOf(listenerId);
        if (index > -1) {
          listeners.splice(index, 1);
        }
      }
    };
  }

  // 清理所有监听器
  cleanup(): void {
    // 清理本地事件监听器
    this.localEventListeners.clear();

    // 清理游戏事件监听器
    for (const [eventName, listenerIds] of this.gameEventListeners) {
      for (const listenerId of listenerIds) {
        try {
          (GameEvents as any).Unsubscribe(listenerId);
        } catch (error) {
          console.warn(`Failed to unsubscribe game event listener: ${eventName}`, error);
        }
      }
    }
    this.gameEventListeners.clear();
  }

  // 获取事件监听器数量
  getListenerCount(eventName: string): number {
    const listeners = this.localEventListeners.get(eventName);
    return listeners ? listeners.length : 0;
  }

  // 获取游戏事件监听器数量
  getGameEventListenerCount(eventName: string): number {
    const listeners = this.gameEventListeners.get(eventName);
    return listeners ? listeners.length : 0;
  }
}

// 创建全局事件总线实例
const eventBus = new EventBus();

// 导出便捷函数
export const emitLocalEvent = (eventName: string, data: EventData = {}): void => {
  eventBus.emitLocalEvent(eventName, data);
};

export const onLocalEvent = (eventName: string, listener: LocalEventListener): (() => void) => {
  return eventBus.onLocalEvent(eventName, listener);
};

export const emitGameEvent = (eventName: string, data: EventData = {}): void => {
  eventBus.emitGameEvent(eventName, data);
};

export const onGameEvent = (eventName: string, listener: GameEventListener): (() => void) => {
  return eventBus.onGameEvent(eventName, listener);
};

export const cleanup = (): void => {
  eventBus.cleanup();
};

// 导出事件总线实例
export { eventBus };

// 在页面卸载时清理资源
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', cleanup);
}

export default eventBus;
