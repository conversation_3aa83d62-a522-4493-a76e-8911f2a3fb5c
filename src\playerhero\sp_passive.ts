import { modifier_fw_hero_spell_blacksmith } from "../modifiers/abilities/hero_spell/modifier_fw_hero_spell_blacksmith";
import { PlayerHeroSpellHandle } from "./playerherospellhandle";


export class PlayerHeroPassive extends PlayerHeroSpellHandle {
    
    

    GetTowerBuff(data:{towerIndex:number}):ModifierData[] { 
        if (this.ab == undefined) {
            this.ab = this.hero.FindAbilityByName(this.spellInfo.SpellName)
        }
        if (this.spellInfo.SpellName == "fw_hero_spell_blacksmith") {
            if (data.towerIndex == 1 || data.towerIndex == 2) {
                return [
                    {
                        ab:this.ab,
                        caster:this.hero,
                        modifierName:modifier_fw_hero_spell_blacksmith.name,
                        data:{} 
                    }
                ]
            }
        }
        return []
    }

    private ab:CDOTABaseAbility;
    constructor (playerId:PlayerID,spellInfo:PlayerHeroForKV
    ) {
        super(playerId,spellInfo)
        let sp = GameRules.KVUtils.getSpellInfo(spellInfo.SpellName)
        this.nowSpellInfo = {
            HeroName: spellInfo.HeroName,
            HeroImage: spellInfo.HeroImage,
            SpellCardIndex: -1,
            SpellName: spellInfo.SpellName,
            cost: 0,
            nextUsingTime: GameRules.GetGameTime() + sp.CoolDown,
            cooldown:sp.CoolDown,
            num:"",
            usesNum:0,
        }
    }

}