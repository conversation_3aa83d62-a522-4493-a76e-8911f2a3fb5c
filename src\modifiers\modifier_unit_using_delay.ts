
import { BaseModifier, registerModifier } from "../utils/dota_ts_adapter";
import { modifier_fw_unit_delay_hero } from "./abilities/unit_innate/modifier_fw_unit_delay_hero";
import { modifier_unit_card_duration } from "./modifier_unit_card_duration";


export class modifier_unit_using_delay extends BaseModifier {


    GetAttributes(): ModifierAttribute {
        return 1 + 2 //+ 4
    }

    IsHidden():boolean {
        return false;
    }

    GetTexture(): string {
        return "action_stop"
    }

    GetOverrideAnimation() {
        return GameActivity_t.ACT_DOTA_SPAWN;
    }
    
    GetActivityTranslationModifiers(): string {
        return "ti8"
    }

    DeclareFunctions(): ModifierFunction[] {
        return [
            9,
            10,
        ];
    }

    CheckState():Partial<Record<modifierstate, boolean>> {
        let state = {
            [10]: true,
            [11]: true,
          }
        
        return state
    }
    
    unitDuration:number;
    OnCreated(keys: any): void {
        if (IsServer()) {
            this.unitDuration = keys.unitDuration
        } else {
            // print('客户端延迟modifier')
            let hero = this.GetParent() as CDOTA_BaseNPC_Hero;
            this.duration = this.GetDuration()
            if (hero.HasModifier(modifier_fw_unit_delay_hero.name)) {
                let ab = hero.FindAbilityByName("fw_unit_delay_hero")
                this.duration -= hero.GetModifierStackCount(modifier_fw_unit_delay_hero.name, hero) * ab.GetSpecialValueFor("sub_delay")
                this.duration = Math.max(this.duration, ab.GetSpecialValueFor("max_sub"))
                this.SetDuration(this.duration, true)
            }
            this.par = ParticleManager.CreateParticle("particles/gui/using_card/delay/overhead.vpcf",  ParticleAttachment_t.PATTACH_OVERHEAD_FOLLOW, hero)
            ParticleManager.SetParticleControl(this.par, 1, Vector(1,0,0))
            this.StartIntervalThink(0.03);
        }
    } 

    num = 0.03;
    duration:number;
    par:ParticleID;
    OnIntervalThink(): void {
        if (IsClient()) {
            if (this.num <= this.duration) {
                this.num += 0.03
                ParticleManager.SetParticleControl(this.par, 1, Vector((this.num/this.duration)*100,0,0))
            }
        }
    }

    OnDestroy(){
        if (IsServer()) {
            let unit = this.GetParent() as CDOTA_BaseNPC;
            if (this.unitDuration > 0) {
                unit.AddNewModifier(unit, undefined, modifier_unit_card_duration.name, {duration:this.unitDuration+1,fastWarDuration:this.unitDuration})
            }
        } else {
            if (this.par != undefined) {
                ParticleManager.SetParticleControl(this.par, 1, Vector(100,0,0))
                ParticleManager.DestroyParticle(this.par, false);
            }
        }
    }

}
