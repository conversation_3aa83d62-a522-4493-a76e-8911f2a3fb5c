"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.clearAllCache = exports.clearTableCache = exports.getCachedTable = exports.getCachedValue = exports.dispatch = exports.isEqual = void 0;
var event_bus_1 = require("./event-bus");
require("panorama-polyfill-x/lib/console");
function isEqual(prev, next) {
    if (typeof prev !== typeof next) {
        return false;
    }
    if (typeof prev === 'object') {
        if (prev === null) {
            return next === null;
        }
        if (Array.isArray(prev)) {
            if (!Array.isArray(next) || prev.length !== next.length) {
                return false;
            }
            for (var i = 0; i < prev.length; i++) {
                if (!isEqual(prev[i], next[i])) {
                    return false;
                }
            }
            return true;
        }
        if (Array.isArray(next)) {
            return false;
        }
        var prevKeys = Object.keys(prev);
        var nextKeys = Object.keys(next);
        if (prevKeys.length !== nextKeys.length) {
            return false;
        }
        for (var _i = 0, prevKeys_1 = prevKeys; _i < prevKeys_1.length; _i++) {
            var key = prevKeys_1[_i];
            var keyStr = String(key);
            var prevValue = prev[keyStr];
            var nextValue = next[keyStr];
            if (!next.hasOwnProperty(keyStr) || !isEqual(prevValue, nextValue)) {
                return false;
            }
        }
        return true;
    }
    return prev === next;
}
exports.isEqual = isEqual;
function dispatch(table_name, key, content) {
    try {
        var customConfig = GameUI.CustomUIConfig();
        if (!customConfig.__x_nettable_cache__) {
            customConfig.__x_nettable_cache__ = {};
        }
        if (!customConfig.__x_nettable_cache__[table_name]) {
            customConfig.__x_nettable_cache__[table_name] = {};
        }
        var prev = customConfig.__x_nettable_cache__[table_name][key];
        if (!isEqual(prev, content)) {
            customConfig.__x_nettable_cache__[table_name][key] = content;
            var tableData = {
                table_name: table_name,
                key: key,
                content: content,
            };
            (0, event_bus_1.emitLocalEvent)('x_net_table', tableData);
        }
    }
    catch (error) {
        console.log("x_net_table dispatch error: ".concat(table_name, " -> ").concat(key, " -> ").concat(content), error);
    }
}
exports.dispatch = dispatch;
function getCachedValue(table_name, key) {
    var customConfig = GameUI.CustomUIConfig();
    if (!customConfig.__x_nettable_cache__ ||
        !customConfig.__x_nettable_cache__[table_name]) {
        return undefined;
    }
    return customConfig.__x_nettable_cache__[table_name][key];
}
exports.getCachedValue = getCachedValue;
function getCachedTable(table_name) {
    var customConfig = GameUI.CustomUIConfig();
    if (!customConfig.__x_nettable_cache__) {
        return undefined;
    }
    return customConfig.__x_nettable_cache__[table_name];
}
exports.getCachedTable = getCachedTable;
function clearTableCache(table_name) {
    var customConfig = GameUI.CustomUIConfig();
    if (customConfig.__x_nettable_cache__ &&
        customConfig.__x_nettable_cache__[table_name]) {
        delete customConfig.__x_nettable_cache__[table_name];
    }
}
exports.clearTableCache = clearTableCache;
function clearAllCache() {
    var customConfig = GameUI.CustomUIConfig();
    customConfig.__x_nettable_cache__ = {};
}
exports.clearAllCache = clearAllCache;
if (typeof CustomNetTables !== 'undefined') {
    CustomNetTables.SubscribeNetTableListener('', function (tableName, key, value) {
        dispatch(tableName, key, value);
    });
}
exports.default = {
    isEqual: isEqual,
    dispatch: dispatch,
    getCachedValue: getCachedValue,
    getCachedTable: getCachedTable,
    clearTableCache: clearTableCache,
    clearAllCache: clearAllCache,
};
