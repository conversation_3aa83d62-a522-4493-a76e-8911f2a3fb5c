import { emitLocalEvent } from './event-bus';
import 'panorama-polyfill-x/lib/console';
/**
 * 深度比较两个值是否相等
 * TODO: 这个比对算法应该还是需要进一步优化的
 *
 * @param prev 前一个值
 * @param next 下一个值
 * @returns 是否相等
 */
export function isEqual(prev, next) {
    // 类型不同直接返回 false
    if (typeof prev !== typeof next) {
        return false;
    }
    // 处理对象类型
    if (typeof prev === 'object') {
        // 处理 null 值
        if (prev === null) {
            return next === null;
        }
        // 处理数组
        if (Array.isArray(prev)) {
            if (!Array.isArray(next) || prev.length !== next.length) {
                return false;
            }
            for (let i = 0; i < prev.length; i++) {
                if (!isEqual(prev[i], next[i])) {
                    return false;
                }
            }
            return true;
        }
        // 处理普通对象
        if (Array.isArray(next)) {
            return false;
        }
        // 比较对象的所有属性
        const prevKeys = Object.keys(prev);
        const nextKeys = Object.keys(next);
        if (prevKeys.length !== nextKeys.length) {
            return false;
        }
        for (const key of prevKeys) {
            const keyStr = String(key);
            const prevValue = prev[keyStr];
            const nextValue = next[keyStr];
            if (!next.hasOwnProperty(keyStr) || !isEqual(prevValue, nextValue)) {
                return false;
            }
        }
        return true;
    }
    // 处理基本类型
    return prev === next;
}
/**
 * 调度 NetTable 数据更新
 *
 * @param table_name 表名
 * @param key 键名
 * @param content 内容
 */
export function dispatch(table_name, key, content) {
    try {
        // 确保缓存对象存在
        const customConfig = GameUI.CustomUIConfig();
        if (!customConfig.__x_nettable_cache__) {
            customConfig.__x_nettable_cache__ = {};
        }
        if (!customConfig.__x_nettable_cache__[table_name]) {
            customConfig.__x_nettable_cache__[table_name] = {};
        }
        // 获取之前的值
        const prev = customConfig.__x_nettable_cache__[table_name][key];
        // 只有在值发生变化时才触发事件
        if (!isEqual(prev, content)) {
            // 更新缓存
            customConfig.__x_nettable_cache__[table_name][key] = content;
            // 构造事件数据
            const tableData = {
                table_name,
                key,
                content,
            };
            // 发射本地事件
            // console.log(`x net table data updated ${table_name}->${key}`);
            emitLocalEvent('x_net_table', tableData);
        }
    }
    catch (error) {
        console.log(`x_net_table dispatch error: ${table_name} -> ${key} -> ${content}`, error);
    }
}
/**
 * 获取 NetTable 缓存的值
 *
 * @param table_name 表名
 * @param key 键名
 * @returns 缓存的值
 */
export function getCachedValue(table_name, key) {
    const customConfig = GameUI.CustomUIConfig();
    if (!customConfig.__x_nettable_cache__ ||
        !customConfig.__x_nettable_cache__[table_name]) {
        return undefined;
    }
    return customConfig.__x_nettable_cache__[table_name][key];
}
/**
 * 获取整个表的缓存数据
 *
 * @param table_name 表名
 * @returns 表的所有数据
 */
export function getCachedTable(table_name) {
    const customConfig = GameUI.CustomUIConfig();
    if (!customConfig.__x_nettable_cache__) {
        return undefined;
    }
    return customConfig.__x_nettable_cache__[table_name];
}
/**
 * 清理指定表的缓存
 *
 * @param table_name 表名
 */
export function clearTableCache(table_name) {
    const customConfig = GameUI.CustomUIConfig();
    if (customConfig.__x_nettable_cache__ &&
        customConfig.__x_nettable_cache__[table_name]) {
        delete customConfig.__x_nettable_cache__[table_name];
    }
}
/**
 * 清理所有缓存
 */
export function clearAllCache() {
    const customConfig = GameUI.CustomUIConfig();
    customConfig.__x_nettable_cache__ = {};
}
// 监听 NetTable 变化并调度事件
if (typeof CustomNetTables !== 'undefined') {
    // 监听所有 NetTable 变化
    CustomNetTables.SubscribeNetTableListener('', (tableName, key, value) => {
        dispatch(tableName, key, value);
    });
}
export default {
    isEqual,
    dispatch,
    getCachedValue,
    getCachedTable,
    clearTableCache,
    clearAllCache,
};
//# sourceMappingURL=x-nettable-dispatcher.js.map