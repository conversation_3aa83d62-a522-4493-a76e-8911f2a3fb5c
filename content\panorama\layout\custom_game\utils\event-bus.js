"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.eventBus = exports.cleanup = exports.onGameEvent = exports.emitGameEvent = exports.onLocalEvent = exports.emitLocalEvent = void 0;
require("panorama-polyfill-x/lib/console");
var EventBus = (function () {
    function EventBus() {
        this.localEventListeners = new Map();
        this.gameEventListeners = new Map();
    }
    EventBus.prototype.emitLocalEvent = function (eventName, data) {
        var listeners = this.localEventListeners.get(eventName);
        if (listeners) {
            listeners.forEach(function (listener) {
                try {
                    listener(data);
                }
                catch (error) {
                    console.error("Error in local event listener for ".concat(eventName, ":"), error);
                }
            });
        }
    };
    EventBus.prototype.onLocalEvent = function (eventName, listener) {
        var _this = this;
        if (!this.localEventListeners.has(eventName)) {
            this.localEventListeners.set(eventName, []);
        }
        this.localEventListeners.get(eventName).push(listener);
        return function () {
            var listeners = _this.localEventListeners.get(eventName);
            if (listeners) {
                var index = listeners.indexOf(listener);
                if (index > -1) {
                    listeners.splice(index, 1);
                }
            }
        };
    };
    EventBus.prototype.emitGameEvent = function (eventName, data) {
        GameEvents.SendCustomGameEventToServer(eventName, data);
    };
    EventBus.prototype.onGameEvent = function (eventName, listener) {
        var _this = this;
        var listenerId = GameEvents.Subscribe(eventName, listener);
        if (!this.gameEventListeners.has(eventName)) {
            this.gameEventListeners.set(eventName, []);
        }
        this.gameEventListeners.get(eventName).push(listenerId);
        return function () {
            GameEvents.Unsubscribe(listenerId);
            var listeners = _this.gameEventListeners.get(eventName);
            if (listeners) {
                var index = listeners.indexOf(listenerId);
                if (index > -1) {
                    listeners.splice(index, 1);
                }
            }
        };
    };
    EventBus.prototype.cleanup = function () {
        this.localEventListeners.clear();
        for (var _i = 0, _a = this.gameEventListeners; _i < _a.length; _i++) {
            var _b = _a[_i], eventName = _b[0], listenerIds = _b[1];
            for (var _c = 0, listenerIds_1 = listenerIds; _c < listenerIds_1.length; _c++) {
                var listenerId = listenerIds_1[_c];
                try {
                    GameEvents.Unsubscribe(listenerId);
                }
                catch (error) {
                    console.warn("Failed to unsubscribe game event listener: ".concat(eventName), error);
                }
            }
        }
        this.gameEventListeners.clear();
    };
    EventBus.prototype.getListenerCount = function (eventName) {
        var listeners = this.localEventListeners.get(eventName);
        return listeners ? listeners.length : 0;
    };
    EventBus.prototype.getGameEventListenerCount = function (eventName) {
        var listeners = this.gameEventListeners.get(eventName);
        return listeners ? listeners.length : 0;
    };
    return EventBus;
}());
var eventBus = new EventBus();
exports.eventBus = eventBus;
var emitLocalEvent = function (eventName, data) {
    if (data === void 0) { data = {}; }
    eventBus.emitLocalEvent(eventName, data);
};
exports.emitLocalEvent = emitLocalEvent;
var onLocalEvent = function (eventName, listener) {
    return eventBus.onLocalEvent(eventName, listener);
};
exports.onLocalEvent = onLocalEvent;
var emitGameEvent = function (eventName, data) {
    if (data === void 0) { data = {}; }
    eventBus.emitGameEvent(eventName, data);
};
exports.emitGameEvent = emitGameEvent;
var onGameEvent = function (eventName, listener) {
    return eventBus.onGameEvent(eventName, listener);
};
exports.onGameEvent = onGameEvent;
var cleanup = function () {
    eventBus.cleanup();
};
exports.cleanup = cleanup;
if (typeof window !== 'undefined') {
    window.addEventListener('beforeunload', exports.cleanup);
}
exports.default = eventBus;
