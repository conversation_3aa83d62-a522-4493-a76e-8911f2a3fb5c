/* Prettified by Source 2 Viewer 13.0.0.0 - https://valveresourceformat.github.io */

/* Fast War Card Game UI Styles - Panorama Compatible Version */
.fast-war-app {
    width: 100%;
    height: 100%;
    background-color: #1a1a2e;
    color: #ffffff;
    position: absolute;
    top: 0px;
    left: 0px;
    z-index: 1000;
}

/* 游戏信息栏 */
.game-info {
    position: absolute;
    top: 20px;
    left: 20px;
    background-color: rgba(0, 0, 0, 0.8);
    border: 2px solid #4a90e2;
    border-radius: 10px;
    padding: 15px;
    z-index: 1001;
    width: 300px;
    height: 80px;
}

.player-stats {
    width: 100%;
    height: 100%;
    flow-children: right;
}

.stat {
    width: 80px;
    height: 60px;
    margin-right: 10px;
    text-align: center;
}

.stat__label {
    font-size: 12px;
    color: #cccccc;
    margin-bottom: 5px;
}

.stat__value {
    font-size: 18px;
    font-weight: bold;
    color: #4a90e2;
}

/* 战场区域 */
.battlefield {
    position: absolute;
    top: 200px;
    left: 300px;
    width: 600px;
    height: 300px;
    background-color: rgba(0, 0, 0, 0.3);
    border: 2px solid #666666;
    border-radius: 15px;
    z-index: 999;
}

.battlefield__content {
    width: 100%;
    height: 100%;
    text-align: center;
    color: #888888;
    vertical-align: middle;
    padding-top: 120px;
}

/* 手牌区域 */
.hand-area {
    position: absolute;
    bottom: 20px;
    left: 200px;
    width: 800px;
    background-color: rgba(0, 0, 0, 0.8);
    border: 2px solid #4a90e2;
    border-radius: 15px;
    padding: 15px;
    z-index: 1001;
}

.hand-cards {
    width: 100%;
    height: 220px;
    flow-children: right;
    margin-top: 10px;
    padding: 5px;
}

/* 卡牌样式 */
.card {
    width: 150px;
    height: 200px;
    background-color: #2c3e50;
    border: 2px solid #4a90e2;
    border-radius: 10px;
    padding: 10px;
    margin-right: 10px;
    position: relative;
}

.card:hover,
.card--hovered {
    background-color: #34495e;
    border-color: #5aa3f0;
    transform: translatey(-5px);
}

.card--selected {
    border-color: #f39c12;
    background-color: #3e2723;
}

.card--playable {
    border-color: #27ae60;
}

.card--unplayable {
    opacity: 0.6;
    border-color: #e74c3c;
}

/* 卡牌类型颜色 */
.card--unit {
    background-color: #2c5530;
}

.card--spell {
    background-color: #4a2c50;
}

.card--hero {
    background-color: #5a4a2c;
}

.card--building {
    background-color: #4a3c2c;
}

/* 卡牌头部 */
.card__header {
    width: 100%;
    height: 30px;
    margin-bottom: 10px;
    flow-children: right;
}

.card__cost {
    background-color: #e74c3c;
    color: white;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    text-align: center;
    font-weight: bold;
    font-size: 12px;
    line-height: 25px;
}

.card__type-icon {
    font-size: 16px;
    margin-left: 80px;
    line-height: 25px;
}

/* 卡牌主体 */
.card__body {
    width: 100%;
    height: 120px;
}

#FastWarRoot
{
	width: 100%;
	height: 100%;
}

#FastWarUIButtonRoot
{
	width: 100%;
	height: 100%;
	z-index: 10;
}

/* 卡牌图片和信息 */
.card__image {
    width: 100%;
    height: 60px;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 5px;
    margin-bottom: 10px;
    text-align: center;
}

.card__placeholder-image {
    font-size: 24px;
    opacity: 0.7;
    line-height: 60px;
}

.card__info {
    width: 100%;
    height: 40px;
}

.card__name {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 5px;
    color: #ffffff;
}

.card__description {
    font-size: 11px;
    color: #cccccc;
}

/* 卡牌统计 */
.card__stats {
    width: 100%;
    height: 20px;
    margin: 5px 0px;
    flow-children: right;
}

.card__count,
.card__delay {
    font-size: 10px;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 2px 6px;
    border-radius: 3px;
    margin-right: 5px;
}

/* 卡牌底部 */
.card__footer {
    width: 100%;
    height: 15px;
    margin-top: 5px;
    text-align: center;
}

.card__rarity {
    font-size: 10px;
    color: #888888;
}

/* 卡牌详情面板 */
.card-detail {
    position: absolute;
    top: 200px;
    right: 20px;
    width: 250px;
    height: 300px;
    background-color: rgba(0, 0, 0, 0.9);
    border: 2px solid #4a90e2;
    border-radius: 10px;
    padding: 15px;
    z-index: 1002;
}

.card-detail__content {
    width: 100%;
    margin-top: 10px;
}

/* Panorama 不支持 keyframes 动画和滚动条样式，已移除 */

#FastWarDeckButtonContainer
{
	width: 400px;
	height: 400px;
	align: right bottom;
}

#FastWarMenuButtonContainer
{
	margin-top: 12px;
	margin-left: 8px;
	tooltip-position: bottom;
	flow-children: right;
}

#FastWarMenuButtonContainer Button
{
	tooltip-position: bottom;
	width: 30px;
	height: 30px;
	background-repeat: no-repeat;
	background-size: 100%;
	margin-top: 0px;
	margin-bottom: 0px;
	margin-left: 8px;
	margin-right: 8px;
	vertical-align: middle;
	horizontal-align: center;
	background-position: center;
	opacity: 0.5;
	wash-color: #CDF;
	transition-property: opacity;
	transition-duration: 0.2s;
	img-shadow: 0px 0px 3px 3 black;
}

#FastWarMenuButtonContainer Button:hover
{
	opacity: 1;
	wash-color: white;
}

#DashboardButton
{
	background-image: url("s2r://panorama/images/control_icons/return_to_game_png.vtex");
	transform: scaleY(-1);
}

#ToggleScoreboardButton
{
	background-image: url("s2r://panorama/images/control_icons/hamburger_png.vtex");
	background-size: 26px;
}

Button#SettingsButton
{
	background-image: url("s2r://panorama/images/control_icons/gear_png.vtex");
	background-size: 27px;
	margin-top: 2px;
}

#FastWarNotificationRoot
{
	width: 100%;
	height: 100%;
	z-index: 5;
}

#PlayerStartRequestContainer
{
	width: 700px;
	height: 250px;
	align: center center;
	background-color: #242424;
	border: 2px solid rgba(255, 255, 255, 0.582);
	opacity: 0;
	transform: translate3d(-1500px, -500px, 0px) scale3d(0.3, 0.3, 1);
	transition-property: opacity, transform;
	transition-duration: 0.3s;
	transition-timing-function: ease-in-out;
}

#PlayerStartRequestContainer.ShowNotification
{
	transform: translate3d(0px, 0px, 0px) scale3d(1, 1, 1);
	opacity: 1;
}

#PlayerStartRequestContainer.HideNotification
{
	transform: translate3d(-1500px, -500px, 0px) scale3d(0.3, 0.3, 1);
	opacity: 0;
}

#PlayerStartRequestMain
{
	width: 660px;
	height: 130px;
	align: center top;
	margin-top: 20px;
}

#PlayerStartRequestTextContainer
{
	align: center center;
	flow-children: down;
}

.PlayerStartRequestText
{
	margin-top: 10px;
	margin-bottom: 10px;
	horizontal-align: center;
	height: 45px;
	text-align: center;
	overflow: noclip;
	text-overflow: shrink;
	font-size: 43px;
	font-weight: bold;
	text-shadow: 0px 0px 5px 5 #000000;
	color: rgba(250, 255, 92, 0.666);
}

.PlayerStartRequestText2
{
	margin-top: 5px;
	margin-bottom: 5px;
	horizontal-align: center;
	height: 45px;
	text-align: center;
	overflow: noclip;
	text-overflow: shrink;
	font-size: 43px;
	font-weight: bold;
	text-shadow: 0px 0px 5px 5 #000000;
	color: rgba(150, 151, 113, 0.666);
}

#PlayerStartRequestButtonContainer
{
	width: 500px;
	margin-bottom: 30px;
	align: center bottom;
}

.PlayerStartRequestButton
{
	height: 60px;
	margin: 2px;
	background-color: rgba(0, 0, 0, 0.538);
	border: 2px solid #000000;
}

#PlayerStartRequestAgree
{
	align: left center;
	border: 2px solid rgba(129, 200, 117, 0.604);
}

#PlayerStartRequestAgree:hover
{
	transform: translate3d(-1px, -1px, 0px);
	border: 2px solid #a3a3a3;
}

#PlayerStartRequestAgree:active
{
	transform: translate3d(2px, 2px, 0px);
}

#PlayerStartRequestDisagree
{
	align: right center;
	border: 2px solid rgba(222, 98, 98, 0.745);
}

#PlayerStartRequestDisagree:hover
{
	transform: translate3d(-1px, -1px, 0px);
	border: 2px solid #a3a3a3;
}

#PlayerStartRequestDisagree:active
{
	transform: translate3d(2px, 2px, 0px);
}

#PlayerStartRequestAgree .PlayerStartRequestButtonText
{
	color: rgba(129, 200, 117, 0.604);
}

#PlayerStartRequestDisagree .PlayerStartRequestButtonText
{
	color: rgba(222, 98, 98, 0.745);
}

.PlayerStartRequestButtonText
{
	align: center center;
	height: 37px;
	text-align: center;
	overflow: noclip;
	text-overflow: shrink;
	font-size: 35px;
	font-weight: bold;
	text-shadow: 0px 0px 5px 5 #000000;
}

#RoundEndNotificationContainer
{
	width: 1500px;
	height: 600px;
	margin-bottom: 200px;
	align: center center;
	transition-property: opacity;
	transition-duration: 0.4s;
	opacity: 0;
}

#RoundEndNotificationContainer.ShowNotification
{
	opacity: 1;
}

#RoundEndNotificationContainer.HideNotification
{
	opacity: 0;
}

#RoundEndNotification
{
	width: 900px;
	height: 150px;
	margin-bottom: 70px;
	align: center center;
	background-image: url("s2r://panorama/images/custom_game/hud/victory_png.vtex");
	background-size: 100% 100%;
	background-repeat: no-repeat;
}

#RoundEndNotificationContainer.ShowNotification #RoundEndNotification
{
	opacity: 1;
	transform: translateX(0%);
	animation-name: pos-easeIn-right3;
	animation-duration: 0.8s;
	animation-timing-function: linear;
}

#RoundEndNotificationContainer.HideNotification #RoundEndNotification
{
	opacity: 0;
	transform: translateX(0%);
	animation-name: pos-out-right;
	animation-duration: 0.5s;
	animation-timing-function: ease-in-out;
}

#RoundEndNotificationText
{
	align: center top;
	width: 340px;
	height: 157px;
	text-align: center;
	overflow: noclip;
	text-overflow: shrink;
	color: #fff3c5;
	font-size: 155px;
	font-weight: bold;
	text-shadow: 0px 0px 5px 5 #000000;
	brightness: 1;
}

#OverTimeNotificationContainer
{
	width: 1500px;
	height: 600px;
	margin-bottom: 200px;
	align: center center;
	transition-property: opacity;
	transition-duration: 0.4s;
	opacity: 0;
}

#OverTimeNotificationContainer.ShowNotification
{
	opacity: 1;
}

#OverTimeNotificationContainer.HideNotification
{
	opacity: 0;
}

#OverTimeNotification
{
	width: 900px;
	height: 150px;
	align: center center;
	background-image: url("s2r://panorama/images/custom_game/hud/overtime_png.vtex");
	background-size: 100% 100%;
	background-repeat: no-repeat;
}

#OverTimeNotificationContainer.ShowNotification #OverTimeNotification
{
	opacity: 1;
	transform: translateX(0%);
	animation-name: pos-easeIn-right3;
	animation-duration: 0.8s;
	animation-timing-function: linear;
}

#OverTimeNotificationContainer.HideNotification #OverTimeNotification
{
	opacity: 0;
	transform: translateX(0%);
	animation-name: pos-out-right;
	animation-duration: 0.5s;
	animation-timing-function: ease-in-out;
}

#OverTimeTip
{
	align: center center;
	margin-bottom: 140px;
	width: 900px;
	flow-children: down;
}

#OverTimeText1
{
	align: center top;
	height: 105px;
	text-align: center;
	overflow: noclip;
	text-overflow: shrink;
	color: #ff9797;
	font-size: 100px;
	font-weight: bold;
	text-shadow: 0px 0px 5px 5 #000000;
	brightness: 1;
}

#OverTimeText2
{
	align: center top;
	height: 32px;
	text-align: center;
	overflow: noclip;
	text-overflow: shrink;
	color: #ffffff;
	font-size: 30px;
	font-weight: bold;
	text-shadow: 0px 0px 5px 5 #000000;
	brightness: 1;
}

#RoundStartNotificationContainer
{
	width: 1500px;
	height: 600px;
	margin-bottom: 200px;
	align: center center;
	transition-property: opacity;
	transition-duration: 0.4s;
	opacity: 0;
}

#RoundStartNotificationContainer.ShowNotification
{
	opacity: 1;
}

#RoundStartNotificationContainer.HideNotification
{
	opacity: 0;
}

#RoundStartNotification
{
	width: 900px;
	height: 150px;
	align: center center;
	background-image: url("s2r://panorama/images/custom_game/hud/round_start_png.vtex");
	background-size: 100% 100%;
	background-repeat: no-repeat;
}

#RoundStartNotificationContainer.ShowNotification #RoundStartNotification
{
	opacity: 1;
	transform: translateX(0%);
	animation-name: pos-easeIn-right3;
	animation-duration: 0.8s;
	animation-timing-function: linear;
}

#RoundStartNotificationContainer.HideNotification #RoundStartNotification
{
	opacity: 0;
	transform: translateX(0%);
	animation-name: pos-out-right;
	animation-duration: 0.5s;
	animation-timing-function: ease-in-out;
}

@keyframes 'pos-easeIn-right3'
{
	0%
	{
		transform: translateX(100%);
	}
	
	20%
	{
		transform: translateX(-5.06%);
	}
	
	40%
	{
		transform: translateX(2.12%);
	}
	
	100%
	{
		transform: translateX(0%);
	}
	
}

#GameStartNotificationContainer
{
	width: 100%;
	height: 100%;
	transition-property: opacity;
	transition-timing-function: ease-in;
	transition-duration: 0.4s;
	opacity: 0;
}

#GameStartNotificationContainer.ShowNotification
{
	background-color: rgba(0, 0, 0, 0.596);
	opacity: 1;
}

#GameStartNotificationContainer.HideNotification
{
	transition-duration: 0.4s;
	opacity: 0;
}

#GameStartNotificationBack
{
	width: 100%;
	height: 100%;
	background-image: url("s2r://panorama/images/custom_game/back/s2_background_psd_png.vtex");
	background-size: 100% 100%;
	background-repeat: no-repeat;
	background-position: center center;
	opacity-mask: url("s2r://panorama/images/custom_game/hud/back_png.vtex");
	opacity: 0.5;
	wash-color: rgba(255, 172, 172, 0.673);
}

#GameStartNotificationContainer.ShowNotification #GameStartNotificationBack
{
	transform: translateX(0%);
	opacity: 0.5;
	animation-name: pos-easeInElastic-right2;
	animation-duration: 2s;
	animation-timing-function: linear;
}

#GameStartNotificationContainer.HideNotification #GameStartNotificationBack
{
	opacity: 0;
	transform: translateX(-100%);
	animation-name: pos-out-left;
	animation-duration: 0.5s;
	animation-timing-function: ease-in-out;
}

@keyframes 'pos-easeInElastic-right2'
{
	0%
	{
		transform: translateX(100%);
	}
	
	30%
	{
		transform: translateX(100%);
	}
	
	34%
	{
		transform: translateX(0%);
	}
	
	38%
	{
		transform: translateX(13.12%);
	}
	
	44%
	{
		transform: translateX(0%);
	}
	
	48%
	{
		transform: translateX(0%);
	}
	
	56%
	{
		transform: translateX(1.64%);
	}
	
	58%
	{
		transform: translateX(1.56%);
	}
	
	70%
	{
		transform: translateX(0%);
	}
	
	72%
	{
		transform: translateX(0%);
	}
	
	86%
	{
		transform: translateX(0%);
	}
	
	98%
	{
		transform: translateX(0.17%);
	}
	
	100%
	{
		transform: translateX(0%);
	}
	
}

#GameStartVSBackContainer
{
	width: 1080px;
	height: 1080px;
	z-index: 2;
	align: center center;
}

#GameStartVSBackPar
{
	width: 1080px;
	height: 1080px;
	z-index: 1;
}

.GameStartNotification
{
	width: 1800px;
	height: 700px;
	z-index: 1;
}

#GameStartNotificationSelf
{
	align: left bottom;
}

@keyframes 'pos-easeInElastic-left'
{
	0%
	{
		transform: translateX(-100%);
	}
	
	40%
	{
		transform: translateX(-100%);
	}
	
	44%
	{
		transform: translateX(10.06%);
	}
	
	48%
	{
		transform: translateX(-13.12%);
	}
	
	54%
	{
		transform: translateX(4.4%);
	}
	
	58%
	{
		transform: translateX(4.63%);
	}
	
	66%
	{
		transform: translateX(-1.64%);
	}
	
	68%
	{
		transform: translateX(-1.56%);
	}
	
	80%
	{
		transform: translateX(0.55%);
	}
	
	82%
	{
		transform: translateX(0.58%);
	}
	
	96%
	{
		transform: translateX(0.04%);
	}
	
	98%
	{
		transform: translateX(-0.17%);
	}
	
	100%
	{
		transform: translateX(0%);
	}
	
}

@keyframes 'pos-easeInElastic-right'
{
	0%
	{
		transform: translateX(100%);
	}
	
	40%
	{
		transform: translateX(100%);
	}
	
	44%
	{
		transform: translateX(-10.06%);
	}
	
	48%
	{
		transform: translateX(13.12%);
	}
	
	54%
	{
		transform: translateX(-4.4%);
	}
	
	58%
	{
		transform: translateX(-4.63%);
	}
	
	66%
	{
		transform: translateX(1.64%);
	}
	
	68%
	{
		transform: translateX(1.56%);
	}
	
	80%
	{
		transform: translateX(-0.55%);
	}
	
	82%
	{
		transform: translateX(-0.58%);
	}
	
	96%
	{
		transform: translateX(-0.04%);
	}
	
	98%
	{
		transform: translateX(0.17%);
	}
	
	100%
	{
		transform: translateX(0%);
	}
	
}

@keyframes 'pos-easeInBounce-left'
{
	0%
	{
		transform: translateX(-100%);
	}
	
	40%
	{
		transform: translateX(-100%);
	}
	
	44%
	{
		transform: translateX(-89.11%);
	}
	
	48%
	{
		transform: translateX(-56.44%);
	}
	
	58%
	{
		transform: translateX(-1.99%);
	}
	
	66%
	{
		transform: translateX(-24.98%);
	}
	
	86%
	{
		transform: translateX(-1.63%);
	}
	
	100%
	{
		transform: translateX(0%);
	}
	
}

@keyframes 'pos-easeInBounce-right'
{
	0%
	{
		transform: translateX(100%);
	}
	
	40%
	{
		transform: translateX(100%);
	}
	
	44%
	{
		transform: translateX(89.11%);
	}
	
	48%
	{
		transform: translateX(56.44%);
	}
	
	58%
	{
		transform: translateX(1.99%);
	}
	
	66%
	{
		transform: translateX(24.98%);
	}
	
	86%
	{
		transform: translateX(1.63%);
	}
	
	100%
	{
		transform: translateX(0%);
	}
	
}

@keyframes 'pos-out-left'
{
	0%
	{
		opacity: 1;
		transform: translateX(0%);
	}
	
	100%
	{
		opacity: 0;
		transform: translateX(100%);
	}
	
}

@keyframes 'pos-out-right'
{
	0%
	{
		opacity: 1;
		transform: translateX(0%);
	}
	
	100%
	{
		opacity: 0;
		transform: translateX(-100%);
	}
	
}

.GameStartPlayerHeroImgContainer
{
	width: 500px;
	height: 500px;
	z-index: 3;
}

#GameStartPlayerHeroImgContainerSelf
{
	align: left center;
	margin-left: 340px;
}

#GameStartPlayerHeroImgContainerEnemy
{
	align: right center;
	margin-right: 340px;
}

#GameStartNotificationContainer.ShowNotification #GameStartPlayerHeroImgContainerSelf
{
	transform: translateX(0%);
	opacity: 1;
	animation-name: pos-easeInElastic-left;
	animation-duration: 2s;
	animation-timing-function: linear;
}

#GameStartNotificationContainer.HideNotification #GameStartPlayerHeroImgContainerSelf
{
	opacity: 0;
	animation-name: pos-out-left;
	animation-duration: 0.5s;
	animation-timing-function: ease-in-out;
}

#GameStartNotificationContainer.ShowNotification #GameStartPlayerHeroImgContainerEnemy
{
	transform: translateX(0%);
	opacity: 1;
	animation-name: pos-easeInElastic-right;
	animation-duration: 2s;
	animation-timing-function: linear;
}

#GameStartNotificationContainer.HideNotification #GameStartPlayerHeroImgContainerEnemy
{
	opacity: 0;
	animation-name: pos-out-right;
	animation-duration: 0.5s;
	animation-timing-function: ease-in-out;
}

.GameStartPlayerHeroImg
{
	width: 100%;
	height: 100%;
	background-position: center center;
	background-repeat: no-repeat;
	background-image: url("s2r://");
	overflow: noclip;
}

#GameStartPlayerInfoMain
{
	height: 400px;
	width: 1300px;
	margin-bottom: 50px;
	align: left bottom;
	z-index: 2;
}

#GameStartNotificationEnemy #GameStartPlayerInfoMain
{
	margin-bottom: 0px;
	align: right top;
}

.GameStartPlayerInfoMainBackContainer
{
	height: 130px;
	width: 1100px;
	flow-children: down;
	z-index: 1;
	overflow: noclip;
}

#GameStartPlayerInfoMainBackContainerSelf
{
	align: left center;
}

#GameStartPlayerInfoMainBackContainerEnemy
{
	align: right center;
}

#GameStartNotificationContainer.ShowNotification #GameStartPlayerInfoMainBackContainerSelf
{
	transform: translateX(0%);
	opacity: 1;
	animation-name: pos-easeInBounce-left;
	animation-duration: 1s;
	animation-timing-function: linear;
}

#GameStartNotificationContainer.HideNotification #GameStartPlayerInfoMainBackContainerSelf
{
	opacity: 0;
	animation-name: pos-out-left;
	animation-duration: 0.2s;
	animation-timing-function: ease-out;
}

#GameStartNotificationContainer.ShowNotification #GameStartPlayerInfoMainBackContainerEnemy
{
	transform: translateX(0%);
	opacity: 1;
	animation-name: pos-easeInBounce-right;
	animation-duration: 1s;
	animation-timing-function: linear;
}

#GameStartNotificationContainer.HideNotification #GameStartPlayerInfoMainBackContainerEnemy
{
	opacity: 0;
	animation-name: pos-out-right;
	animation-duration: 0.2s;
	animation-timing-function: ease-out;
}

#GameStartPlayerInfoMainBack1
{
	width: 100%;
	height: 1px;
	background-color: gradient(linear, 0% 100%, 100% 0%, from(#ffffff), color-stop(0.2, #ffffff), to(rgba(0, 0, 0, 0)));
}

#GameStartNotificationEnemy #GameStartPlayerInfoMainBack1
{
	transform: rotateY(180deg);
}

#GameStartPlayerInfoMainBack2
{
	width: 100%;
	height: 125px;
	background-color: gradient(linear, 0% 100%, 100% 0%, from(rgba(255, 63, 63, 0.774)), color-stop(0.2, rgba(255, 63, 63, 0.774)), to(rgba(0, 0, 0, 0)));
}

#GameStartNotificationEnemy #GameStartPlayerInfoMainBack2
{
	transform: rotateY(180deg);
	background-color: gradient(linear, 0% 100%, 100% 0%, from(rgba(63, 63, 255, 0.774)), color-stop(0.2, rgba(63, 63, 255, 0.774)), to(rgba(0, 0, 0, 0)));
}

#GameStartPlayerInfoMainBack3
{
	width: 100%;
	height: 1px;
	background-color: gradient(linear, 0% 100%, 100% 0%, from(#ffffff), color-stop(0.2, #ffffff), to(rgba(0, 0, 0, 0)));
}

#GameStartNotificationEnemy #GameStartPlayerInfoMainBack3
{
	transform: rotateY(180deg);
}

.GameStartPlayerInfoContainer
{
	width: 340px;
	height: 250px;
	margin-top: 70px;
	align: left top;
	z-index: 3;
	animation-duration: 1.3s;
	animation-timing-function: linear;
	transition-property: transform;
	transition-duration: 0.3s;
	transition-timing-function: ease-in-out;
}

#GameStartPlayerInfoContainerEnemy
{
	margin-bottom: 76px;
	align: right bottom;
}

#GameStartNotificationContainer.ShowNotification #GameStartPlayerInfoContainerSelf
{
	transform: translateX(0%);
	opacity: 1;
	animation-name: pos-easeInBounce-left;
	animation-duration: 1.5s;
	animation-timing-function: linear;
}

#GameStartNotificationContainer.HideNotification #GameStartPlayerInfoContainerSelf
{
	transform: translateX(0%) translateY(450px);
}

#GameStartNotificationContainer.ShowNotification #GameStartPlayerInfoContainerEnemy
{
	transform: translateX(0%);
	opacity: 1;
	animation-name: pos-easeInBounce-right;
	animation-duration: 1.5s;
	animation-timing-function: linear;
}

#GameStartNotificationContainer.HideNotification #GameStartPlayerInfoContainerEnemy
{
	transform: translateX(0%) translateY(-450px);
}

#GameStartPlayerHeroBack
{
	height: 130px;
	width: 1100px;
	z-index: 1;
	background-image: url("s2r://panorama/images/custom_game/player_spell/full_art/10014_psd_png.vtex");
	background-size: 100% 100%;
}

#GameStartNotificationSelf #GameStartPlayerHeroBack
{
	align: left center;
}

#GameStartPlayerDefaultImg
{
	width: 200px;
	height: 200px;
	align: center top;
	background-size: 100% 100%;
	background-color: #222222;
	background-image: url("s2r://panorama/images/custom_game/hud/106863163_png_png.vtex");
	border: 5px solid rgba(255, 210, 120, 0.815);
	border-radius: 32px;
}

#GameStartPlayerImg
{
	width: 200px;
	height: 200px;
	border: 2px solid rgba(255, 210, 120, 0.815);
}

#GameStartPlayerName
{
	align: center bottom;
	width: 340px;
	height: 37px;
	text-align: center;
	overflow: noclip;
	text-overflow: shrink;
	color: #fff3c5;
	font-size: 35px;
	font-weight: bold;
	text-shadow: 0px 0px 5px 5 #000000;
	brightness: 1;
}

.GameStartPlayerHeroInfoContainer
{
	height: 65px;
	flow-children: right;
	z-index: 2;
	animation-duration: 0.9s;
	animation-timing-function: linear;
}

#GameStartPlayerHeroInfoContainerSelf
{
	margin-left: 330px;
	align: left center;
}

#GameStartPlayerHeroInfoContainerEnemy
{
	margin-right: 330px;
	align: right center;
}

#GameStartNotificationContainer.ShowNotification #GameStartPlayerHeroInfoContainerSelf
{
	transform: translateX(0%);
	opacity: 1;
	animation-name: pos-easeInBounce-left;
	animation-duration: 1.2s;
	animation-timing-function: linear;
}

#GameStartNotificationContainer.HideNotification #GameStartPlayerHeroInfoContainerSelf
{
	opacity: 0;
	animation-name: pos-out-left;
	animation-duration: 0.5s;
	animation-timing-function: ease-out;
}

#GameStartNotificationContainer.ShowNotification #GameStartPlayerHeroInfoContainerEnemy
{
	transform: translateX(0%);
	opacity: 1;
	animation-name: pos-easeInBounce-right;
	animation-duration: 1.2s;
	animation-timing-function: linear;
}

#GameStartNotificationContainer.HideNotification #GameStartPlayerHeroInfoContainerEnemy
{
	opacity: 0;
	animation-name: pos-out-right;
	animation-duration: 0.5s;
	animation-timing-function: ease-out;
}

#GameStartPlayerHeroName
{
	align: center bottom;
	vertical-align: center;
	height: 45px;
	text-align: center;
	overflow: noclip;
	text-overflow: shrink;
	color: #ffffff;
	font-size: 43px;
	font-weight: bold;
	text-shadow: 0px 0px 5px 5 #000000;
	brightness: 1;
}

#GameStartPlayerHeroAb
{
	height: 65px;
	width: 65px;
	border-radius: 20px;
	vertical-align: center;
	border: 1px solid #c2c4a7;
}

#GameStartNotificationEnemy
{
	align: right top;
}

#FastWarCardPreUsingTipRoot
{
	z-index: 5;
	width: 400px;
	height: 200px;
	opacity: 0;
	overflow: noclip;
	transition-property: opacity;
	transition-duration: 0.2s;
	transition-timing-function: linear;
	animation-duration: 0.2s;
	animation-timing-function: linear;
}

#FastWarCardPreUsingTipRoot.Hide
{
	opacity: 0;
	animation-name: ScaleHideEaseInOutElastic;
}

#FastWarCardPreUsingTipRoot.Show
{
	opacity: 0.3;
	animation-name: ScaleShowEaseInOutElastic;
}

#FastWarCardPreUsingTipTextContainer
{
	width: 400px;
	overflow: noclip;
	align: center center;
}

#FastWarCardPreUsingTipText
{
	width: 400px;
	height: 37px;
	text-align: center;
	overflow: noclip;
	color: #fff3c5;
	font-size: 35px;
	font-weight: bold;
	text-shadow: 0px 0px 5px 5 #000000;
	brightness: 1;
	transition-property: opacity, color, text-shadow;
	transition-duration: 0.2s;
	transition-timing-function: linear;
}

#FastWarCardPreUsingTipText.CantUsing
{
	color: #aa5f4e;
	text-shadow: 0px 0px 3px 3 #4b4b4b;
}

#FastWarCardPreUsingTipText.CanUsing
{
	color: #fff3c5;
	text-shadow: 0px 0px 5px 5 #000000;
}

@keyframes 'ScaleShowCanUsing'
{
	0%
	{
		brightness: 0.5;
		transform: scale3d(1, 1, 1) translate3d(0px, 0px, 0px);
	}
	
	50%
	{
		brightness: 1.5;
		transform: scale3d(1.1, 1.1, 1) translate3d(0px, -5px, 0px);
	}
	
	100%
	{
		brightness: 0.5;
		transform: scale3d(1, 1, 1) translate3d(0px, 0px, 0px);
	}
	
}

@keyframes 'ScaleShowEaseInOutElastic'
{
	0%
	{
		transform: scale3d(0, 0, 1);
	}
	
	4%
	{
		transform: scale3d(0, 0, 1);
	}
	
	8%
	{
		transform: scale3d(0, 0, 1);
	}
	
	18%
	{
		transform: scale3d(-0.01, -0.01, 1);
	}
	
	20%
	{
		transform: scale3d(0, 0, 1);
	}
	
	28%
	{
		transform: scale3d(0.02, 0.02, 1);
	}
	
	30%
	{
		transform: scale3d(0.02, 0.02, 1);
	}
	
	38%
	{
		transform: scale3d(-0.09, -0.09, 1);
	}
	
	40%
	{
		transform: scale3d(-0.12, -0.12, 1);
	}
	
	60%
	{
		transform: scale3d(1.12, 1.12, 1);
	}
	
	62%
	{
		transform: scale3d(1.09, 1.09, 1);
	}
	
	70%
	{
		transform: scale3d(1.09, 1.09, 1);
	}
	
	72%
	{
		transform: scale3d(0.98, 0.98, 1);
	}
	
	80%
	{
		transform: scale3d(1, 1, 1);
	}
	
	82%
	{
		transform: scale3d(1.01, 1.01, 1);
	}
	
	90%
	{
		transform: scale3d(1, 1, 1);
	}
	
	92%
	{
		transform: scale3d(1, 1, 1);
	}
	
	100%
	{
		transform: scale3d(1, 1, 1);
	}
	
}

@keyframes 'ScaleHideEaseInOutElastic'
{
	0%
	{
		transform: scale3d(1, 1, 1);
	}
	
	100%
	{
		transform: scale3d(0, 0, 1);
	}
	
}

#FastWarPlayerHeroRoot
{
	width: 560px;
	height: 750px;
	align: left bottom;
	margin-left: 620px;
	z-index: 4;
}

#FastWarPlayerHeroSpellDescContainer
{
	width: 400px;
	height: 200px;
	align: right bottom;
	z-index: 2;
	opacity: 0;
	transform: scale3d(0.1, 1, 1) translate3d(-200px, 0px, 0px);
	transition-property: transform;
	transition-duration: 0.3s;
	transition-timing-function: ease-in-out;
}

#FastWarPlayerHeroSpellDescContainer.ShowFastWarPlayerHeroBack
{
	transform: scale3d(1, 1, 1) translate3d(0px, 0px, 0px);
	opacity: 1;
}

#HeroSpellDescMain
{
	width: 400px;
	height: 150px;
	align: center center;
	margin-bottom: 10px;
	background-image: url("s2r://panorama/images/custom_game/card/bg/card_tier_2_png_png.vtex");
	background-size: 100% 100%;
	background-repeat: no-repeat;
	background-position: 50% 50%;
}

#HeroSpellDescBack
{
	width: 100%;
	height: 100%;
	align: center top;
	brightness: 2;
	border-radius: 40px;
	background-image: url("s2r://panorama/images/custom_game/hud/battlepass_levelup_popup_background_psd_png.vtex");
	background-size: 100% 100%;
	opacity-mask: url("s2r://panorama/images/custom_game/hud/treasure_image_mask_psd_png.vtex");
	background-img-opacity: 0.5;
	z-index: 1;
}

#HeroSpellDescLabel
{
	padding-left: 35px;
	padding-right: 35px;
	padding-bottom: 20px;
	padding-top: 20px;
	align: center center;
	text-align: left;
	text-overflow: shrink;
	color: #fff09d;
	font-size: 20px;
	text-shadow: 0px 0px 5px 3 #000000;
}

#FastWarPlayerHeroSpellContainer
{
	width: 230px;
	height: 230px;
	align: left bottom;
	z-index: 3;
}

#FastWarPlayerHeroSpellContainer.Usable
{
	brightness: 1;
}

#FastWarPlayerHeroSpellContainer.Unusable
{
	brightness: 0.5;
}

#FastWarPlayerHeroSpellHover
{
	width: 230px;
	height: 230px;
	padding-left: 50px;
	padding-right: 50px;
	padding-bottom: 50px;
	padding-top: 50px;
	align: left bottom;
	border-radius: 50%;
}

#PlayerHeroSpellCountingIndicator
{
	width: 230px;
	height: 230px;
	z-index: 4;
}

#CountingIndicator
{
	align: center top;
	margin-top: 30px;
	width: 140px;
	height: 50px;
	background-image: url("s2r://panorama/images/custom_game/hud/mask_count_png.vtex");
	background-size: 100% 100%;
}

#FastWarPlayerHeroSpellIconContainer
{
	width: 108px;
	height: 108px;
	margin-bottom: 10px;
	align: center center;
	border-radius: 50%;
	z-index: 2;
}

.Usable #FastWarPlayerHeroSpellIconContainer
{
	wash-color: #ffffff;
}

.Unusable #FastWarPlayerHeroSpellIconContainer
{
	wash-color: rgba(189, 189, 189, 0.689);
}

#FastWarPlayerHeroSpellCooldownClip
{
	width: 100%;
	height: 100%;
	align: center center;
	clip: radial(50% 50%, 0deg, 0deg);
	background-color: rgba(145, 145, 145, 0.403);
	z-index: 1;
	transform: rotateY(180deg);
	transition-property: clip, background-color;
	transition-duration: 0.1s;
	transition-timing-function: linear;
}

#FastWarPlayerHeroSpellCooldownClipCoolDown
{
	width: 100%;
	height: 45px;
	text-align: center;
	color: #b1b1b1;
	font-size: 43px;
	font-weight: bold;
	text-shadow: 0px 0px 5px 5 #000000;
	align: center center;
	z-index: 2;
	opacity: 0.8;
}

#FastWarPlayerHeroSpellIcon
{
	box-shadow: 0 3px 20px #efe93760;
	width: 100%;
	height: 100%;
	transform: scale3d(1, 1, 1);
	transition-property: transform,brightness;
	transition-duration: 0.3s;
	transition-timing-function: ease-in-out;
	box-shadow: 0 3px 20px #ffffff00;
}

#FastWarPlayerHeroSpellIcon.ShowFastWarPlayerHeroBack
{
	box-shadow: 0 3px 20px #fffc9795;
	brightness: 1;
	transform: scale3d(1.1, 1.1, 1);
}

#FastWarPlayerHeroSpellIconContainer:hover #FastWarPlayerHeroSpellIcon
{
	transform: scale3d(1.1, 1.1, 1);
}

#FastWarPlayerHeroSpellBackContainer
{
	width: 230px;
	height: 230px;
	brightness: 1;
	transform: rotateZ(0deg) translate3d(0px, 0px, 0px);
	transition-property: transform;
	transition-duration: 0.3s;
	transition-timing-function: linear;
	z-index: 3;
}

#FastWarPlayerHeroSpellBackContainer.ShowFastWarPlayerHeroBack
{
	transform: rotateZ(-90deg) translate3d(3px, -3px, 0px);
}

#FastWarPlayerHeroSpellPar
{
	width: 230px;
	height: 230px;
	margin-bottom: 4px;
	z-index: 1;
}

#FastWarPlayerHeroSpellUsingPar
{
	width: 230px;
	height: 230px;
	margin-bottom: 4px;
	z-index: 9 ;
}

#FastWarPlayerHeroSpellBack
{
	width: 230px;
	height: 230px;
	brightness: 1;
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_01-0_psd_png.vtex");
	background-size: 230px 242px;
	background-position: 50% 0%;
	background-repeat: no-repeat;
	z-index: 3;
}

#FastWarPlayerHeroSpellCostContainer
{
	align: center bottom;
	margin-bottom: 40px;
	z-index: 3;
	transform: rotateZ(0deg);
	transition-property: transform;
	transition-duration: 0.3s;
	transition-timing-function: linear;
}

.ShowFastWarPlayerHeroBack #FastWarPlayerHeroSpellCostContainer
{
	align: center bottom;
	margin-bottom: 40px;
	z-index: 3;
	transform: rotateZ(90deg);
}

#FastWarPlayerHeroSpellCostLabel
{
	width: 32px;
	height: 42px;
	overflow: noclip;
	text-align: center;
	text-overflow: shrink;
	color: #ffffff;
	font-size: 40px;
	font-weight: bold;
	text-shadow: 0px 0px 5px 5 #000000;
	align: center center;
	z-index: 2;
}

#FastWarPlayerHeroSpellCostBack
{
	width: 37px;
	height: 54px;
	align: center center;
	wash-color: #ffffff;
	background-image: url("s2r://panorama/images/custom_game/hud/hex_icon_rarity_shadow_tier_5_large_selected_psd_png.vtex");
	background-size: 100% 100%;
	z-index: 1;
}

#FastWarPlayerHeroMain
{
	width: 400px;
	height: 550px;
	align: right bottom;
	z-index: 1;
	background-image: url("s2r://panorama/images/custom_game/hud/tutorial_background_png_png.vtex");
	background-size: 100%;
	background-repeat: no-repeat;
	transition-property: opacity,transform;
	transition-duration: 0.3s;
	transition-timing-function: ease-in-out;
	opacity: 0;
	transform: translate3d(-800px, 800px, 0px) scale3d(0.2, 0.2, 1);
}

#FastWarPlayerHeroMain.ShowFastWarPlayerHeroBack
{
	opacity: 1;
	transform: translate3d(0px, 0px, 0px) scale3d(1, 1, 1);
}

#FastWarPlayerHeroBackContainer
{
	width: 400px;
	height: 400px;
	align: center top;
	margin-top: 20px;
	opacity: 0;
	transition-property: opacity,transform;
	transition-duration: 0.3s;
	transition-timing-function: ease-in-out;
	transform: translate3d(500px, 800px, 0px) scale3d(0.2, 0.2, 1);
}

#FastWarPlayerHeroBackContainer.ShowFastWarPlayerHeroBack
{
	opacity: 1;
	transform: translate3d(0px, 0px, 0px) scale3d(1, 1, 1);
}

#FastWarPlayerHeroBackBorder
{
	width: 83%;
	height: 83%;
	border: 4px solid #bbb185;
	border-radius: 50%;
	margin-right: 1px;
	margin-bottom: 1px;
	align: center center;
	z-index: 10;
}

#FastWarPlayerHeroBack
{
	width: 83%;
	height: 83%;
	align: center center;
	z-index: 2;
	animation-name: Wobble;
	animation-duration: 5s;
	animation-timing-function: ease-in-out;
	animation-iteration-count: infinite;
	opacity-mask: url("s2r://panorama/images/custom_game/player_spell/mask3_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/full_art/10054_psd_png.vtex");
	background-size: 100% 100%;
	background-repeat: no-repeat;
	background-position: 50% 50%;
}

@keyframes 'Wobble'
{
	0%
	{
		transform: scaleX(1) scaleY(1.01) translate3d(0px, 0px, 0px);
	}
	
	25%
	{
		transform: scaleX(1.01) scaleY(1) translate3d(3px, -1px, 0px);
	}
	
	50%
	{
		transform: scaleX(1) scaleY(1.01) translate3d(-2px, 4px, 0px);
	}
	
	75%
	{
		transform: scaleX(1.01) scaleY(1) translate3d(1px, 5px, 0px);
	}
	
	100%
	{
		transform: scaleX(1) scaleY(1.01) translate3d(0px, 0px, 0px);
	}
	
}

#FastWarPlayerHeroBackSceneBackPar
{
	width: 100%;
	height: 100%;
	align: center center;
	z-index: 3;
}

#FastWarCardUnitRealTimeDetailsRoot
{
	width: 500px;
	height: 700px;
	align: left bottom;
	opacity: 0;
	z-index: 6;
	transform: scale3d(0.2, 0.2, 0.2) translate3d(-200px, 200px, 0px);
	transition-property: opacity,transform;
	transition-duration: 0.2s;
	transition-timing-function: linear;
}

#FastWarCardUnitRealTimeDetailsRoot.ShowDetails
{
	opacity: 1;
	transform: scale3d(1, 1, 1) translate3d(0px, 0px, 0px);
}

#RealTimeDetailsBuff
{
	height: 50px;
	margin-top: 340px;
	margin-left: 32px;
	z-index: 8;
}

#RealTimeDetailsBuffLine
{
	width: 250px;
	height: 2px;
	margin-top: 390px;
	margin-left: 28px;
	z-index: 9;
	background-color: gradient(linear, 0% 100%, 100% 0%, from(rgba(255, 239, 173, 0.542)), to(rgba(0, 0, 0, 0)));
}

#FastWarCardDetailsContainer
{
	width: 500px;
	height: 700px;
	z-index: 1;
}

#FastWarCardDetailsNameContainer
{
	width: 480px;
	height: 40px;
	margin-top: 80px;
	align: center top;
	background-color: gradient(linear, 0% 100%, 100% 0%, from(rgba(0, 0, 0, 0)), color-stop(0.15, rgba(0, 0, 0, 0)), color-stop(0.5, rgba(255, 205, 188, 0.627)), color-stop(0.85, rgba(0, 0, 0, 0)), to(#ffffff00));
	z-index: 8;
}

#FastWarCardDetailsUnitNum
{
	align: right bottom;
	margin-bottom: 65px;
	margin-right: 30px;
	height: 62px;
	text-align: left;
	font-size: 60px;
	text-shadow: 0px 0px 3px 5 #00000073;
	color: #ffffff;
	overflow: noclip;
	z-index: 12;
}

.CenterFlow
{
	horizontal-align: center;
	vertical-align: center;
	flow-children: right;
}

#FastWarCardDetailsLogo
{
	width: 40px;
	height: 40px;
	background-image: url("s2r://panorama/images/custom_game/hud/main_png.vtex");
	background-size: 100% 100%;
	opacity: 0;
}

#FastWarCardDetailsLogo.ShowMain
{
	opacity: 1;
	background-image: url("s2r://panorama/images/custom_game/hud/main_png.vtex");
	background-size: 100% 100%;
}

#FastWarCardDetailsLogo.ShowSub
{
	opacity: 1;
	background-image: url("s2r://panorama/images/custom_game/hud/sub_png.vtex");
	background-size: 100% 100%;
}

#FastWarCardDetailsName
{
	height: 40px;
	text-overflow: clip;
	color: #fff09d;
	font-size: 38px;
	text-shadow: 0px 0px 5px 3 #000000;
}

#FastWarCardDetailsSpellContainer
{
	align: center center;
	flow-children: right;
	margin-top: 470px;
	margin-left: 150px;
	z-index: 9;
}

.FastWarSpellContainer
{
	margin-left: 5px;
	margin-right: 5px;
	border-radius: 50%;
	height: 90px;
	width: 90px;
}

#FastWarSpellBorder
{
	border-radius: 50%;
	border: 3px solid #adadadbd;
	z-index: 9;
}

#FastWarSpellImage
{
	height: 88px;
	width: 88px;
	border-radius: 50%;
	align: center center;
	tooltip-position: right;
	z-index: 1;
}

#FastWarSpellSilenced
{
	height: 100%;
	width: 100%;
	z-index: 3;
	opacity: 0;
	background-image: url("s2r://panorama/images/hud/reborn/spells_silenced_psd.vtex");
	background-size: 100% 100%;
	background-color: rgba(47, 47, 47, 0.662);
}

#FastWarSpellSilenced.ShowDisable
{
	opacity: 1;
}

#FastWarSpellDisable
{
	height: 100%;
	width: 100%;
	z-index: 3;
	opacity: 0;
	background-image: url("s2r://panorama/images/hud/reborn/passives_broken_psd.vtex");
	background-size: 100% 100%;
	background-color: rgba(47, 47, 47, 0.662);
}

#FastWarSpellDisable.ShowDisable
{
	opacity: 1;
}

#FastWarSpellCoolDown
{
	height: 100%;
	width: 100%;
	z-index: 2;
	opacity: 0;
}

#FastWarSpellCoolDown.ShowCooldown
{
	opacity: 1;
}

#CoolDownTimer
{
	width: 100%;
	align: center center;
	height: 31px;
	text-align: center;
	font-size: 30px;
	margin-bottom: 5px;
	text-shadow: 0px 0px 3px 5 #00000073;
	color: #ffffff;
	overflow: noclip;
	z-index: 3;
}

#CoolDownOverlay
{
	height: 100%;
	width: 100%;
	clip: radial(50% 50%, 0deg, 0deg);
	background-color: rgba(0, 0, 0, 0.899);
	z-index: 1;
	transform: rotateY(180deg);
	transition-property: clip, background-color;
	transition-duration: 0.1s;
	transition-timing-function: linear;
}

#FastWarCardDetailsMainTextContainer
{
	height: 300px;
	width: 440px;
	align: center bottom;
	z-index: 3;
	flow-children: right;
}

.DetailsContainer
{
	height: 330px;
	width: 100%;
	flow-children: down;
}

#DetailsExtra
{
	align: right center;
}

#DetailsBase
{
	align: left center;
}

.DetailsBaseRow
{
	width: 100%;
	margin-top: 2px;
	margin-bottom: 2px;
	overflow: noclip;
}

.RowLabel
{
	overflow: noclip;
}

.RowValue
{
	margin-left: 100px;
	flow-children: right;
	overflow: noclip;
}

#Duration
{
	color: #ff6c6c;
}

.MainText
{
	height: 27px;
	text-align: left;
	font-size: 25px;
	text-shadow: 0px 0px 3px 5 #00000073;
	color: #ffe491;
	overflow: noclip;
}

.BonusMainText
{
	height: 27px;
	text-align: left;
	font-size: 25px;
	text-shadow: 0px 0px 3px 5 #00000073;
	color: #95ff91;
	overflow: noclip;
}

.SecondText
{
	height: 22px;
	text-align: left;
	font-size: 20px;
	text-shadow: 0px 0px 3px 5 #00000073;
	color: #ffc491;
	overflow: noclip;
}

#AttackRangeRow .RowText
{
	color: #c0ffa7;
}

#VisionRow .RowText
{
	color: #ffa7a7;
}

.RowText
{
	height: 18px;
	text-align: left;
	font-size: 16px;
	text-shadow: 0px 0px 3px 5 #00000073;
	color: #fffaec;
}

.BonusRowText
{
	height: 18px;
	text-align: left;
	font-size: 16px;
	text-shadow: 0px 0px 3px 5 #00000073;
	color: #b9ffaa;
}

.FastWarCardDetailsLabelContainer
{
	height: 15%;
	width: 100%;
}

#FastWarCardDetailsSceneContainer
{
	width: 500px;
	height: 700px;
	align: center center;
}

#FastWarCardDetailsScene
{
	width: 450px;
	height: 570px;
	align: center center;
	z-index: 1;
	border: 3px solid #898989;
}

.FriendDetail #FastWarCardDetailsScene
{
	border: 3px solid #b3ffa4;
}

.EnemyDetail #FastWarCardDetailsScene
{
	border: 3px solid #ff6161;
}

#FastWarCardDetailsSceneBackPar
{
	width: 500px;
	height: 700px;
	align: center center;
}

#FastWarCardDetailsSceneBack
{
	width: 500px;
	height: 700px;
	align: center center;
	z-index: 0;
	wash-color: #ffffff;
	background-color: none;
	box-shadow: none;
	background-image: url("s2r://panorama/images/custom_game/hud/tutorial_background_png_png.vtex");
	background-size: 100%;
	background-repeat: no-repeat;
}

.FriendDetail #FastWarCardDetailsSceneBack
{
	wash-color: #dbffbc;
}

.EnemyDetail #FastWarCardDetailsSceneBack
{
	wash-color: #ffbcbc;
}

#FastWarCardDetailsSceneBack2
{
	width: 450px;
	height: 450px;
	align: center center;
	z-index: 9;
}

#FastWarCardDetailsSpellBack
{
	width: 450px;
	height: 450px;
	align: center center;
	margin-bottom: 120px;
	z-index: 1;
	border: 3px solid #898989;
	opacity-mask: url("s2r://panorama/images/custom_game/card/bg/mask3_png.vtex");
	opacity: 1;
	background-size: 450px 450px ;
	background-position: 50% 50%;
	background-repeat: no-repeat;
}

#FastWarCardDetailsBottomLabelContain
{
	width: 450px;
	height: 150px;
	align: center bottom;
	margin-bottom: 50px;
}

#FastWarCardDetailsSpellLabelBack
{
	width: 450px;
	height: 150px;
	background-image: url("s2r://panorama/images/custom_game/hud/battlepass_levelup_popup_background_psd_png.vtex");
	background-size: 100% 100%;
	opacity-mask: url("s2r://panorama/images/custom_game/hud/treasure_image_mask_psd_png.vtex");
	brightness: 3;
}

.FastWarCardDetailsSpellDesc
{
	width: 450px;
	height: 150px;
	padding-left: 20px;
	padding-right: 20px;
	padding-bottom: 15px;
	padding-top: 15px;
	font-size: 24px;
	text-overflow: shrink;
	text-shadow: 0px 0px 3px 5 #00000073;
	color: #ffeeba;
	z-index: 2;
}

#FastWarDeckRoot
{
	width: 1960px;
	height: 1080px;
	background-color: #424242;
	border: 4px solid #000000;
	background-image: url("s2r://panorama/images/custom_game/hud/hud_deckinfo_bg_psd_png.vtex");
	background-size: 100% 100%;
	opacity: 0;
	z-index: 6;
	transition-property: opacity,transform;
	transition-duration: 0.4s;
	transition-timing-function: ease-in-out;
	transform: scale3d(0.2, 0.2, 1) translate3d(1000px, 800px, 0px);
}

#FastWarDeckRoot.Show
{
	transform: scale3d(1, 1, 1) translate3d(0px, 0px, 0px);
	opacity: 1;
}

#FastWarModeChooseContainer
{
	width: 1960px;
	height: 1080px;
	z-index: 4;
}

.OpenDeck
{
	opacity: 1;
	animation-duration: 0.7s;
	animation-timing-function: linear;
	transition-property: opacity;
	transition-duration: 0.7s;
}

.OpenDeck.Next
{
	animation-name: pos-easeInElastic-right2;
}

.OpenDeck.Pre
{
	animation-name: pos-easeInElastic-left;
}

.CloseDeck
{
	opacity: 0;
	transition-property: opacity;
	transition-duration: 0.2s;
	animation-duration: 0.2s;
	animation-timing-function: linear;
}

.CloseDeck.Next
{
	animation-name: pos-out-right;
}

.CloseDeck.Pre
{
	animation-name: pos-out-left;
}

#FastWarModeChooseTitle
{
	width: 1000px;
	height: 70px;
	align: center top;
	margin-top: 50px;
}

#FastWarModeChooseTitletext
{
	height: 70px;
	align: center center;
	text-align: center;
	font-size: 68px;
	font-weight: bold;
	text-overflow: shrink;
	text-shadow: 0px 0px 3px 5 #00000073;
	color: #d3cd9e;
	z-index: 2;
}

#FastWarModeChooseTitleBack
{
	width: 100%;
	height: 100%;
	align: center center;
	background-color: gradient(linear, 0% 100%, 100% 100%, from(rgba(0, 0, 0, 0)), color-stop(0.1, rgba(0, 0, 0, 0)), color-stop(0.5, rgba(210, 210, 210, 0.586)), color-stop(0.9, rgba(0, 0, 0, 0)), to(#ffffff00));
}

#FastWarModeChooseBack
{
	width: 100%;
	height: 100%;
	z-index: 2;
	opacity: 1;
}

#FastWarModeChooseBack.Host
{
	opacity: 0;
}

#FastWarModeGroupList
{
	width: 1600px;
	height: 800px;
	align: center center;
	z-index: 1;
}

#FastWarModeGroupContainer
{
	width: 1760px;
	height: 900px;
	align: center center;
	opacity-mask: url("s2r://panorama/images/custom_game/hud/mask_big_back_png.vtex");
}

.FastWarModeGroup
{
	width: 760px;
	height: 800px;
	tooltip-position: top;
	transform: scale3d(0.2, 0.2, 1);
	transition-property: opacity,transform;
	transition-duration: 0.2s;
	transition-timing-function: ease-in-out;
}

#FastWarModeGroupButton
{
	width: 760px;
	height: 700px;
	z-index: 2;
}

#FastWarModeGroupButton .FastWarButtonBack
{
	width: 560px;
	height: 400px;
	align: center top;
	border-radius: 10px;
	background-size: 100% 100%;
	background-image: url("s2r://panorama/images/game_modes/randomdraft_popup_png.vtex");
}

#FastWarModeGroupButton .FastWarButtonLabel
{
	font-size: 60px;
	margin-bottom: 90px;
}

#FastWarModeGroupBan
{
	width: 150px;
	height: 150px;
	margin-bottom: 90px;
	z-index: 9;
	align: center center;
	background-image: url("s2r://panorama/images/custom_game/hud/icon_x_png.vtex");
	background-size: 100% 100%;
	opacity: 0;
	transition-property: opacity,transform;
	transition-duration: 0.2s;
	transition-timing-function: ease-in-out;
}

.GroupUnReady #FastWarModeGroupBan
{
	opacity: 1;
}

#FastWarModeGroupTip
{
	width: 100%;
	height: 180px;
	align: center top;
	margin-top: 400px;
	padding-left: 15px;
	padding-right: 15px;
	opacity: 0;
	transition-property: opacity,transform;
	transition-duration: 0.2s;
	transition-timing-function: ease-in-out;
	z-index: 3;
}

.ChooseGroup #FastWarModeGroupTip
{
	opacity: 1;
}

#FastWarModeGroupTipText
{
	align: center center;
	width: 100%;
	text-align: left;
	font-size: 22px;
	font-weight: bold;
	text-overflow: shrink;
	text-shadow: 0px 0px 3px 5 #00000073;
	color: #d3cd9e;
}

#FastWarModeList
{
	z-index: 3;
	width: 100%;
	height: 260px;
	align: center bottom;
	opacity: 0 ;
	transition-property: opacity,transform;
	transition-duration: 0.2s;
	transition-timing-function: ease-in-out;
}

#FastWarModeList VerticalScrollBar,HTMLVerticalScrollBar
{
	opacity: 0;
}

.ChooseGroup #FastWarModeList
{
	opacity: 1;
}

#FastWarModeListContainer
{
	align: center center;
	width: 100%;
	overflow: squish scroll;
	flow-children: down;
}

#FastWarMode
{
	width: 100%;
	height: 50px;
}

#FastWarMode:hover .FastWarButtonBack
{
	background-color: gradient(linear, 0% 100%, 100% 100%, from(rgba(0, 0, 0, 0)), color-stop(0.1, rgba(0, 0, 0, 0)), color-stop(0.5, rgba(245, 255, 103, 0.586)), color-stop(0.9, rgba(0, 0, 0, 0)), to(#ffffff00));
}

#FastWarMode .FastWarButtonBack
{
	height: 25px;
	width: 80%;
	background-img-opacity: 0 ;
}

#FastWarMode .FastWarButtonLabel
{
	font-size: 35px;
	color: #e1ffad;
}

#FastWarMode.ShowButton.UnreadyButton .FastWarButtonLabel
{
	color: #808080;
}

#FastWarMode.ShowButton.ReadyButton .FastWarButtonLabel
{
	color: #e1ffad;
}

#OpenDeckContainer
{
	width: 200px;
	height: 100px;
	align: right bottom;
	margin-bottom: 10px;
	margin-right: 10px;
}

#OpenDeckButton .FastWarButtonLabel
{
	font-size: 40px;
	font-weight: bolder;
}

#CloseDeckContainer
{
	width: 60px;
	height: 60px;
	align: right top;
	margin-top: 10px;
	margin-right: 10px;
	z-index: 10;
}

#CloseDeckButton
{
	opacity: 0.6;
}

#CloseDeckButton .FastWarButtonBack
{
	background-image: url("s2r://panorama/images/custom_game/hud/x_close_filled_png_png.vtex");
}

#PageButton
{
	width: 1960px;
	height: 1080px;
	z-index: 5;
}

#PreviousPageButtonContainer
{
	width: 150px;
	height: 150px;
	align: left center;
	transform: rotateY(180deg);
}

#PreviousPageButton .FastWarButtonBack
{
	background-image: url("s2r://panorama/images/custom_game/hud/large_ui_arrow_psd_png.vtex");
}

#NextPageButtonContainer
{
	width: 150px;
	height: 150px;
	align: right center;
}

#NextPageButton .FastWarButtonBack
{
	background-image: url("s2r://panorama/images/custom_game/hud/large_ui_arrow_psd_png.vtex");
}

#FastWarCnterVSContainer
{
	width: 1200px;
	height: 600px;
	align: center center;
}

#FastWarVSBack
{
	width: 400px;
	height: 400px;
	align: center center;
	background-image: url("s2r://panorama/images/custom_game/hud/vs_png.vtex");
	background-size: 100% 100%;
	opacity: 0;
}

.ShowButton #FastWarVSBack
{
	opacity: 1;
	transform: scale3d(1, 1, 1);
	animation-name: scale-easeInElastic;
	animation-duration: 1s;
}

.HideButton #FastWarVSBack
{
	opacity: 0;
	transform: scale3d(0, 0, 1);
	animation-duration: 1s;
	animation-name: scale-easeOutElastic;
}

@keyframes 'scale-easeInElastic'
{
	0%
	{
		transform: scale3d(0, 0, 1);
	}
	
	50%
	{
		transform: scale3d(0, 0, 1);
	}
	
	60%
	{
		transform: scale3d(1.37, 1.37, 1);
	}
	
	70%
	{
		transform: scale3d(0.87, 0.87, 1);
	}
	
	80%
	{
		transform: scale3d(1.05, 1.05, 1);
	}
	
	90%
	{
		transform: scale3d(0.98, 0.98, 1);
	}
	
	100%
	{
		transform: scale3d(1, 1, 1);
	}
	
}

@keyframes 'scale-easeOutElastic'
{
	0%
	{
		transform: scale3d(1, 1, 1);
	}
	
	16%
	{
		transform: scale3d(0, 0, 1);
	}
	
	28%
	{
		transform: scale3d(0.13, 0.13, 1);
	}
	
	44%
	{
		transform: scale3d(0, 0, 1);
	}
	
	100%
	{
		transform: scale3d(0, 0, 1);
	}
	
}

#FastWarReadyButton
{
	width: 200px;
	height: 90px;
	margin-top: 250px;
	align: right center;
	margin-right: 20px;
	z-index: 99;
}

#GameReadyButton .FastWarButtonBack
{
	background-image: url("s2r://panorama/images/custom_game/hud/levelup_button_psd_png.vtex");
	wash-color: #00ff15;
}

#GameReadyButton .FastWarButtonLabel
{
	color: #f8ff9a;
}

#CancelReadyButton .FastWarButtonBack
{
	wash-color: #ff0000;
}

#CancelReadyButton .FastWarButtonLabel
{
	color: #f8ff9a;
}

#FastWarDeckCardDetailContainer
{
	width: 100%;
	height: 100%;
	z-index: 4;
	opacity: 0;
}

#FastWarDeckCardDetailContainer.ShowDeckCardDetail
{
	opacity: 1;
}

#FastWarDeckCardDetailBack
{
	width: 100%;
	height: 100%;
	z-index: 1;
	background-color: rgba(31, 31, 31, 0.579);
}

#FastWarDeckCardDetailRoot
{
	width: 100%;
	height: 100%;
	z-index: 2;
	opacity: 0;
	overflow: noclip;
	background-color: rgba(27, 27, 27, 0.579);
	transform: scale3d(0.2, 0.2, 1);
	transition-property: opacity,transform;
	transition-duration: 0.2s;
	transition-timing-function: ease-in-out;
}

#FastWarDeckCardDetailRootBack
{
	width: 1200px;
	height: 500px;
	align: center center;
	background-color: gradient(linear, 0% 100%, 100% 100%, from(rgba(0, 0, 0, 0)), color-stop(0.1, rgba(0, 0, 0, 0)), color-stop(0.5, rgba(255, 223, 158, 0.586)), color-stop(0.9, rgba(0, 0, 0, 0)), to(#ffffff00));
}

#FastWarDeckCardDetailRoot.ShowDeckCardDetailMain
{
	transform: scale3d(1, 1, 1) translate3d(0px, 0px, 0px);
	transition-duration: 0s;
	opacity: 1;
}

#DetailContainer
{
	margin-top: 80px;
	width: 1200px;
	height: 700px;
	margin-bottom: 100px;
	align: center center;
}

#TipContainer
{
	width: 800px;
	height: 200px;
	margin-top: 650px;
	align: center center;
}

#TipBack
{
	width: 500px;
	height: 500px;
	overflow: noclip;
	align: left top;
	background-repeat: no-repeat;
	background-position: 0% 50%;
}

#TipText
{
	padding-left: 100px;
	padding-right: 100px;
	padding-top: 20px;
	padding-bottom: 20px;
	margin-top: 80px;
	align: center top;
	width: 800px;
	height: 300px;
	text-align: center;
	font-size: 20px;
	font-weight: bold;
	text-overflow: shrink;
	text-shadow: 0px 0px 3px 5 #00000073;
	color: #826662;
	z-index: 2;
}

#TipAuthor
{
	align: right bottom;
	width: 200px;
	height: 30px;
	text-align: center;
	font-size: 28px;
	font-weight: bold;
	text-overflow: shrink;
	text-shadow: 0px 0px 3px 5 #00000073;
	color: #826662;
	z-index: 2;
}

.FastWarCardDetailsContainer
{
	width: 500px;
	height: 700px;
	overflow: noclip;
	transition-property: opacity,transform,x;
	transition-duration: 0.2s;
	transition-timing-function: ease-in-out;
}

#FilterSlotContainer
{
	flow-children: down;
}

#CardDetailsFilterContainer
{
	width: 200px;
	height: 50px;
	border: 3px solid #ffffff;
	background-color: red;
}

#FastWarDeckMainContainer
{
	width: 100%;
	height: 100%;
	z-index: 3;
}

#FastWarDeckBack
{
	width: 100%;
	height: 100%;
	z-index: 2;
	visibility: collapse;
}

#FastWarDeckBack.ShowDeckBack
{
	visibility: visible;
}

#TestP
{
	width: 200px;
	height: 200px;
	align: center center;
	z-index: 9;
}

#FastWarHandRoot
{
	width: 100%;
	height: 800px;
	align: center bottom;
	margin-top: 50px;
	z-index: 1;
	opacity: 0;
	transform: translate3d(0px, 500px, 0px);
	transition-property: opacity,transform;
	transition-duration: 1s;
	transition-timing-function: linear;
}

#FastWarHandRoot.PhaseShow
{
	opacity: 1;
	transform: translate3d(0px, 0px, 0px);
}

#FastWarPlayerUsingUnitSpellContainer
{
	height: 800px;
	width: 250px;
	margin-right: 10px;
	align: right center;
}

#UnitSpellContainer
{
	align: center bottom;
	flow-children: right-wrap;
	margin-bottom: 80px;
}

#PlayerUsingUnitSpell
{
	width: 125px;
	height: 125px;
}

#PlayerUsingUnitSpellImg
{
	align: center center;
	width: 90px;
	height: 90px;
	border-radius: 50%;
	border: 5px solid #232323;
}

#FastWarDeckCardButton
{
	margin-right: 30px;
	align: right top;
	width: 130px;
	height: 62px;
	z-index: 3;
	opacity: 1;
}

#FastWarDeckCardButton.HideEdit
{
	opacity: 0;
}

.FastWarButton
{
	align: center center;
	width: 80%;
	height: 80%;
	opacity: 0;
	z-index: 5;
}

.FastWarButton:hover
{
	brightness: 1;
	transform: translate3d(1px, -1px, 0px);
}

.FastWarButton:active
{
	brightness: 2;
	transform: translate3d(2px, 2px, 0px);
}

.FastWarButton.HideButton
{
	opacity: 0;
	visibility: collapse;
}

.FastWarButton.ShowButton.UnreadyButton
{
	animation-duration: 0s;
	opacity: 0.1;
}

.FastWarButton.ShowButton.ReadyButton
{
	opacity: 1;
}

.FastWarButton.ShowButton
{
	visibility: visible;
	animation-name: ShowButtonAni;
	animation-duration: 0.6s;
	animation-timing-function: ease-in-out;
	opacity: 1;
}

@keyframes 'ShowButtonAni'
{
	0%
	{
		opacity: 0;
		transform: scale3d(0.3, 0.3, 1) translate3d(0px, 0px, 0px);
	}
	
	30%
	{
		opacity: 0;
		transform: scale3d(0.3, 0.3, 1) translate3d(0px, 0px, 0px);
	}
	
	75%
	{
		opacity: 1;
		transform: scale3d(1.2, 1.2, 1) translate3d(0px, 0px, 0px);
	}
	
	100%
	{
		opacity: 1;
		transform: scale3d(1, 1, 1) translate3d(0px, 0px, 0px);
	}
	
}

.FastWarButtonLabel
{
	align: center center;
	width: 100%;
	text-align: center;
	text-overflow: shrink;
	font-size: 30px;
	font-weight: bold;
	text-shadow: 0px 0px 3px 5 #00000073;
	color: #ffeeba;
	z-index: 2;
}

.FastWarButtonBack
{
	height: 100%;
	width: 100%;
	align: center center;
	border-radius: 10px;
	z-index: 1;
	background-size: 100% 100%;
	background-image: url("s2r://panorama/images/custom_game/hud/open_deck_png.vtex");
}

#FastWarPlayerHeroContainer
{
	width: 350px;
	height: 1020px;
	align: left center;
	margin-top: 20px;
}

#FastWarPlayerReserveHeroContainer
{
	width: 340px;
	height: 610px;
	align: center bottom;
	border-radius: 10px;
	margin-bottom: 15px;
}

#FastWarPlayerReserveHeros
{
	padding: 10px;
	width: 340px;
	height: 590px;
	align: center center;
	flow-children: down;
	overflow: squish scroll;
}

#FastWarPlayerReserveHeroBack
{
	width: 300px;
	height: 610px;
	align: left center;
	border-radius: 20px;
	border-left: 4px solid rgba(50, 70, 70, 0.652);
	border-top: 4px solid rgba(50, 70, 70, 0.652);
	border-bottom: 4px solid rgba(50, 70, 70, 0.652);
	background-color: rgba(0, 0, 0, 0.255);
}

#FastWarPlayerReserveHeros VerticalScrollBar,HTMLVerticalScrollBar
{
	opacity: 0;
}

.FastWarPlayerHeroEasy
{
	width: 350px;
	height: 45px;
	margin-bottom: 8px;
	overflow: noclip;
	transform: translate3d(0px, 0px, 0px);
	transition-property: transform;
	transition-duration: 0.1s;
	transition-timing-function: ease-in-out;
}

.FastWarPlayerHeroEasy:hover
{
	brightness: 1;
	transform: translate3d(12px, 0px, 0px);
}

.FastWarPlayerHeroEasy:active
{
	brightness: 2;
	transform: translate3d(14px, 2px, 0px);
}

.FastWarPlayerHeroEasy.OnSelect
{
	transform: translate3d(12px, 0px, 0px);
	brightness: 1;
}

#FastWarPlayerHeroEasyImg
{
	width: 90px;
	height: 45px;
	vertical-align: center;
	horizontal-align: left;
	background-size: 100% 100%;
	background-position: center 30%;
	background-repeat: no-repeat;
	opacity-mask: url("s2r://panorama/images/custom_game/hud/topbar_heroslant_right_inset_psd_png.vtex");
	z-index: 2;
}

#FastWarPlayerHeroEasyBack
{
	width: 100%;
	height: 35px;
	vertical-align: center;
	horizontal-align: left;
	margin-left: 20px;
	background-color: gradient(linear, 0% 100%, 100% 0%, from(rgba(150, 150, 150, 0.627)), color-stop(0.5, rgba(150, 150, 150, 0.07)), color-stop(0.8, rgba(0, 0, 0, 0)), to(#ffffff00));
	z-index: 1;
	transition-property: background-color;
	transition-duration: 0.2s;
	transition-timing-function: ease-in-out;
}

.FastWarPlayerHeroEasy.OnSelect #FastWarPlayerHeroEasyBack
{
	background-color: gradient(linear, 0% 100%, 100% 0%, from(rgba(255, 245, 166, 0.627)), color-stop(0.6, rgba(117, 104, 56, 0.314)), to(#ffffff00));
}

#FastWarPlayerHeroEasyName
{
	vertical-align: center;
	horizontal-align: left;
	margin-left: 135px;
	width: 160px;
	height: 30px;
	font-size: 27px;
	text-align: left;
	font-weight: bold;
	text-shadow: 0px 0px 3px 5 #00000073;
	color: #ffffffac;
	text-overflow: shrink;
	z-index: 2;
	transition-property: color;
	transition-duration: 0.2s;
	transition-timing-function: ease-in-out;
}

.FastWarPlayerHeroEasy.OnSelect #FastWarPlayerHeroEasyName
{
	color: rgba(115, 255, 55, 0.675);
}

#FastWarPlayerHeroEasySpellIcon
{
	width: 34px;
	height: 34px;
	vertical-align: center;
	horizontal-align: left;
	margin-left: 95px;
	border: 2px solid #324646;
	border-radius: 10px;
	z-index: 2;
}

#FastWarHeroContainer
{
	width: 340px;
	height: 700px;
}

#FastWarPlayerDeckHeroName
{
	align: center top;
	width: 340px;
	height: 90px;
	z-index: 2;
	transition-property: transform, opacity;
	transition-duration: 0.5s;
	transition-timing-function: ease-in-out;
}

.ShowDeck #FastWarPlayerDeckHeroName
{
	opacity: 0;
}

#FastWarPlayerDeckHeroContainer
{
	width: 350px;
	height: 340px;
	margin-top: 30px;
	margin-left: 10px;
	margin-right: 10px;
	opacity: 1;
}

.ShowDeck.EnemyDeck #FastWarPlayerDeckHeroContainer
{
	position: 1483.2px 0px 0px;
}

.ShowReserve.EnemyDeck #FastWarPlayerDeckHeroContainer
{
	position: 0px 0px 0px;
}

#FastWarPlayerHeroTips
{
	height: 50px;
	width: 220px;
	margin-left: 20px;
	align: center top;
	border-radius: 25px;
	z-index: 2;
	background-color: gradient(linear, 0% 100%, 100% 0%, from(rgba(152, 239, 137, 0.082)), to(rgba(73, 89, 135, 0.285)));
}

#RandomDeckHero
{
	align: center top;
	margin-right: 260px;
	width: 40px;
	height: 44px;
	z-index: 9;
	background-image: url("s2r://panorama/images/custom_game/hud/random_png.vtex");
	background-size: 100% 100%;
}

#RandomDeckHero:hover
{
	brightness: 1;
	transform: translate3d(-1px, -1px, 0px);
}

#RandomDeckHero:active
{
	brightness: 2;
	transform: translate3d(2px, 2px, 0px);
}

#FastWarUIPlayerName
{
	height: 40px;
	align: center top;
	z-index: 8;
	flow-children: right;
}

#FastWarUIPlayerNameText1
{
	font-size: 20px;
	height: 22px;
	margin-top: 13px;
	color: rgba(196, 196, 196, 0.621);
}

#FastWarUIPlayerNameText2
{
	font-size: 38px;
	height: 40px;
	color: #ffbe56;
}

.FastWarUIPlayerNameText
{
	text-align: center;
	text-overflow: shrink;
	font-weight: bold;
	text-shadow: 0px 0px 5px 5 #000000;
}

#FastWarPlayerDeckHero
{
	width: 320px;
	height: 320px;
	align: center top;
	border-radius: 50% 50% 10px 10px;
	border: 5px solid rgba(152, 239, 137, 0.274);
	box-shadow: rgba(73, 89, 135, 0.285) 0px 0px 3px 3px;
}

#FastWarPlayerDeckHeroImg
{
	width: 320px;
	height: 320px;
	align: center top;
	background-image: url("s2r://panorama/images/custom_game/player_spell/full_art/10054_psd_png.vtex");
	background-size: 100% 100%;
}

#FastWarPlayerDeckHeroSpellContainer
{
	align: right bottom;
	width: 140px;
	height: 148px;
}

#DFastWarPlayerHeroSpellBack
{
	width: 140px;
	height: 148px;
	brightness: 1;
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_01-0_psd_png.vtex");
	background-size: 100% 100%;
	background-position: 50% 0%;
	background-repeat: no-repeat;
	z-index: 3;
}

#FastWarPlayerDeckHeroSpell
{
	align: center center;
	width: 70px;
	height: 70px;
	border: 2px solid rgba(152, 239, 137, 0.274);
	box-shadow: rgba(73, 89, 135, 0.285) 0px 0px 3px 3px;
	border-radius: 50%;
	margin-bottom: 10px;
	margin-right: 2px;
}

#SpellImg
{
	width: 70px;
	height: 70px;
}

#FastWarPlayerDeckHeroSpellCostContainer
{
	align: center bottom;
	margin-bottom: 28px;
	z-index: 3;
}

#DPlayerHeroSpellCostLabel
{
	width: 22px;
	height: 26px;
	overflow: noclip;
	text-align: center;
	text-overflow: shrink;
	color: #ffffff;
	font-size: 26px;
	font-weight: bold;
	text-shadow: 0px 0px 5px 5 #000000;
	align: center center;
	z-index: 2;
}

#DPlayerHeroSpellCostBack
{
	width: 27px;
	height: 30px;
	align: center center;
	wash-color: #ffffff;
	background-image: url("s2r://panorama/images/custom_game/hud/hex_icon_rarity_shadow_tier_5_large_selected_psd_png.vtex");
	background-size: 100% 100%;
	z-index: 1;
}

#FastWarDeckContainer
{
	width: 100%;
	height: 100%;
	z-index: 1;
}

.FastWarDeckReserveCards
{
	width: 1960px;
	height: 1080px;
	transition-property: transform, opacity;
	transition-duration: 0.5s;
	transition-timing-function: ease-in-out;
	flow-children: down;
	opacity: 0;
}

#FastWarDeckReserveCardsSelf
{
	z-index: 2;
	transform: translate3d(0px, 1080px, 0px);
}

#FastWarDeckReserveCardsSelf.ShowDeck
{
	opacity: 1;
	transform: translate3d(0px, 700px, 0px);
}

#FastWarDeckReserveCardsSelf.ShowReserve
{
	opacity: 1;
	transform: translate3d(0px, 0px, 0px);
}

#FastWarDeckReserveCardsEnemy
{
	z-index: 1;
	transform: translate3d(0px, -1080px, 0px);
}

#FastWarDeckReserveCardsEnemy.ShowDeck
{
	opacity: 1;
	transform: translate3d(0px, 0px, 0px);
}

#FastWarDeckReserveCardsEnemy.ShowReserve
{
	opacity: 1;
	transform: translate3d(0px, 0px, 0px);
}

#FastWarPlayerDeckContainer
{
	height: 370px;
	width: 1960px;
	padding-right: 15px;
	padding-left: 15px;
	opacity: 1;
}

#FastWarPlayerDeckContainer.SelfDeck
{
	flow-children: right;
}

#FastWarPlayerDeckContainer.EnemyDeck
{
	flow-children: left;
}

#FastWarPlayerReserveContainer
{
	height: 700px;
	width: 1960px;
	align: center bottom;
	opacity: 0;
	padding-left: 10px;
	padding-right: 10px;
	transition-property: transform, opacity;
	transition-duration: 0.5s;
	transition-timing-function: ease-in-out;
}

.ShowReserve #FastWarPlayerReserveContainer
{
	opacity: 1;
}

#FastWarPlayerReserveContainer.SelfDeck
{
	flow-children: right;
}

#FastWarPlayerReserveContainer.EnemyDeck
{
	flow-children: left;
}

#FastWarDeckCardCloseButton
{
	width: 50px;
	height: 50px;
	align: right top;
	margin-top: 10px;
	margin-right: 10px;
	z-index: 10;
	opacity: 0.6;
	background-size: 100% 100%;
	background-image: url("s2r://panorama/images/custom_game/hud/x_close_filled_png_png.vtex");
}

#FastWarDeckCardCloseButton:hover
{
	brightness: 1;
	opacity: 1;
	transform: translate3d(-1px, -1px, 0px);
}

#FastWarDeckCardCloseButton:active
{
	brightness: 2;
	opacity: 1;
	transform: translate3d(2px, 2px, 0px);
}

#FastWarDeckCardContainer
{
	width: 1500px;
	height: 340px;
	align: right top;
	margin-top: 10px;
	margin-right: 10px;
	margin-left: 10px;
}

.ShowDeck.SelfDeck #FastWarDeckCardContainer
{
	position: 369.6px 0px 0px;
}

.ShowReserve.SelfDeck #FastWarDeckCardContainer
{
	position: 0px 0px 0px;
}

#FastWarDeckCardNumContainer
{
	margin-left: 20px;
	margin-right: 20px;
	height: 60px;
	z-index: 9;
	flow-children: right;
	opacity: 0;
	transition-property: transform, opacity;
	transition-duration: 0.5s;
	transition-timing-function: ease-in-out;
}

.ShowReserve #FastWarDeckCardNumContainer
{
	opacity: 1;
}

.FastWarDeckCardNumReady .FastWarDeckCardNumLabel
{
	color: #8aff63;
	brightness: 1.5;
}

#FastWarDeckCardNumMain
{
	height: 60px;
	flow-children: right;
}

.FastWarDeckCardNum
{
	font-size: 47px;
	height: 50px;
}

.FastWarDeckCardNumText
{
	font-size: 25px;
	height: 30px;
}

.FastWarDeckCardNumLabel
{
	vertical-align: bottom;
	horizontal-align: left;
	text-align: center;
	font-weight: bold;
	margin-left: 2px;
	margin-right: 2px;
	text-shadow: 0px 0px 3px 5 #00000073;
	color: #ff6363;
	brightness: 1;
	transition-property: color;
	transition-duration: 0.1s;
	transition-timing-function: linear;
}

.WarnPanel
{
	animation-name: WarnPanelAni;
	animation-duration: 0.2s;
	animation-timing-function: ease-in-out;
	opacity: 1;
}

@keyframes 'WarnPanelAni'
{
	0%
	{
		brightness: 1;
		transform: scale3d(1, 1, 1) translate3d(0px, 0px, 0px);
	}
	
	50%
	{
		brightness: 1.5;
		wash-color: #ff896c;
		transform: scale3d(1.1, 1.1, 1) translate3d(0px, -5px, 0px);
	}
	
	100%
	{
		brightness: 1;
		transform: scale3d(1, 1, 1) translate3d(0px, 0px, 0px);
	}
	
}

#RandomDeckCards
{
	vertical-align: center;
	width: 40px;
	height: 44px;
	margin-left: 20px;
	margin-right: 20px;
	z-index: 9;
	background-image: url("s2r://panorama/images/custom_game/hud/random_png.vtex");
	background-size: 100% 100%;
	opacity: 0;
}

#RandomDeckCards:hover
{
	brightness: 1;
	transform: translate3d(-1px, -1px, 0px);
}

#RandomDeckCards:active
{
	brightness: 2;
	transform: translate3d(2px, 2px, 0px);
}

.ShowReserve #RandomDeckCards
{
	opacity: 1;
}

#FastWarDeckCardAvgCostContainer
{
	margin-left: 20px;
	margin-right: 20px;
	height: 50px;
	width: 190px;
	z-index: 9;
	flow-children: right;
}

#FastWarDeckCardAvgCost
{
	font-size: 47px;
	height: 50px;
}

.ChangeDeckCardAvgCost
{
	animation-name: ChangeDeckCardAvgCostAni;
	animation-duration: 0.2s;
	animation-timing-function: ease-in-out;
}

@keyframes 'ChangeDeckCardAvgCostAni'
{
	0%
	{
		brightness: 1;
		transform: scale3d(1, 1, 1) translate3d(0px, 0px, 0px);
	}
	
	50%
	{
		brightness: 1.5;
		transform: scale3d(1.1, 1.1, 1) translate3d(0px, -5px, 0px);
	}
	
	100%
	{
		brightness: 1;
		transform: scale3d(1, 1, 1) translate3d(0px, 0px, 0px);
	}
	
}

.FastWarDeckCardAvgCostText
{
	vertical-align: bottom;
	horizontal-align: left;
	text-align: center;
	font-weight: bold;
	text-shadow: 0px 0px 3px 5 #00000073;
	color: #ffde70;
	brightness: 1;
	font-size: 25px;
	height: 30px;
}

#FastWarReserveCardContainer
{
	width: 1550px;
	height: 700px;
	align: right bottom;
	margin-bottom: 10px;
}

.FastWarDeckCardTips
{
	height: 50px;
	align: center top;
	border-radius: 25px;
	z-index: 2;
	flow-children: right;
	opacity: 1;
	background-color: #00000000;
	transform: translate3d(580px, 0px, 0px);
	transition-property: transform, background-color;
	transition-duration: 0.5s;
	transition-timing-function: ease-in-out;
}

.FastWarDeckCardTips.HideEdit
{
	opacity: 0;
}

.ShowReserve .FastWarDeckCardTips
{
	background-color: gradient(linear, 0% 100%, 100% 0%, from(rgba(152, 239, 137, 0.082)), to(rgba(73, 89, 135, 0.285)));
	transform: translate3d(0px, 0px, 0px);
}

#ReserveCardFilterContainer
{
	align: left bottom;
	height: 45px;
	z-index: 2;
	flow-children: right;
}

DropDownMenu
{
	width: 150px;
	border-radius: 10px;
}

DropDown Label
{
	y: 3px;
	margin-left: 18px;
}

DropDownMenu Label
{
	background-color: rgba(66, 59, 97, 0.434);
	opacity: 0.4;
}

DropDown:enabled:hover:not(:parentdisabled),DOTASettingsEnumDropDown:enabled:hover
{
	background-color: rgba(0, 0, 0, 0);
	brightness: 1;
}

.ReserveCardFilterListButtonText
{
	font-size: 23px;
}

ToggleButton.ReserveCardListFilter .TickBox,CycleButton .TickBox,DOTASettingsCheckbox .TickBox
{
	width: 75px;
}

ToggleButton .TickBox,CycleButton .TickBox,DOTASettingsCheckbox .TickBox
{
	width: 48px;
	height: 32px;
	border-radius: 5px;
}

ToggleButton:selected .TickBox,CycleButton:selected .TickBox,DOTASettingsCheckbox:selected .TickBox
{
	background-color: gradient(linear, 0% 0%, 0% 100%, from(#b0a4299e), to(#b77e1c9e));
	box-shadow: #ffffff00 0px 0px 8px 0px;
}

ToggleButton,CycleButton,DOTASettingsCheckbox
{
	vertical-align: center;
	flow-children: none;
}

ToggleButton:selected Label,ToggleButton:selected Label,DOTASettingsCheckbox:selected Label
{
	color: #ffffff;
}

ToggleButton:enabled:hover Label,ToggleButton:enabled:hover Label,DOTASettingsCheckbox:enabled:hover Label
{
	color: #ffffff;
}

ToggleButton Label,CycleButton Label,DOTASettingsCheckbox Label
{
	padding: 1px;
	height: 26px;
	margin-left: 5px;
	align: center center;
	font-size: 24px;
	color: rgba(185, 197, 140, 0.679);
}

#ReserveCardListFilterRefresh
{
	margin-left: 30px;
	margin-right: 30px;
	vertical-align: center;
	height: 32px;
	width: 32px;
	background-image: url("s2r://panorama/images/custom_game/hud/refresh_psd_png.vtex");
	background-size: 100% 100%;
}

#ReserveCardListFilterRefresh:hover
{
	brightness: 1;
	transform: translate3d(0px, 0px, 0px) scale3d(1.05, 1.05, 1);
}

#ReserveCardListFilterRefresh:active
{
	brightness: 1;
	transform: translate3d(1px, 1px, 0px) scale3d(1, 1, 1);
}

.ReserveCardListFilterText
{
	margin-left: 20px;
	font-size: 25px;
	height: 27px;
	vertical-align: center;
	color: #e9ff98;
}

.FastWarDeckCardTitleText
{
	vertical-align: center;
	horizontal-align: left;
	text-align: center;
	font-weight: bold;
	text-shadow: 0px 0px 3px 5 #00000073;
	brightness: 1;
	font-size: 40px;
	height: 42px;
}

#PlayerHeroNameText
{
	horizontal-align: center;
	width: 200px;
	text-overflow: shrink;
	margin-left: 0px;
	margin-right: 0px;
	color: #feffbd;
}

#DeckCardTitleText
{
	margin-left: 30px;
	margin-right: 10px;
	color: #66c28f;
	opacity: 0;
	transition-property: transform, opacity;
	transition-duration: 0.5s;
	transition-timing-function: ease-in-out;
}

.ShowReserve #DeckCardTitleText
{
	opacity: 1;
}

#ReserveCardTitleText
{
	margin-left: 30px;
	margin-right: 10px;
	color: #af7eeb;
}

.FastWarDeckCardsMainBack
{
	width: 100%;
	align: center center;
	margin-top: 22px;
	border-radius: 30px;
	background-color: rgba(0, 0, 0, 0.419);
	background-size: 100% 100%;
	z-index: 1;
}

#FastWarDeckMainContainer .FastWarDeckCardsMainBack
{
	height: 310px;
}

#FastWarReserveCardContainer .FastWarDeckCardsMainBack
{
	height: 660px;
}

.FastWarDeckCardsMain
{
	width: 100%;
	align: center bottom;
	background-size: 100% 100%;
	overflow: noclip;
}

.FastWarDeckCardsMain.Enough
{
	border-left: 3px solid #8bff95;
	border-right: 3px solid #8bff95;
}

.FastWarDeckCardsMain.NotEnough
{
	border-left: 3px solid #ff0000;
	border-right: 3px solid #ff0000;
}

#FastWarDeckCardContainer .FastWarDeckCardsMain
{
	border-radius: 30px;
	margin-top: 20px;
	height: 290px;
}

#FastWarReserveCardContainer .FastWarDeckCardsMain
{
	border-radius: 0px;
	flow-children: right-wrap;
	overflow: squish scroll;
	height: 620px;
}

.FastWarDeckCardsMain VerticalScrollBar,HTMLVerticalScrollBar
{
	background-color: #341f002e;
}

.FastWarDeckCardsMain VerticalScrollBar .ScrollThumb,HTMLVerticalScrollBar .ScrollThumb
{
	border: 2px solid rgba(154, 154, 154, 0.271);
	background-color: #462300a2;
	border-radius: 4px;
}

.FastWarDeckCard
{
	overflow: noclip;
	width: 200px;
	height: 300px;
	border-radius: 10px;
	box-shadow: #0a0a0a94 1px 1px 0px 0px;
	visibility: visible;
}

.FastWarDeckCard.NotEmpty:hover
{
	border: 1px solid #ffedc3df;
}

.FastWarDeckCard.Reserve
{
	margin-left: 13px;
	margin-right: 7px;
	margin-bottom: 20px;
	transform: scale3d(1, 1, 1);
}

.FastWarDeckCard.Deck
{
	margin-left: 0px;
	margin-right: 0px;
	margin-bottom: 0px;
	transform: scale3d(0.9, 0.9, 1);
}

.HideCard
{
	visibility: collapse;
}

.RemoveDeckCard
{
	animation-name: RemoveDeckCardAni;
	animation-duration: 0.1s;
	animation-timing-function: ease-in-out;
}

@keyframes 'RemoveDeckCardAni'
{
	0%
	{
		opacity: 1;
		transform: translateX(0) translateY(0px);
	}
	
	100%
	{
		opacity: 0;
		transform: translateX(10px) translateY(50px);
	}
	
}

.AddDeckCard
{
	animation-name: AddDeckCardAni;
	animation-duration: 0.2s;
	animation-timing-function: ease-in-out;
}

@keyframes 'AddDeckCardAni'
{
	0%
	{
		opacity: 0;
		transform: scale3d(1, 1, 1) translateX(10px) translateY(50px);
	}
	
	50%
	{
		opacity: 1;
		transform: scale3d(1.1, 1.1, 1) translateX(0) translateY(0px);
	}
	
	100%
	{
		opacity: 1;
		transform: scale3d(0.9, 0.9, 1) translateX(0) translateY(0px);
	}
	
}

.FastWarDeckCardEmpty
{
	width: 190px;
	height: 300px;
	align: center center;
	background-image: url("s2r://panorama/images/custom_game/card/bg/card_mini_bg_black_psd_png.vtex");
	background-size: 100% 100%;
	z-index: 1;
}

.FastWarDeckCardEmptyImage
{
	align: center center;
	width: 150px;
	height: 150px;
	background-size: 100% 100%;
	margin-bottom: 30px;
	background-image: url("s2r://panorama/images/custom_game/hud/dota_generic_psd_png.vtex");
}

.FastWarDeckCardEmptyImage.HideEdit
{
	background-image: url("s2r://panorama/images/custom_game/hud/question_mark_icon_png_png.vtex");
}

#FastWarPhaseRoot
{
	width: 100%;
	height: 100%;
	z-index: 4;
}

#Test
{
	width: 100px;
	height: 50px;
}

#FastWarGameTimeContainer
{
	width: 200px;
	height: 160px;
	align: right top ;
	margin-top: 20px;
}

#FastWarPlayerTowerStatusContainer
{
	width: 100%;
	height: 100%;
}

.FastWarPlayerTowerStatus
{
	width: 480px;
	height: 100px;
	margin-left: 20px;
	opacity: 0;
	transition-property: opacity,transform;
	transition-duration: 0.1s;
}

#FastWarPlayerTowerStatusSelf
{
	margin-bottom: 40px;
	align: left bottom ;
}

#FastWarPlayerTowerStatusEnemy
{
	margin-top: 30px;
	margin-right: 200px;
	align: right top ;
}

#FastWarPlayerTower
{
	margin-top: 5px;
	width: 100%;
	height: 100%;
}

#FastWarPlayerTowerStatusSelf #FastWarPlayerTower
{
	flow-children: right;
}

#FastWarPlayerTowerStatusEnemy #FastWarPlayerTower
{
	flow-children: left;
}

#FastWarPlayerInfo
{
	width: 100px;
	height: 100px;
	flow-children: down;
}

#FastWarPlayerInfo
{
	width: 90px;
	height: 90px;
	align: center center;
	background-size: 100% 100%;
	background-color: #222222;
	background-image: url("s2r://panorama/images/custom_game/hud/106863163_png_png.vtex");
	border-radius: 10px;
}

#FastWarPlayerName
{
	align: center top;
	height: 30px;
	text-align: left;
	overflow: clip;
	text-overflow: ellipsis;
	color: #fff3c5;
	font-size: 20px;
	font-weight: bold;
	text-shadow: 0px 0px 5px 5 #000000;
	brightness: 1;
}

#FastWarTowerContainer
{
	width: 380px;
	height: 100px;
}

#FastWarTower1
{
	align: left bottom;
}

#FastWarPlayerTowerStatusEnemy #FastWarTower1
{
	align: right bottom;
}

#FastWarTower2
{
	align: right bottom;
}

#FastWarPlayerTowerStatusEnemy #FastWarTower2
{
	align: left bottom;
}

#FastWarTower3
{
	width: 250px;
	align: center top;
}

.FastWarTower
{
	width: 190px;
	height: 55px;
}

#FastWarTowerImg
{
	margin-left: 5px;
	width: 45px;
	height: 45px;
	align: left center;
}

.RadiantTower #TowerImg
{
	background-image: url("s2r://panorama/images/custom_game/hud/radiant_png.vtex");
}

.DireTower #TowerImg
{
	background-image: url("s2r://panorama/images/custom_game/hud/dire_png.vtex");
}

.RadiantTower #FontImg
{
	background-image: url("s2r://panorama/images/custom_game/hud/1000_psd_png.vtex");
}

.DireTower #FontImg
{
	background-image: url("s2r://panorama/images/custom_game/hud/1001_psd_png.vtex");
}

.TowerImg
{
	width: 100%;
	height: 100%;
	align: center center;
	background-size: 100% 100%;
	z-index: 1;
}

#TowerImgBack
{
	width: 100%;
	height: 100%;
	align: center center;
	background-size: 100% 100%;
	background-image: url("s2r://panorama/images/custom_game/hud/guild_tier1_icon_frame_psd_png.vtex");
	z-index: 3;
}

#TowerDeath
{
	width: 100%;
	height: 100%;
	opacity: 0;
	align: center center;
	background-size: 100% 100%;
	background-image: url("s2r://panorama/images/custom_game/hud/icon_x_2_png.vtex");
	z-index: 2;
}

#TowerDeath.ShowIconX
{
	opacity: 1;
}

#FastWarTowerHealth
{
	align: right center;
	height: 28px;
	width: 130px;
	border-radius: 10px;
	background-color: gradient(linear, 100% 0%, 0% 0%, from(rgba(22, 22, 22, 0.512)), to(rgba(0, 0, 0, 0.26)));
}

#FastWarTower3 #FastWarTowerHealth
{
	width: 190px;
}

#HealthText
{
	z-index: 2;
	align: center center;
	flow-children: right;
}

.HealthNum
{
	font-size: 20px;
	font-weight: bold;
	color: #ffffff;
	z-index: 1;
	font-family: Stratum2 TF, "Arial Unicode MS";
	text-shadow: 0px 0px 5px 5 #36302119;
}

.JumpNumber
{
	transform-origin: 50% -50%;
	animation-name: jitterNumber;
	animation-duration: 0.15s;
	animation-timing-function: ease-out;
	animation-iteration-count: 1;
}

@keyframes 'jitterNumber'
{
	0%
	{
		transform: scale3D(1.15, 1.15, 1) translate3d(-3px, -4px, 0px);
		brightness: 3;
	}
	
	25%
	{
		transform: scale3D(1, 1, 1) translate3d(0px, -2px, 0px);
		brightness: 5;
	}
	
	50%
	{
		transform: scale3D(1, 1, 1) translate3d(3px, 5px, 0px);
		brightness: 3;
	}
	
	100%
	{
		transform: scale3D(1, 1, 1) translate3d(0px, 0px, 0px);
		brightness: 1;
	}
	
}

.ProgressBarLeft
{
	background-color: gradient(linear, 100% 0%, 0% 0%, from(#6dd775), color-stop(0.5, #23a72e), to(#05713d));
	height: 100%;
	z-index: 2;
	border-radius: 5px;
	transition-property: width;
	transition-duration: 1s;
	transition-timing-function: ease-in-out;
}

.ProgressBarLeftPar
{
	height: 100%;
	width: 100%;
	border-radius: 5px;
	z-index: 3;
}

.BarBack
{
	width: 100%;
	height: 40px;
	background-color: gradient(linear, 100% 0%, 0% 0%, from(rgba(0, 0, 0, 0)), color-stop(0.3, #a93b3b), to(rgba(255, 0, 0, 0.821)));
	z-index: 1;
	transition-delay: 0.8s;
	transition-property: width;
	transition-duration: 1s;
	transition-timing-function: ease-in-out;
}

#HealthBar
{
	width: 100%;
	height: 40px;
	brightness: 1;
	border-radius: 10px;
	border: 4px solid #000000;
	horizontal-align: left;
	vertical-align: center;
	z-index: 1;
}

#FastWarManaSpeed
{
	align: center top ;
	height: 50px;
	margin-top: 100px;
	margin-right: 10px;
	flow-children: right;
	opacity: 0;
	transition-property: opacity;
	transition-duration: 0.5s;
}

#FastWarManaSpeed.Show
{
	opacity: 1;
}

#FastWarManaSpeedImg
{
	width: 37px;
	height: 50px;
	margin-left: 3px;
	wash-color: #ffffff;
	background-image: url("s2r://panorama/images/custom_game/hud/quick_icon_psd_png.vtex");
	background-size: 100% 100%;
	z-index: 5;
}

#FastWarManaSpeedText
{
	vertical-align: center;
	height: 40px;
	font-size: 38px;
	text-align: center;
	font-weight: normal;
	color: #fff09a;
	text-shadow: 0px 0px 3px 5 #00000073;
}

#FastWarGameTime
{
	width: 150px;
	height: 100px;
	align: center top ;
	margin-top: 10px;
	background-color: rgba(68, 68, 68, 0.664);
	border: 3px solid #00000071;
	border-radius: 15px;
	flow-children: down;
}

.FastWarGameTimeText
{
	width: 150px;
	height: 22px;
	font-size: 20px;
	horizontal-align: center;
	text-align: center;
	font-weight: normal;
	color: white;
	text-shadow: 0px 0px 3px 5 #00000073;
	brightness: 1;
}

#FastWarGameTimeText1
{
	margin-top: 5px;
}

#FastWarGameTimeText2
{
	font-size: 60px;
	width: 150px;
	height: 60px;
}

#FastWarHandContainer
{
	width: 100%;
	height: 800px;
	perspective: 1200px;
	horizontal-align: center;
	vertical-align: bottom;
	margin-bottom: 60px;
	z-index: 1;
}

.FastWarHandCard
{
	horizontal-align: center;
	vertical-align: bottom;
	overflow: noclip;
	width: 200px;
	height: 300px;
	transition-property: position, transform, opacity;
	transition-duration: 0.2s;
	transition-timing-function: linear;
	transform: translateX(0px) translateY(120px) rotateZ(0deg);
	box-shadow: #25252580 1px 1px 1px 1px;
	z-index: 3;
}

.FastWarHandCard.Dragging
{
	transition-duration: 0.2s;
	transition-timing-function: linear;
	z-index: 10;
	box-shadow: 0 3px 20px #efe93760;
}

.FastWarHandCard.HandCardChoose:not(.ExitAnimation)
{
	transition-duration: 0.2s;
	border: 2px solid #ffffff71;
	brightness: 1;
	transform: translateY(0px);
}

@keyframes 'CardShake'
{
	0%
	{
		transform: rotateY(3deg) rotateX(0deg);
	}
	
	25%
	{
		transform: rotateY(0deg) rotateX(-2deg);
	}
	
	75%
	{
		transform: rotateY(-3deg) rotateX(3deg);
	}
	
	100%
	{
		transform: rotateY(0deg) rotateX(0deg);
	}
	
}

.ExitAnimation
{
	opacity: 0;
}

.EnterAnimation
{
	opacity: 0;
	transition-duration: 0.2s;
	z-index: 1;
}

.EnterAnimation:not(.ExitAnimation)
{
	opacity: 1;
	animation-name: CardDraw;
	animation-duration: 0.3s;
	animation-timing-function: ease-in-out;
}

@keyframes 'CardDraw'
{
	0%
	{
		opacity: 0;
		transform: scale3d(0.4, 0.4, 1) translateX(1000px) translateY(120px);
	}
	
	100%
	{
		opacity: 1;
		transform: scale3d(1, 1, 1) translateX(0) translateY(120px);
	}
	
}

#FastWarHandCardMain
{
	width: 200px;
	height: 300px;
	align: center center;
	border-radius: 5px;
	brightness: 1;
	overflow: noclip;
}

#FastWarHandCardShowLevel1
{
	width: 100%;
	height: 100%;
	z-index: 3;
}

#FastWarHandCardShowLevel2
{
	width: 100%;
	height: 100%;
	z-index: 2;
}

#FastWarHandCardShowLevel3
{
	width: 100%;
	height: 100%;
	z-index: 1;
}

#FastWarHandCardBg
{
	width: 190px;
	height: 300px;
	align: center center;
	wash-color: #ffffff;
	background-size: 100% 100%;
	z-index: 1;
	brightness: 2;
}

#FastWarHandCardBg.UnitCard
{
	background-image: url("s2r://panorama/images/custom_game/card/bg/card_mini_bg_black_psd_png.vtex");
}

#FastWarHandCardBg.SpellCard
{
	background-image: url("s2r://panorama/images/custom_game/card/bg/spell_card_bg_png.vtex");
}

#FastWarHandCardMain.Usable
{
	brightness: 1;
	animation-name: CardUsable;
	animation-duration: 0.4s;
	animation-iteration-count: 1;
	animation-timing-function: linear;
}

@keyframes 'CardUsable'
{
	0%
	{
		brightness: 1;
		transform: scale3d(1, 1, 1) translate3d(0px, 0px, 0px);
	}
	
	50%
	{
		brightness: 2;
		transform: scale3d(1.1, 1.1, 1) translate3d(0px, -10px, 0px);
	}
	
	100%
	{
		brightness: 1;
		transform: scale3d(1, 1, 1) translate3d(0px, 0px, 0px);
	}
	
}

#FastWarHandCardMain.Unusable
{
	brightness: 0.5;
	wash-color: #7d7d7d;
}

#FastWarHandCardCooldownClip
{
	width: 180px;
	height: 200px;
	margin-top: 5px;
	align: center top;
	clip: radial(50% 50%, 0deg, 0deg);
	background-color: rgba(145, 145, 145, 0.403);
	z-index: 3;
	transform: rotateY(180deg);
	transition-property: clip, background-color;
	transition-duration: 0.1s;
	transition-timing-function: linear;
}

#FastWarHandCardDisablePar
{
	width: 178px;
	height: 190px;
	align: center center;
	margin-bottom: 50px;
	z-index: 4;
	background-image: url("s2r://panorama/images/custom_game/hud/portrait_silenced_png_png.vtex");
	background-size: 100% 100%;
	background-color: rgba(0, 0, 0, 0.747);
	opacity: 0;
	transition-property: opacity;
	transition-duration: 0.1s;
	transition-timing-function: linear;
}

#FastWarHandCardDisablePar.ShowDisable
{
	opacity: 1;
}

#FastWarHandCardCooldownClipCoolDown
{
	width: 100%;
	height: 57px;
	text-align: center;
	color: #b1b1b1;
	font-size: 55px;
	font-weight: bold;
	text-shadow: 0px 0px 5px 5 #000000;
	margin-top: 70px;
	align: center top;
	z-index: 6;
	opacity: 0.8;
}

#FastWarHandCardTopLabelContain
{
	width: 180px;
	height: 27px;
	margin-top: 5px;
	align: center top;
	background-color: #000000;
	z-index: 4;
}

#FastWarHandCardCostBack
{
	width: 37px;
	height: 54px;
	margin-left: 3px;
	wash-color: #ffffff;
	background-image: url("s2r://panorama/images/custom_game/hud/hex_icon_rarity_shadow_tier_5_large_selected_psd_png.vtex");
	background-size: 100% 100%;
	z-index: 5;
}

#FastWarHandCardType
{
	width: 40px;
	height: 40px;
	align: center bottom;
	margin-bottom: 65px;
	border-radius: 50%;
	wash-color: #ffffff;
	background-image: url("s2r://");
	background-size: 100% 100%;
	z-index: 5;
}

#FastWarHandCardCostLabel
{
	width: 32px;
	height: 42px;
	overflow: noclip;
	text-align: center;
	text-overflow: shrink;
	color: #ffffff;
	font-size: 40px;
	margin-left: 5px;
	margin-top: 6px;
	font-weight: bold;
	text-shadow: 0px 0px 5px 5 #000000;
	horizontal-align: left;
	z-index: 6;
}

#FastWarHandCardCostLabel.MoreCost
{
	color: #ff3333;
}

#FastWarHandCardCostLabel.LessCost
{
	color: #48ff61;
}

#FastWarHandCardSubUnitContain
{
	align: right bottom;
	height: 100%;
	width: 70px;
	z-index: 3;
	flow-children: up;
	margin-bottom: 75px;
	overflow: noclip;
}

.FastWarHandCardSubUnit
{
	horizontal-align: center;
	margin-top: 2px;
	margin-bottom: 2px;
	overflow: noclip;
	border: 1px solid #939393;
	width: 60px;
	height: 31px;
	transform: rotateZ(20deg);
	opacity: 1;
	background-image: url("s2r://panorama/images/heroes/npc_dota_beastmaster_boar_png.vtex");
	background-size: 100% 100%;
}

.FastWarHandCardSubUnitText
{
	align: center bottom;
	width: 56px;
	height: 20px;
	text-align: right;
	font-size: 20px;
	color: rgba(213, 166, 95, 0.875);
	text-shadow: 0px 0px 5px 3 #000000;
}

#FastWarHandCardNameLabel
{
	height: 22px;
	width: 140px;
	overflow: noclip;
	text-overflow: shrink;
	color: #ffffff;
	font-size: 20px;
	margin-left: 10px;
	text-shadow: 0px 0px 5px 3 #000000;
	text-align: center;
	horizontal-align: center;
	vertical-align: center;
}

#FastWarHandCardBottomLabelContain
{
	width: 180px;
	height: 80px;
	margin-bottom: 5px;
	border-radius: 10px;
	align: center bottom;
	z-index: 2;
}

#HandCardBottomTextMain
{
	width: 180px;
	height: 70px;
	align: center center;
	margin-bottom: 10px;
	background-image: url("s2r://panorama/images/custom_game/card/bg/card_tier_2_png_png.vtex");
	background-size: 100% 100%;
	background-repeat: no-repeat;
	background-position: 50% 50%;
}

#HandCardBottomTextMainBack
{
	width: 180px;
	height: 70px;
	align: center top;
	background-image: url("s2r://panorama/images/custom_game/hud/battlepass_levelup_popup_background_psd_png.vtex");
	background-size: 100% 100%;
	opacity-mask: url("s2r://panorama/images/custom_game/hud/treasure_image_mask_psd_png.vtex");
	background-img-opacity: 0.5;
	z-index: 1;
}

#HandCardBottomDescLabel
{
	width: 140px;
	color: #ffefd2;
	font-size: 17px;
	text-align: center;
	text-shadow: 0px 0px 5px 3 #000000;
	horizontal-align: center;
	vertical-align: center;
	z-index: 2;
}

#HandCardBottomSpellContainer
{
	align: center center;
	flow-children: right-wrap;
	z-index: 9;
}

#FastWarHandCardBack
{
	width: 180px;
	height: 290px;
	margin-top: 3px;
	align: center center;
	z-index: 1;
	opacity-mask: url("s2r://panorama/images/custom_game/card/bg/mask_png.vtex");
	opacity: 1;
}

#FastWarHandCardSpellBack
{
	width: 200px;
	height: 200px;
	align: center center;
	margin-bottom: 50px;
	background-image: url("s2r://panorama/images/custom_game/card/full_art/10782_psd_png.vtex");
	background-size: 200px 200px;
	background-position: 50% 50%;
	background-repeat: no-repeat;
	z-index: 2;
	opacity: 0;
}

.SpellCard #FastWarHandCardSpellBack
{
	opacity: 1;
}

#FastWarHandCardSpellBack2
{
	z-index: 1;
	width: 200px;
	height: 200px;
	align: center center;
	margin-bottom: 50px;
	background-image: url("s2r://panorama/images/custom_game/card/bg/recipe_png_png.vtex");
	background-size: 200px 200px;
	background-position: 50% 50%;
	background-repeat: no-repeat;
	opacity: 0;
}

.SpellCard #FastWarHandCardSpellBack2
{
	opacity: 1;
}

#FastWarHandCardUnitBack
{
	width: 100%;
	height: 100%;
	opacity: 0;
}

.UnitCard #FastWarHandCardUnitBack
{
	opacity: 1;
}

.TestScenePanel
{
	width: 100%;
	height: 100%;
}

#FastWarInGameEnergyPointContainer
{
	height: 120px;
	width: 900px;
	margin-left: 600px;
	horizontal-align: center;
	vertical-align: bottom;
	z-index: 3;
}

#SuperWaterBackground
{
	width: 800px;
	height: 60px;
	align: center bottom;
	border-radius: 8px;
	border: 2px solid #b7b9ff85;
	background-color: #303030;
	background-image: url("s2r://panorama/images/custom_game/hud/mana_bar_back_png.vtex");
	background-size: 100% 100%;
	z-index: 1;
}

#ManaNumContainer
{
	width: 200px;
	height: 90px;
	margin-left: 25px;
	align: left bottom;
	z-index: 2;
}

.ManaNumText
{
	text-align: center;
	font-weight: normal;
	color: #fff674;
	text-shadow: 0px 0px 3px 5 #00000073;
	brightness: 1;
}

#CurrentManaNumContainer
{
	width: 140px;
	height: 65px;
	flow-children: right;
}

#CurrentManaIcon
{
	height: 46px;
	width: 32px;
	margin-left: 30px;
	vertical-align: center;
	wash-color: #fffcd0;
	background-image: url("s2r://panorama/images/custom_game/hud/quick_icon_psd_png.vtex");
	background-size: 100% 100%;
}

@keyframes 'CurrentManaIconShake'
{
	0%
	{
		transform: translate3d(0px, 0px, 0px);
	}
	
	25%
	{
		transform: translate3d(1px, -1px, 0px);
	}
	
	50%
	{
		transform: translate3d(-1px, 1px, 0px);
	}
	
	75%
	{
		transform: translate3d(1px, 2px, 0px);
	}
	
	100%
	{
		transform: translate3d(0px, 0px, 0px);
	}
	
}

#CurrentManaNumText
{
	font-size: 60px;
	z-index: 3;
}

.AddMana
{
	animation-name: CurrentManaNumUpShake;
	animation-duration: 0.2s;
	animation-timing-function: linear;
}

@keyframes 'CurrentManaNumUpShake'
{
	0%
	{
		brightness: 1;
		transform: scale3d(1, 1, 1) translate3d(0px, 0px, 0px);
	}
	
	50%
	{
		brightness: 2;
		transform: scale3d(1.1, 1.1, 1) translate3d(0px, -5px, 0px);
	}
	
	100%
	{
		brightness: 1;
		transform: scale3d(1, 1, 1) translate3d(0px, 0px, 0px);
	}
	
}

.SubMana
{
	animation-name: CurrentManaNumDownShake;
	animation-duration: 0.2s;
	animation-timing-function: linear;
}

@keyframes 'CurrentManaNumDownShake'
{
	0%
	{
		brightness: 1;
		transform: scale3d(1, 1, 1) translate3d(0px, 0px, 0px);
	}
	
	50%
	{
		brightness: 2;
		transform: scale3d(1.1, 1.1, 1) translate3d(-5px, 2px, 0px);
	}
	
	100%
	{
		brightness: 1;
		transform: scale3d(1, 1, 1) translate3d(0px, 0px, 0px);
	}
	
}

#MaxManaNumText
{
	width: 120px;
	height: 20px;
	font-size: 15px;
	align: left bottom;
	margin-bottom: 3px;
	margin-left: 2px;
}

#ManaContainer
{
	flow-children: right;
	horizontal-align: center;
	width: 800px;
	height: 100%;
}

.ManaNodeContainer
{
	width: 78px;
	height: 60px;
	margin-right: 2px;
	transition: transform 0.2s ease-in-out 0s;
}

.AlmostActiveMana.ManaNodeContainer
{
	wash-color: #dfd184;
}

.ManaNodeBackground
{
	height: 100%;
	background-size: 100% 100%;
	z-index: 3;
	opacity: 0;
	border-radius: 12px;
	background-image: url("s2r://panorama/images/custom_game/hud/mana_node_png.vtex");
	wash-color: #7b7b7b;
	transition-property: width;
	transition-duration: 0.1s;
	transition-timing-function: linear;
}

.AlmostActiveMana .ManaNodeBackground
{
	opacity: 1;
}

.ManaNodeFill
{
	z-index: 2;
	border-radius: 10px;
	width: 100%;
	height: 0%;
	transition: height 0.3s ease-in-out 0s;
	background-color: gradient(linear, 0% 0%, 0% 100%, from(#4f63ab8e), to(#0066ff61));
	brightness: 0.5;
	opacity: 0.9;
}

.ActiveMana .ManaNodeFill
{
	height: 100%;
	brightness: 1;
	opacity: 0.3;
}

.ManaNodeGlow
{
	z-index: 2;
	border-radius: 10px;
	width: 100%;
	height: 100%;
	border-radius: 12px;
	background-image: url("s2r://panorama/images/custom_game/hud/mana_node_png.vtex");
	background-size: 100% 100%;
	opacity: 0;
	transition: opacity 0.2s ease-in-out 0s;
}

.ActiveMana .ManaNodeGlow
{
	opacity: 0.8;
}

.PreviewMana
{
	width: 0%;
	height: 100%;
	border-radius: 6px;
	border: 5px solid #cfffbe;
}

.ManaCost
{
	color: white;
	font-size: 20px;
	font-weight: bold;
	text-shadow: 0px 0px 5px 3 #000000;
	horizontal-align: right;
	vertical-align: top;
	margin: 8px;
}

#TestText
{
	color: white;
	font-size: 50px;
	font-weight: bold;
	text-shadow: 0px 0px 5px 3 #000000;
}

.root
{
	width: 100%;
	height: 100%;
}

.image-precache
{
	background-image: url("s2r://panorama/images/custom_game/back/background_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/back/mode1_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/back/mode2_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/back/mode3_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/back/mode4_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/back/mode5_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/back/s2_background_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/bounty_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/call_to_arms_cardpack_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/card_mini_bg_black_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/card_tier_1_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/card_tier_2_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/card_tier_3_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/card_tier_4_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/card_tier_5_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/card_tier_6_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/cardtype_armor_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/cardtype_building_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/cardtype_creep_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/cardtype_hero_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/cardtype_spell_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/cardtype_spell2_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/cardtype_weapon_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/cleave_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/death_shield_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/disarmed_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/icon_perfectgauntlet_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/immunity_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/mask_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/mask2_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/mask3_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/piercing_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/recipe_opacity_mask_nopurchase_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/recipe_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/regen_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/retaliate_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/set_01_rare_small_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/set_01_uncommon_small_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/siege_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/bg/spell_card_bg_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/desc/action_lock_green_cycle_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/desc/action_lock_green_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/desc/action_lock_red_cycle_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/desc/action_lock_red_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/desc/action_lockcreep_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/desc/action_lockenemycreep_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/desc/action_lockenemyhero_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/desc/action_lockenemytower_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/desc/action_lockhero_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/desc/action_lockrune_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/desc/action_locktower_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/extra_icon/10024_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/full_art/10007_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/full_art/10063_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/full_art/10141_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/full_art/10142_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/full_art/10148_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/full_art/10184_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/full_art/10226_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/full_art/10277_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/full_art/10315_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/full_art/10323_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/full_art/10335_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/full_art/10412_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/full_art/10616_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/full_art/10657_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/full_art/10703_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/full_art/10708_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/full_art/10782_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/full_art/10785_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/full_art/1_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/10014_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/10029_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/10054_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/10055_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/10056_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/10077_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/10079_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/10096_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (1)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (10)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (100)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (101)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (102)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (103)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (104)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (105)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (106)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (107)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (108)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (109)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (11)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (110)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (111)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (112)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (113)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (114)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (115)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (117)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (118)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (119)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (12)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (13)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (14)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (15)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (16)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (17)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (18)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (19)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (2)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (20)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (21)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (22)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (23)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (24)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (25)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (26)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (27)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (28)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (29)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (3)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (30)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (31)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (32)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (33)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (34)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (35)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (36)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (37)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (38)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (4)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (40)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (41)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (42)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (44)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (45)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (46)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (47)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (48)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (49)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (5)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (50)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (51)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (52)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (53)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (54)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (55)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (56)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (57)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (58)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (59)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (6)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (60)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (62)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (63)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (64)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (65)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (66)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (67)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (68)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (69)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (7)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (70)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (71)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (72)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (73)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (74)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (75)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (77)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (78)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (79)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (8)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (80)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (81)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (82)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (83)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (85)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (86)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (87)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (88)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (89)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (90)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (92)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (93)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (94)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (95)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (96)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (97)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (98)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/card/tip_icon/tip (99)_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/1000_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/1001_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/18001_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/a_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/ability_bevel_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/ability_death_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/avatarframe_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/back_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/battlepass_levelup_popup_background_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/battlepass_logo_grayscale_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/colosseum_popup_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/dark_moon_popup_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/death_icon_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/debut_bg_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/debut_brush_bg_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/defeat_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/dire_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/dota_generic_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/glossary_basics_heroes_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/guild_tier0_icon_frame_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/guild_tier1_icon_frame_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/guild_tier2_icon_frame_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/guild_tier3_icon_frame_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/hex_icon_rarity_shadow_tier_5_large_selected_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/hud_cards_in_hand_icon_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/hud_cards_in_hand_icon_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/hud_deckinfo_bg_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/icon_dota_logo_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/icon_empty_slot_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/icon_glyph_on_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/icon_outpost_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/icon_star_shadow_hi_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/icon_x_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/icon_x_2_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/invoker_empty1_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/large_ui_arrow_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/levelup_button_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/main_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/mana_bar_back_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/mana_cost_back_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/mana_node_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/mana_node2_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/mask_back_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/mask_big_back_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/mask_count_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/morokai_icon_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/open_deck_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/overtime_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/persona_phantom_assassin_debut_bg_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/portrait_disable_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/portrait_silenced_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/question_mark_icon_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/quick_icon_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/radiant_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/random_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/refresh_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/restore_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/reward_type_gold_coin_icon_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/rewards_bg_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/round_end_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/round_start_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/sub_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/sw_coach_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/sw_default_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/sw_disappointed_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/sw_glossary_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/sw_rage_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/sw_scream_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/sw_shards_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/sw_smiling_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/topbar_heroslant_right_inset_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/topbar_heroslant_right_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/treasure_image_mask_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/tutorial_background_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/victory_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/vs_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/hud/x_close_filled_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_01-0_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_01-1_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_01-2_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_01-3_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_01-4_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_01-5_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_02-1_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_02-2_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_02-3_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_02-4_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_02-5_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_03-1_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_03-2_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_03-3_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_03-4_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_03-5_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_04-1_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_04-2_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_04-3_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_04-4_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_04-5_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_05-1_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_05-2_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_05-3_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_05-4_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_05-5_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_06-1_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_06-2_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_06-3_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_06-4_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_06-5_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_07-1_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_07-2_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_07-3_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_07-4_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_07-5_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_08-1_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_08-2_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_08-3_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_08-4_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_08-5_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_09_glowy-frame_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/badge/avatar_rank_badge_09_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/black_inhand_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/black_tile_full_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/blue_inhand_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/blue_tile_full_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/bonus_room_icon_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/boss_room_icon_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/difficulty_1_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/difficulty_2_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/difficulty_3_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/difficulty_4_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/difficulty_5_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/empty_room_icon_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/encounter_room_elite_icon_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/encounter_room_icon_png_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/full_art/10014_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/full_art/10029_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/full_art/10054_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/full_art/10055_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/full_art/10056_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/full_art/10077_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/full_art/10079_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/full_art/10096_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/full_art/10668_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/full_art/1_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/green_inhand_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/green_tile_full_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/mask3_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/mask4_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/mini_icon/10014_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/mini_icon/10029_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/mini_icon/10054_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/mini_icon/10055_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/mini_icon/10056_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/mini_icon/10077_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/mini_icon/10079_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/mini_icon/10096_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/mini_icon/10668_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/red_inhand_psd_png.vtex");
	background-image: url("s2r://panorama/images/custom_game/player_spell/red_tile_full_psd_png.vtex");
}

@keyframes 'my_test'
{
	0%
	{
		transform: translateX(0);
	}
	
	100%
	{
		transform: translateX(100%);
	}
	
}

