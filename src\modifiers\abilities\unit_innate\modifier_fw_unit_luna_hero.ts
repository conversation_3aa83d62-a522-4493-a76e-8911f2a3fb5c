import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";


export class modifier_fw_unit_luna_hero extends BaseModifier {
    
    IsHidden() {
        return false;
    }
    
    extra_attack:number;
    OnCreated(params: object): void {
        this.extra_attack = this.GetAbility().GetSpecialValueFor("extra_attack")
        if (IsServer()) {
            Timers.CreateTimer(0.3,()=>{
                this.SetStackCount(this.GetParent().cardUsesNum)
            })
        }
    }

    IsDebuff(): boolean {
        return false;
    }

    IsPurgable(): boolean {
        return false;
    }

    IsPurgeException(): boolean {
        return false;
    }

    RemoveOnDeath(): boolean {
        return true
    }
    GetModifierPreAttack_BonusDamage(): number {
        return this.GetStackCount() * this.extra_attack
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            7,
        ];
    }
    
}
