import React, { useEffect, useState } from 'react';
import { render } from 'react-panorama-x';

// 类型定义
interface Card {
  Name: string;
  ChName: string;
  LayCost: number;
  CardType: 'unit' | 'spell';
  CardUIType: 'creep' | 'hero' | 'building' | 'spell';
  Enable: number;
  EnableForDeckCards: number;
  UsingAreaLimit: number;
  UnitCardInfo: {
    Delay: number;
    LayType: string;
    CoverTemplate: number;
    LayGroupNum: string | number;
    Template1: string;
    Num1: number;
    Template2?: string;
    Num2?: number;
  };
  SpellCardInfo: {
    UICardBack?: number;
    SpellTemplate?: string;
  };
  ExtraData: {
    HandCardCooldown?: number[];
    HandCardCost?: number[];
  };
  BotCardAI: {
    CardTag: string;
  };
}

interface GameState {
  currentTurn: number;
  playerMana: number;
  playerHealth: number;
  handCards: Card[];
  selectedCard?: Card;
  hoveredCard?: Card;
  draggedCard?: Card;
}

// 游戏状态管理
class GameStore {
  private state: GameState = {
    currentTurn: 1,
    playerMana: 10,
    playerHealth: 30,
    handCards: [],
    selectedCard: undefined,
    hoveredCard: undefined,
    draggedCard: undefined,
  };

  private listeners: Array<() => void> = [];

  getState(): GameState {
    return { ...this.state };
  }

  setState(updater: (state: GameState) => void): void {
    updater(this.state);
    this.notifyListeners();
  }

  subscribe(listener: () => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener());
  }

  // Actions
  setPlayerMana(mana: number): void {
    this.setState(state => {
      state.playerMana = mana;
    });
  }

  setPlayerHealth(health: number): void {
    this.setState(state => {
      state.playerHealth = health;
    });
  }

  addCardToHand(card: Card): void {
    this.setState(state => {
      state.handCards.push(card);
    });
  }

  removeCardFromHand(cardIndex: number): void {
    this.setState(state => {
      state.handCards.splice(cardIndex, 1);
    });
  }

  playCard(cardIndex: number): void {
    this.setState(state => {
      const card = state.handCards[cardIndex];
      if (card && state.playerMana >= card.LayCost) {
        state.playerMana -= card.LayCost;
        state.handCards.splice(cardIndex, 1);
        // 通知服务器端播放卡牌
        (GameEvents as any).SendCustomGameEventToServer('play_card', {
          cardName: card.Name,
          cardIndex: cardIndex
        });
      }
    });
  }

  selectCard(card: Card | undefined): void {
    this.setState(state => {
      state.selectedCard = card;
    });
  }

  hoverCard(card: Card | undefined): void {
    this.setState(state => {
      state.hoveredCard = card;
    });
  }

  startDragCard(card: Card | undefined): void {
    this.setState(state => {
      state.draggedCard = card;
    });
  }

  endDragCard(): void {
    this.setState(state => {
      state.draggedCard = undefined;
    });
  }

  initializeGame(): void {
    this.setState(state => {
      state.currentTurn = 1;
      state.playerMana = 10;
      state.playerHealth = 30;
      // 添加一些测试卡牌数据
      state.handCards = [
        {
          Name: 'test_unit_1',
          ChName: '测试单位',
          LayCost: 3,
          CardType: 'unit' as const,
          CardUIType: 'creep' as const,
          Enable: 1,
          EnableForDeckCards: 1,
          UsingAreaLimit: 0,
          UnitCardInfo: {
            Delay: 2,
            LayType: 'lay_row',
            CoverTemplate: 0,
            LayGroupNum: 1,
            Template1: 'npc_dota_creep_goodguys_melee',
            Num1: 2,
          },
          SpellCardInfo: {},
          ExtraData: {},
          BotCardAI: { CardTag: 'test' },
        },
        {
          Name: 'test_spell_1',
          ChName: '测试法术',
          LayCost: 5,
          CardType: 'spell' as const,
          CardUIType: 'spell' as const,
          Enable: 1,
          EnableForDeckCards: 1,
          UsingAreaLimit: 0,
          UnitCardInfo: {
            Delay: 0,
            LayType: 'lay_row',
            CoverTemplate: 0,
            LayGroupNum: 1,
            Template1: '',
            Num1: 1,
          },
          SpellCardInfo: {
            SpellTemplate: 'fireball_spell',
          },
          ExtraData: {},
          BotCardAI: { CardTag: 'spell' },
        },
        {
          Name: 'test_hero_1',
          ChName: '测试英雄',
          LayCost: 7,
          CardType: 'unit' as const,
          CardUIType: 'hero' as const,
          Enable: 1,
          EnableForDeckCards: 1,
          UsingAreaLimit: 0,
          UnitCardInfo: {
            Delay: 3,
            LayType: 'lay_row',
            CoverTemplate: 0,
            LayGroupNum: 1,
            Template1: 'npc_dota_hero_pudge',
            Num1: 1,
          },
          SpellCardInfo: {},
          ExtraData: {},
          BotCardAI: { CardTag: 'hero' },
        },
      ];
      state.selectedCard = undefined;
      state.hoveredCard = undefined;
      state.draggedCard = undefined;
    });
  }
}

// 全局游戏状态实例
const gameStore = new GameStore();

// 自定义 Hook 用于订阅状态变化
function useGameStore(): GameState & {
  setPlayerMana: (mana: number) => void;
  setPlayerHealth: (health: number) => void;
  addCardToHand: (card: Card) => void;
  removeCardFromHand: (cardIndex: number) => void;
  playCard: (cardIndex: number) => void;
  selectCard: (card: Card | undefined) => void;
  hoverCard: (card: Card | undefined) => void;
  startDragCard: (card: Card | undefined) => void;
  endDragCard: () => void;
  initializeGame: () => void;
} {
  const [state, setState] = useState(gameStore.getState());

  useEffect(() => {
    const unsubscribe = gameStore.subscribe(() => {
      setState(gameStore.getState());
    });
    return unsubscribe;
  }, []);

  return {
    ...state,
    setPlayerMana: gameStore.setPlayerMana.bind(gameStore),
    setPlayerHealth: gameStore.setPlayerHealth.bind(gameStore),
    addCardToHand: gameStore.addCardToHand.bind(gameStore),
    removeCardFromHand: gameStore.removeCardFromHand.bind(gameStore),
    playCard: gameStore.playCard.bind(gameStore),
    selectCard: gameStore.selectCard.bind(gameStore),
    hoverCard: gameStore.hoverCard.bind(gameStore),
    startDragCard: gameStore.startDragCard.bind(gameStore),
    endDragCard: gameStore.endDragCard.bind(gameStore),
    initializeGame: gameStore.initializeGame.bind(gameStore),
  };
}

// 卡牌组件
interface CardProps {
  card: Card;
  index: number;
  isPlayable: boolean;
  isSelected: boolean;
  isHovered: boolean;
  onPlay: (index: number) => void;
  onSelect: (card: Card) => void;
  onHover: (card: Card | undefined) => void;
  onDragStart: (card: Card) => void;
  onDragEnd: () => void;
}

const CardComponent: React.FC<CardProps> = ({
  card,
  index,
  isPlayable,
  isSelected,
  isHovered,
  onPlay,
  onSelect,
  onHover,
  onDragStart,
  onDragEnd
}) => {
  const handleClick = () => {
    onSelect(card);
    if (isPlayable) {
      onPlay(index);
    }
  };

  const handleMouseEnter = () => {
    onHover(card);
  };

  const handleMouseLeave = () => {
    onHover(undefined);
  };

  const handleDragStart = () => {
    onDragStart(card);
  };

  const handleDragEnd = () => {
    onDragEnd();
  };

  const getCardTypeIcon = () => {
    switch (card.CardUIType) {
      case 'hero': return '👤';
      case 'creep': return '🐾';
      case 'building': return '🏢';
      case 'spell': return '✨';
      default: return '❓';
    }
  };

  const cardClasses = [
    'card',
    `card--${card.CardType}`,
    `card--${card.CardUIType}`,
    isSelected && 'card--selected',
    isHovered && 'card--hovered',
    isPlayable && 'card--playable',
    !isPlayable && 'card--unplayable'
  ].filter(Boolean).join(' ');

  return (
    <Panel
      className={cardClasses}
      onactivate={handleClick}
      onmouseover={handleMouseEnter}
      onmouseout={handleMouseLeave}
      draggable={isPlayable}
    >
      <Panel className="card__header">
        <Label className="card__cost" text={card.LayCost.toString()} />
        <Label className="card__type-icon" text={getCardTypeIcon()} />
      </Panel>

      <Panel className="card__body">
        <Panel className="card__image">
          <Panel className="card__placeholder-image">
            <Label text={getCardTypeIcon()} />
          </Panel>
        </Panel>

        <Panel className="card__info">
          <Label className="card__name" text={card.ChName} />
          <Label
            className="card__description"
            text={
              card.CardType === 'unit'
                ? card.UnitCardInfo.Template1
                : card.SpellCardInfo.SpellTemplate || ''
            }
          />
        </Panel>
      </Panel>

      {card.CardType === 'unit' && (
        <Panel className="card__stats">
          {card.UnitCardInfo.Num1 > 1 && (
            <Label className="card__count" text={`×${card.UnitCardInfo.Num1}`} />
          )}
          {card.UnitCardInfo.Delay && (
            <Label className="card__delay" text={`⏱${card.UnitCardInfo.Delay}s`} />
          )}
        </Panel>
      )}

      <Panel className="card__footer">
        <Label className="card__rarity" text={card.CardUIType} />
      </Panel>
    </Panel>
  );
};

// 主应用组件
const FastWarApp: React.FC = () => {
  const {
    currentTurn,
    playerMana,
    playerHealth,
    handCards,
    selectedCard,
    hoveredCard,
    setPlayerMana,
    setPlayerHealth,
    addCardToHand,
    playCard,
    selectCard,
    hoverCard,
    startDragCard,
    endDragCard,
    initializeGame,
  } = useGameStore();

  useEffect(() => {
    // 初始化游戏
    initializeGame();

    // 监听游戏事件
    const gameEventListeners = [
      (GameEvents as any).Subscribe('player_mana_changed', (event: any) => {
        setPlayerMana(event.mana);
      }),
      (GameEvents as any).Subscribe('player_health_changed', (event: any) => {
        setPlayerHealth(event.health);
      }),
      (GameEvents as any).Subscribe('card_added_to_hand', (event: any) => {
        addCardToHand(event.card);
      }),
    ];

    // 监听 NetTable 变化
    const netTableListener = (CustomNetTables as any).SubscribeNetTableListener('game_state', (tableName: string, key: string, value: any) => {
      if (key === 'player_mana') {
        setPlayerMana(value);
      } else if (key === 'player_health') {
        setPlayerHealth(value);
      }
    });

    // 清理函数
    return () => {
      gameEventListeners.forEach(id => (GameEvents as any).Unsubscribe(id));
      (CustomNetTables as any).UnsubscribeNetTableListener(netTableListener);
    };
  }, []);

  const handleCardPlay = (cardIndex: number) => {
    playCard(cardIndex);
  };

  const handleCardSelect = (card: Card) => {
    selectCard(selectedCard?.Name === card.Name ? undefined : card);
  };

  const handleCardHover = (card: Card | undefined) => {
    hoverCard(card);
  };

  const handleCardDragStart = (card: Card) => {
    startDragCard(card);
  };

  const handleCardDragEnd = () => {
    endDragCard();
  };

  return (
    <Panel className="fast-war-app">
      {/* 游戏信息栏 */}
      <Panel className="game-info">
        <Panel className="player-stats">
          <Panel className="stat">
            <Label className="stat__label" text="回合" />
            <Label className="stat__value" text={currentTurn.toString()} />
          </Panel>
          <Panel className="stat">
            <Label className="stat__label" text="法力" />
            <Label className="stat__value" text={playerMana.toString()} />
          </Panel>
          <Panel className="stat">
            <Label className="stat__label" text="生命" />
            <Label className="stat__value" text={playerHealth.toString()} />
          </Panel>
        </Panel>
      </Panel>

      {/* 战场区域 */}
      <Panel className="battlefield">
        <Panel className="battlefield__content">
          <Label text="战场" />
          <Label text="拖拽卡牌到此处使用" />
        </Panel>
      </Panel>

      {/* 手牌区域 */}
      <Panel className="hand-area">
        <Label text={`手牌 (${handCards.length})`} />
        <Panel className="hand-cards">
          {handCards.map((card, index) => (
            <CardComponent
              key={`${card.Name}-${index}`}
              card={card}
              index={index}
              isPlayable={playerMana >= card.LayCost}
              isSelected={selectedCard?.Name === card.Name}
              isHovered={hoveredCard?.Name === card.Name}
              onPlay={handleCardPlay}
              onSelect={handleCardSelect}
              onHover={handleCardHover}
              onDragStart={handleCardDragStart}
              onDragEnd={handleCardDragEnd}
            />
          ))}
        </Panel>
      </Panel>

      {/* 卡牌详情面板 */}
      {selectedCard && (
        <Panel className="card-detail">
          <Label text="卡牌详情" />
          <Panel className="card-detail__content">
            <Label text={selectedCard.ChName} />
            <Label text={`费用: ${selectedCard.LayCost}`} />
            <Label text={`类型: ${selectedCard.CardUIType}`} />
            <Label text={`内部名称: ${selectedCard.Name}`} />
            {selectedCard.CardType === 'unit' && (
              <>
                <Label text={`模板: ${selectedCard.UnitCardInfo.Template1}`} />
                <Label text={`数量: ${selectedCard.UnitCardInfo.Num1}`} />
                <Label text={`延迟: ${selectedCard.UnitCardInfo.Delay}s`} />
              </>
            )}
            {selectedCard.CardType === 'spell' && (
              <Label text={`法术: ${selectedCard.SpellCardInfo.SpellTemplate || ''}`} />
            )}
          </Panel>
        </Panel>
      )}
    </Panel>
  );
};

// 初始化和渲染
function initializeFastWar() {
  const rootPanel = $.GetContextPanel();

  // 查找目标容器
  let targetPanel = rootPanel.FindChildInLayoutFile('FastWarApp');
  if (!targetPanel) {
    // 如果没有找到指定容器，创建一个
    targetPanel = $.CreatePanel('Panel', rootPanel, 'FastWarApp');
    targetPanel.AddClass('fast-war-app');
  }

  // 渲染 React 应用到 Panorama
  render(<FastWarApp />, targetPanel);

  // 确保面板可见
  targetPanel.SetHasClass('hidden', false);

  console.log('Fast War UI initialized successfully');
}

// 当面板加载完成时初始化
(function() {
  if ($.GetContextPanel()) {
    initializeFastWar();
  } else {
    $.Schedule(0.1, initializeFastWar);
  }
})();
