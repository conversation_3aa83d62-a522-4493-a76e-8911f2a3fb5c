import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";
import { MathUtils } from "../../../utils/math_utils";


export class modifier_fw_hero_spell_lycan extends BaseModifier {

    // GetAttributes(): ModifierAttribute {
    //     return 1 + 4
    // }

    par:ParticleID;
    attack:number;
    OnCreated(keys:any): void {
        let ab = this.GetAbility()
        if (IsServer()) {
            let unit = this.GetParent()
            this.par = ParticleManager.CreateParticle("particles/spell/fw_hero_spell_lycan/buff.vpcf",ParticleAttachment_t.PATTACH_ABSORIGIN, unit)
            ParticleManager.SetParticleControlEnt(this.par, 0, unit, ParticleAttachment_t.PATTACH_POINT_FOLLOW, "attach_hitloc", Vector(0,0,0), true)
        }
        this.attack = ab.GetSpecialValueFor("attack")
    } 
    OnDestroy(): void {
        if (IsServer()) {
            ParticleManager.DestroyParticle(this.par,true)
        }
    }

    IsHidden() {
        return false;
    }

    RemoveOnDeath(): boolean {
        return true
    }

    GetModifierBaseDamageOutgoing_Percentage(): number {
        return this.attack
    }

    DeclareFunctions(): ModifierFunction[] {
        return [
            23,
        ];
    }
    
}
